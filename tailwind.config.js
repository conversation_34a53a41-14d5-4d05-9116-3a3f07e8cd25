/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: "#4D5993", // Deep blue
        secondary: "#73AED2", // Sky blue
        accent: "#E2A453", // Gold
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'gradient-main': 'linear-gradient(to bottom right, #FFFFFF, #73AED2)',
      },
      fontFamily: {
        sans: ['var(--font-inter)'],
        cairo: ['var(--font-cairo)'],
      },
    },
  },
  plugins: [],
}
