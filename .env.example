# Mokhba Wallet Environment Variables
# Copy this file to .env.local and fill in your actual values

# ===================================
# REQUIRED - Application Configuration
# ===================================

# Next.js Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NODE_ENV=development

# ===================================
# REQUIRED - Supabase Configuration
# ===================================

# Supabase URL - Your project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co

# Supabase Anon Key - Public key for client-side operations
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Supabase Service Role Key - Private key for server-side operations (SENSITIVE)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ===================================
# OPTIONAL - External Services
# ===================================

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Error Tracking
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# Email Service (for notifications)
RESEND_API_KEY=re_xxxxxxxxxx

# ===================================
# OPTIONAL - Blockchain Configuration
# ===================================

# RPC Endpoints - Choose one or more providers
# Alchemy (Recommended for production)
NEXT_PUBLIC_ALCHEMY_API_KEY=your-alchemy-api-key

# Infura (Alternative/Backup)
NEXT_PUBLIC_INFURA_API_KEY=your-infura-api-key

# Custom RPC (Optional)
NEXT_PUBLIC_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-key

# Solana
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Wallet Connect
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your-project-id

# ===================================
# OPTIONAL - Development
# ===================================

# Enable debug logging
DEBUG=false

# API Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100

# ===================================
# SETUP INSTRUCTIONS
# ===================================
# 1. Copy this file to .env.local
# 2. Replace placeholder values with your actual credentials
# 3. Never commit .env.local to version control
# 4. For production, set these variables in your hosting platform
# 5. Required variables must be set or the app will fail to start
