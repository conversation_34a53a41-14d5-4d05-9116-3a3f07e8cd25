/** @type {import('next').NextConfig} */

// Validate environment variables at build time
if (process.env.NODE_ENV === 'production') {
  try {
    // Use the validation script instead of importing TypeScript directly
    const { execSync } = require('child_process');
    execSync('node scripts/validate-env.js', { stdio: 'inherit' });
    // Use console.log for build-time messages (not runtime)
    console.log('✅ Environment variables validated at build time');
  } catch (error) {
    // Use console.error for build failures (not runtime)
    console.error('❌ Build failed due to invalid environment variables:', error.message);
    process.exit(1);
  }
}

const nextConfig = {
  reactStrictMode: true,
  
  // TypeScript checking re-enabled - all duplicates fixed!
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // ESLint checking re-enabled
  eslint: {
    ignoreDuringBuilds: false,
  },
  
  // Image Optimization Configuration
  images: {
    // Enable modern formats for better compression
    formats: ['image/avif', 'image/webp'],
    
    // Quality settings (handled at component level in Next.js 13+)
    // quality: 85, // Moved to component level in Next.js 13+
    
    // Device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    
    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // Allowed external image domains
    domains: [
      'cryptologos.cc',
      'upload.wikimedia.org',
      'cdn.jsdelivr.net',
      'raw.githubusercontent.com',
      'assets.coingecko.com',
      'logos.covalenthq.com',
      'tokens.1inch.io',
      'wallet-asset.matic.network',
      'ethereum-optimism.github.io',
      'bridge.arbitrum.io',
      'zapper.fi',
      'defipulse.com'
    ],
    
    // Remote patterns for more flexible domain matching
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**.wikimedia.org',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cryptologos.cc',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**.coingecko.com',
        port: '',
        pathname: '/**',
      }
    ],
    
    // Enable blur placeholders (handled at component level)
    // placeholder: 'blur', // Moved to component level
    
    // Enable dangerous use of SVG (for trusted sources only)
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    
    // Loader configuration for custom image optimization
    loader: 'default',
    
    // Disable static imports optimization for better control
    disableStaticImages: false,
    
    // Minimize automatic static optimization for images
    minimumCacheTTL: 60 * 60 * 24 * 7, // 7 days
  },
  
  // Security Headers Configuration
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          // Content Security Policy - Restrict resource loading to prevent XSS
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live https://fonts.googleapis.com https://fonts.gstatic.com https://www.google.com https://www.gstatic.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com",
              "img-src 'self' data: https: blob:",
              "font-src 'self' https://fonts.gstatic.com data:",
              "connect-src 'self' https://*.supabase.co https://supabase.com wss://*.supabase.co https://api.coingecko.com https://api.coinbase.com https://mainnet.infura.io https://polygon-rpc.com https://arbitrum-mainnet.infura.io https://optimism-mainnet.infura.io https://base-mainnet.g.alchemy.com https://www.google.com https://www.gstatic.com https://metamask-sdk.api.cx.metamask.io",
              "frame-src 'self' https://www.google.com https://www.gstatic.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; ')
          },
          
          // Strict Transport Security - Enforce HTTPS
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          
          // X-Frame-Options - Prevent clickjacking
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          
          // X-Content-Type-Options - Prevent MIME type sniffing
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          
          // Referrer Policy - Control referrer information
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          
          // Permissions Policy - Restrict browser features
          {
            key: 'Permissions-Policy',
            value: [
              'camera=()',
              'microphone=()',
              'geolocation=()',
              'interest-cohort=()',
              'payment=(self)',
              'usb=()',
              'serial=()',
              'bluetooth=()',
              'magnetometer=()',
              'gyroscope=()',
              'accelerometer=()'
            ].join(', ')
          },
          
          // X-DNS-Prefetch-Control - Control DNS prefetching
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          
          // X-XSS-Protection - Legacy XSS protection (for older browsers)
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          
          // Cross-Origin-Embedder-Policy - Enable cross-origin isolation
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'credentialless'
          },
          
          // Cross-Origin-Opener-Policy - Isolate browsing context
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin'
          },
          
          // Cross-Origin-Resource-Policy - Control cross-origin resource sharing
          {
            key: 'Cross-Origin-Resource-Policy',
            value: 'same-origin'
          }
        ]
      }
    ];
  },
  
  webpack: (config, { webpack, isServer }) => {
    // Fix for Solana libraries and crypto dependencies
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
      stream: false,
      url: false,
      zlib: false,
      http: false,
      https: false,
      assert: false,
      os: false,
      path: false,
      buffer: require.resolve('buffer'),
    };

    // Add buffer polyfill for Solana and wagmi
    config.plugins = [
      ...config.plugins,
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
      }),
    ];

    // Optimize Solana modules
    config.module.rules.push({
      test: /\.m?js$/,
      include: /node_modules\/@solana/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    // Optimize wagmi/viem modules to prevent vendor chunk issues
    config.module.rules.push({
      test: /\.m?js$/,
      include: /node_modules\/(wagmi|viem|@wagmi)/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    // Handle server-side externals
    if (isServer) {
      config.externals = [...(config.externals || []), {
        '@solana/web3.js': 'commonjs @solana/web3.js',
        '@solana/wallet-adapter-base': 'commonjs @solana/wallet-adapter-base',
        '@solana/wallet-adapter-react': 'commonjs @solana/wallet-adapter-react',
        'wagmi': 'commonjs wagmi',
        'viem': 'commonjs viem',
        '@wagmi/core': 'commonjs @wagmi/core',
      }];
    }

    // Prevent webpack from trying to bundle certain node_modules
    config.resolve.alias = {
      ...config.resolve.alias,
    };

    // Use default chunk splitting to avoid vendor chunk issues
    // Remove conflicting optimization settings that cause webpack errors
    // config.optimization = {
    //   ...config.optimization,
    //   usedExports: true,
    // };

    return config;
  },
};

module.exports = nextConfig;
