#!/usr/bin/env node

/**
 * Environment Variable Validation Script
 * 
 * This script validates all required environment variables for the Mokhba Wallet project.
 * It can be used in CI/CD pipelines, deployment processes, and local development.
 * 
 * Usage:
 *   node scripts/validate-env.js
 *   npm run validate-env
 * 
 * Exit codes:
 *   0 - All validations passed
 *   1 - Validation failed
 */

const path = require('path');

// Set up the environment for validation
process.env.NODE_ENV = process.env.NODE_ENV || 'production';

console.log('🔍 Validating environment variables for Mokhba Wallet...');
console.log(`Environment: ${process.env.NODE_ENV}`);
console.log('────────────────────────────────────────────────────────');

try {
  // Try to import the validation module (handle both dev and build contexts)
  let validateEnvironment;
  
  try {
    // Try compiled version first (for production builds)
    const compiled = require('../.next/server/chunks/ssr/src_lib_env-validation_ts.js');
    validateEnvironment = compiled.validateEnvironment;
  } catch (e) {
    try {
      // Try direct import (for development)
      const { validateEnvironment: devValidate } = require('../src/lib/env-validation.ts');
      validateEnvironment = devValidate;
    } catch (e2) {
      // Manual validation as fallback
      console.log('⚠️ Using manual validation (TypeScript modules not available)');
      validateEnvironment = manualValidation;
    }
  }
  
  // Run validation
  validateEnvironment();
  
  console.log('────────────────────────────────────────────────────────');
  console.log('✅ Environment validation completed successfully!');
  console.log('🚀 Application is ready for deployment');
  
  // Additional validation checks
  performAdditionalChecks();
  
  process.exit(0);
  
} catch (error) {
  console.log('────────────────────────────────────────────────────────');
  console.error('❌ Environment validation failed!');
  console.error('');
  console.error('Error details:');
  console.error(error.message);
  console.error('');
  
  // Provide helpful guidance
  provideGuidance();
  
  process.exit(1);
}

/**
 * Perform additional validation checks
 */
function performAdditionalChecks() {
  console.log('');
  console.log('🔧 Additional validation checks:');
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    console.log(`✅ Node.js version: ${nodeVersion} (supported)`);
  } else {
    console.log(`⚠️ Node.js version: ${nodeVersion} (recommend 18+)`);
  }
  
  // Check environment-specific requirements
  const env = process.env.NODE_ENV;
  
  if (env === 'production') {
    checkProductionRequirements();
  } else if (env === 'staging') {
    checkStagingRequirements();
  } else {
    checkDevelopmentRequirements();
  }
}

/**
 * Check production-specific requirements
 */
function checkProductionRequirements() {
  console.log('');
  console.log('🏭 Production environment checks:');
  
  const checks = [
    {
      name: 'Session Secret',
      check: () => process.env.SESSION_SECRET && process.env.SESSION_SECRET.length >= 32,
      message: 'SESSION_SECRET should be at least 32 characters for security'
    },
    {
      name: 'CORS Origins',
      check: () => process.env.ALLOWED_ORIGINS && process.env.ALLOWED_ORIGINS.length > 0,
      message: 'ALLOWED_ORIGINS should be set for production security'
    },
    {
      name: 'Supabase URL Format',
      check: () => {
        const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
        return url && (url.includes('supabase.co') || url.includes('supabase.com'));
      },
      message: 'Supabase URL should be a valid Supabase domain'
    }
  ];
  
  checks.forEach(({ name, check, message }) => {
    if (check()) {
      console.log(`✅ ${name}`);
    } else {
      console.log(`⚠️ ${name}: ${message}`);
    }
  });
}

/**
 * Check staging-specific requirements
 */
function checkStagingRequirements() {
  console.log('');
  console.log('🧪 Staging environment checks:');
  
  if (process.env.ALLOWED_ORIGINS) {
    console.log('✅ CORS origins configured for staging');
  } else {
    console.log('⚠️ Consider setting ALLOWED_ORIGINS for staging environment');
  }
}

/**
 * Check development-specific requirements
 */
function checkDevelopmentRequirements() {
  console.log('');
  console.log('🛠️ Development environment checks:');
  
  if (process.env.DEBUG === 'true') {
    console.log('✅ Debug logging enabled');
  } else {
    console.log('💡 Tip: Set DEBUG=true for verbose logging in development');
  }
}

/**
 * Provide helpful guidance for fixing issues
 */
function provideGuidance() {
  console.log('📋 Quick Setup Guide:');
  console.log('');
  console.log('1. Copy the example environment file:');
  console.log('   cp .env.example .env.local');
  console.log('');
  console.log('2. Get your Supabase credentials:');
  console.log('   - Visit https://supabase.com');
  console.log('   - Go to your project → Settings → API');
  console.log('   - Copy the Project URL and API keys');
  console.log('');
  console.log('3. Fill in your .env.local file with real values');
  console.log('');
  console.log('4. For production deployment:');
  console.log('   - Set all environment variables in your hosting platform');
  console.log('   - Ensure NODE_ENV=production');
  console.log('   - Generate a secure SESSION_SECRET');
  console.log('');
  console.log('📚 Documentation:');
  console.log('   - README.md - Complete setup instructions');
  console.log('   - docs/ci-cd-recommendations.md - CI/CD configuration');
  console.log('');
  console.log('🔍 Health Check:');
  console.log('   - After fixing, test with: curl http://localhost:3000/api/health');
}

// Handle unhandled errors gracefully
process.on('uncaughtException', (error) => {
  console.error('');
  console.error('💥 Unexpected error during validation:');
  console.error(error.message);
  console.error('');
  console.error('This might indicate a configuration or code issue.');
  console.error('Please review your setup and try again.');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('');
  console.error('💥 Unhandled promise rejection during validation:');
  console.error(reason);
  process.exit(1);
});

/**
 * Manual validation function as fallback when TypeScript modules aren't available
 */
function manualValidation() {
  console.log('🔧 Running manual environment validation...');
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = [];
  const invalid = [];
  
  for (const varName of requiredVars) {
    const value = process.env[varName];
    
    if (!value) {
      missing.push(varName);
    } else if (varName === 'NEXT_PUBLIC_SUPABASE_URL') {
      // Validate URL format
      try {
        new URL(value);
        if (!value.includes('supabase') && !value.includes('localhost')) {
          invalid.push(`${varName}: Should be a Supabase URL`);
        }
      } catch (e) {
        invalid.push(`${varName}: Invalid URL format`);
      }
    } else if (varName.includes('KEY') && value.length < 20) {
      invalid.push(`${varName}: Key appears to be too short`);
    }
  }
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  if (invalid.length > 0) {
    throw new Error(`Invalid environment variables: ${invalid.join(', ')}`);
  }
  
  console.log('✅ Manual validation completed - all required variables are set');
} 