#!/usr/bin/env node

/**
 * Test Add Wallet Functionality
 * Tests the wallet addition feature to ensure it works correctly
 */

const { walletManager } = require('../src/lib/walletManager');

async function testAddWalletFunctionality() {
  console.log('🧪 Testing Add Wallet Functionality\n');

  // Clear existing wallets for clean test
  walletManager.clearAllWallets();
  console.log('✅ Cleared existing wallets for clean test');

  // Test 1: Add Ethereum wallet
  console.log('\n📝 Test 1: Adding Ethereum wallet');
  try {
    const ethWallet = walletManager.addWallet({
      name: 'Test Ethereum Wallet',
      address: '******************************************',
      provider: 'metamask',
      chainType: 'ethereum',
      isConnected: false,
      isDefault: true
    });
    
    console.log('✅ Ethereum wallet added successfully');
    console.log(`   ID: ${ethWallet.id}`);
    console.log(`   Name: ${ethWallet.name}`);
    console.log(`   Address: ${ethWallet.address}`);
    console.log(`   Chain: ${ethWallet.chainType}`);
    console.log(`   Default: ${ethWallet.isDefault}`);
  } catch (error) {
    console.log('❌ Failed to add Ethereum wallet:', error.message);
  }

  // Test 2: Add Solana wallet
  console.log('\n📝 Test 2: Adding Solana wallet');
  try {
    const solWallet = walletManager.addWallet({
      name: 'Test Solana Wallet',
      address: 'DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1',
      provider: 'phantom',
      chainType: 'solana',
      isConnected: false,
      isDefault: true
    });
    
    console.log('✅ Solana wallet added successfully');
    console.log(`   ID: ${solWallet.id}`);
    console.log(`   Name: ${solWallet.name}`);
    console.log(`   Address: ${solWallet.address}`);
    console.log(`   Chain: ${solWallet.chainType}`);
    console.log(`   Default: ${solWallet.isDefault}`);
  } catch (error) {
    console.log('❌ Failed to add Solana wallet:', error.message);
  }

  // Test 3: Add duplicate wallet (should fail)
  console.log('\n📝 Test 3: Adding duplicate wallet (should detect existing)');
  try {
    const duplicateWallet = walletManager.addWallet({
      name: 'Duplicate Ethereum Wallet',
      address: '******************************************', // Same as first
      provider: 'metamask',
      chainType: 'ethereum',
      isConnected: false
    });
    
    console.log('⚠️  Duplicate wallet was added (this might be expected behavior)');
    console.log(`   ID: ${duplicateWallet.id}`);
  } catch (error) {
    console.log('✅ Duplicate wallet correctly rejected:', error.message);
  }

  // Test 4: Get wallets by chain
  console.log('\n📝 Test 4: Retrieving wallets by chain');
  const ethWallets = walletManager.getWalletsByChain('ethereum');
  const solWallets = walletManager.getWalletsByChain('solana');
  
  console.log(`✅ Ethereum wallets: ${ethWallets.length}`);
  ethWallets.forEach((wallet, index) => {
    console.log(`   ${index + 1}. ${wallet.name} (${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)})`);
  });
  
  console.log(`✅ Solana wallets: ${solWallets.length}`);
  solWallets.forEach((wallet, index) => {
    console.log(`   ${index + 1}. ${wallet.name} (${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)})`);
  });

  // Test 5: Get grouped wallets
  console.log('\n📝 Test 5: Getting grouped wallets');
  const walletGroups = walletManager.getWalletsGrouped();
  
  console.log(`✅ Wallet groups: ${walletGroups.length}`);
  walletGroups.forEach((group) => {
    console.log(`   ${group.chainType}: ${group.wallets.length} wallets`);
    group.wallets.forEach((wallet, index) => {
      console.log(`     ${index + 1}. ${wallet.name} ${wallet.isDefault ? '(Default)' : ''}`);
    });
  });

  // Test 6: Address validation
  console.log('\n📝 Test 6: Address validation tests');
  
  const testAddresses = [
    { address: '******************************************', chain: 'ethereum', valid: true },
    { address: '0xinvalid', chain: 'ethereum', valid: false },
    { address: 'DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1', chain: 'solana', valid: true },
    { address: 'invalid-solana', chain: 'solana', valid: false },
  ];

  testAddresses.forEach((test) => {
    let isValid = false;
    
    if (test.chain === 'ethereum') {
      isValid = /^0x[a-fA-F0-9]{40}$/.test(test.address);
    } else if (test.chain === 'solana') {
      isValid = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(test.address);
    }
    
    const result = isValid === test.valid ? '✅' : '❌';
    console.log(`   ${result} ${test.chain} address "${test.address.slice(0, 10)}..." - Expected: ${test.valid ? 'valid' : 'invalid'}, Got: ${isValid ? 'valid' : 'invalid'}`);
  });

  // Test 7: Wallet export/import
  console.log('\n📝 Test 7: Wallet export/import functionality');
  try {
    const exportedData = walletManager.exportWallets();
    console.log('✅ Wallets exported successfully');
    console.log(`   Export size: ${exportedData.length} characters`);
    
    // Clear and import
    walletManager.clearAllWallets();
    const importSuccess = walletManager.importWallets(exportedData);
    
    if (importSuccess) {
      console.log('✅ Wallets imported successfully');
      const importedWallets = walletManager.getAllWallets();
      console.log(`   Imported wallets: ${importedWallets.length}`);
    } else {
      console.log('❌ Failed to import wallets');
    }
  } catch (error) {
    console.log('❌ Export/import test failed:', error.message);
  }

  // Summary
  console.log('\n📊 Test Summary:');
  const allWallets = walletManager.getAllWallets();
  console.log(`Total wallets in system: ${allWallets.length}`);
  
  const chainCounts = {};
  allWallets.forEach(wallet => {
    chainCounts[wallet.chainType] = (chainCounts[wallet.chainType] || 0) + 1;
  });
  
  Object.entries(chainCounts).forEach(([chain, count]) => {
    console.log(`${chain}: ${count} wallets`);
  });

  console.log('\n🎉 Add Wallet Functionality Test Complete!');
  console.log('\n📝 Manual Testing Instructions:');
  console.log('1. Open http://localhost:3000/en/app/move-crypto/transfer');
  console.log('2. Click "Select destination wallet"');
  console.log('3. Click "Add New Wallet" button');
  console.log('4. Fill in wallet details:');
  console.log('   - Name: "My Test Wallet"');
  console.log('   - Network: Select Ethereum');
  console.log('   - Address: ******************************************');
  console.log('   - Provider: MetaMask');
  console.log('5. Click "Add Wallet"');
  console.log('6. Verify wallet appears in the list');
  console.log('7. Test with invalid address to see validation');

  return true;
}

// Run the test
if (require.main === module) {
  testAddWalletFunctionality()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testAddWalletFunctionality };
