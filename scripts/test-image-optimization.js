#!/usr/bin/env node

/**
 * Image Optimization Testing Script for Mokhba Wallet
 * Tests image performance, formats, lazy loading, and responsiveness
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Utility functions
function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

function logSubHeader(message) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`${message}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

function addResult(test, status, message) {
  testResults.details.push({ test, status, message });
  if (status === 'pass') testResults.passed++;
  else if (status === 'fail') testResults.failed++;
  else if (status === 'warning') testResults.warnings++;
}

// Test Next.js Image Configuration
function testNextJSConfig() {
  logSubHeader('Testing Next.js Image Configuration');
  
  try {
    const configPath = path.join(process.cwd(), 'next.config.js');
    if (!fs.existsSync(configPath)) {
      addResult('Next.js Config', 'fail', 'next.config.js not found');
      logError('next.config.js not found');
      return;
    }

    const configContent = fs.readFileSync(configPath, 'utf8');
    
    const checks = [
      { name: 'Image formats configured', pattern: /formats.*\[.*['"`]image\/(avif|webp)['"`].*\]/ },
      { name: 'Quality setting configured', pattern: /quality.*:.*\d+/ },
      { name: 'Device sizes configured', pattern: /deviceSizes.*:.*\[/ },
      { name: 'Remote patterns configured', pattern: /remotePatterns.*:.*\[/ }
    ];

    checks.forEach(check => {
      if (check.pattern.test(configContent)) {
        addResult('Next.js Config', 'pass', check.name);
        logSuccess(check.name);
      } else {
        addResult('Next.js Config', 'warning', `${check.name} not found`);
        logWarning(`${check.name} not found`);
      }
    });

  } catch (error) {
    addResult('Next.js Config', 'fail', `Error reading config: ${error.message}`);
    logError(`Error reading config: ${error.message}`);
  }
}

// Test Image Assets
function testImageAssets() {
  logSubHeader('Testing Image Assets');
  
  const publicDir = path.join(process.cwd(), 'public');
  const requiredImages = [
    'logo.svg',
    'favicon.svg',
    'favicon.ico',
    'images/social/og-image.svg'
  ];

  requiredImages.forEach(imagePath => {
    const fullPath = path.join(publicDir, imagePath);
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      addResult('Image Assets', 'pass', `${imagePath} exists (${sizeKB} KB)`);
      logSuccess(`${imagePath} exists (${sizeKB} KB)`);
      
      if (stats.size > 500000) {
        addResult('Image Assets', 'warning', `${imagePath} is large (${sizeKB} KB)`);
        logWarning(`${imagePath} is large (${sizeKB} KB) - consider optimization`);
      }
    } else {
      addResult('Image Assets', 'fail', `${imagePath} missing`);
      logError(`${imagePath} missing`);
    }
  });
}

// Main test runner
async function runImageOptimizationTests() {
  logHeader('Mokhba Wallet - Image Optimization Test Suite');
  
  log('Starting image optimization tests...', 'cyan');
  
  testNextJSConfig();
  testImageAssets();
  
  logHeader('Test Results Summary');
  
  log(`Total Tests: ${testResults.passed + testResults.failed + testResults.warnings}`, 'white');
  log(`✅ Passed: ${testResults.passed}`, 'green');
  log(`❌ Failed: ${testResults.failed}`, 'red');
  log(`⚠️  Warnings: ${testResults.warnings}`, 'yellow');
  
  const totalTests = testResults.passed + testResults.failed + testResults.warnings;
  const score = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
  
  log(`\nOptimization Score: ${score}%`, score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red');
  
  if (testResults.failed > 0) {
    log('\n🚨 Critical issues found! Please address failed tests.', 'red');
  } else if (testResults.warnings > 0) {
    log('\n⚠️  Some optimizations recommended. See warnings above.', 'yellow');
  } else {
    log('\n🎉 All image optimization tests passed!', 'green');
  }
}

if (require.main === module) {
  runImageOptimizationTests().catch(error => {
    logError(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runImageOptimizationTests, testResults };
