#!/usr/bin/env node

/**
 * Test RPC Connection Script
 * Tests various RPC endpoints to ensure wallet balance fetching works
 */

const https = require('https');
const http = require('http');

// Test address (Ethereum Foundation)
const TEST_ADDRESS = '******************************************';

// RPC endpoints to test
const RPC_ENDPOINTS = [
  {
    name: 'Ethereum Public Node',
    url: 'https://ethereum.publicnode.com',
    type: 'public'
  },
  {
    name: 'Ankr Ethereum',
    url: 'https://rpc.ankr.com/eth',
    type: 'public'
  },
  {
    name: 'LlamaRPC Ethereum',
    url: 'https://eth.llamarpc.com',
    type: 'public'
  }
];

// Add Alchemy if API key is provided
if (process.env.NEXT_PUBLIC_ALCHEMY_API_KEY) {
  RPC_ENDPOINTS.unshift({
    name: 'Alchemy',
    url: `https://eth-mainnet.g.alchemy.com/v2/${process.env.NEXT_PUBLIC_ALCHEMY_API_KEY}`,
    type: 'private'
  });
}

// Add Infura if API key is provided
if (process.env.NEXT_PUBLIC_INFURA_API_KEY) {
  RPC_ENDPOINTS.unshift({
    name: 'Infura',
    url: `https://mainnet.infura.io/v3/${process.env.NEXT_PUBLIC_INFURA_API_KEY}`,
    type: 'private'
  });
}

function makeRPCRequest(endpoint, address) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      jsonrpc: '2.0',
      method: 'eth_getBalance',
      params: [address, 'latest'],
      id: 1
    });

    const url = new URL(endpoint.url);
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Mokhba-Wallet-Test/1.0'
      },
      timeout: 10000
    };

    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.error) {
            reject(new Error(`RPC Error: ${response.error.message}`));
          } else {
            const balanceWei = BigInt(response.result);
            const balanceEth = Number(balanceWei) / 1e18;
            resolve({
              balanceWei: response.result,
              balanceEth: balanceEth.toFixed(6),
              statusCode: res.statusCode
            });
          }
        } catch (error) {
          reject(new Error(`Parse Error: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Network Error: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function testRPCEndpoints() {
  console.log('🔍 Testing RPC Endpoints for Wallet Balance Fetching\n');
  console.log(`Test Address: ${TEST_ADDRESS}\n`);

  const results = [];

  for (const endpoint of RPC_ENDPOINTS) {
    process.stdout.write(`Testing ${endpoint.name} (${endpoint.type})... `);
    
    try {
      const startTime = Date.now();
      const result = await makeRPCRequest(endpoint, TEST_ADDRESS);
      const duration = Date.now() - startTime;
      
      console.log(`✅ Success (${duration}ms)`);
      console.log(`   Balance: ${result.balanceEth} ETH`);
      console.log(`   Status: ${result.statusCode}\n`);
      
      results.push({
        endpoint: endpoint.name,
        status: 'success',
        duration,
        balance: result.balanceEth
      });
    } catch (error) {
      console.log(`❌ Failed`);
      console.log(`   Error: ${error.message}\n`);
      
      results.push({
        endpoint: endpoint.name,
        status: 'failed',
        error: error.message
      });
    }
  }

  // Summary
  console.log('📊 Summary:');
  const successful = results.filter(r => r.status === 'success');
  const failed = results.filter(r => r.status === 'failed');
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}\n`);

  if (successful.length > 0) {
    console.log('🎉 At least one RPC endpoint is working!');
    console.log('Your wallet balance fetching should work.');
  } else {
    console.log('⚠️  All RPC endpoints failed!');
    console.log('This explains why balance fetching is not working.');
    console.log('\nRecommended actions:');
    console.log('1. Check your internet connection');
    console.log('2. Add NEXT_PUBLIC_ALCHEMY_API_KEY to your .env.local');
    console.log('3. Add NEXT_PUBLIC_INFURA_API_KEY to your .env.local');
    console.log('4. Verify firewall/proxy settings');
  }

  return successful.length > 0;
}

// Run the test
if (require.main === module) {
  testRPCEndpoints()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testRPCEndpoints };
