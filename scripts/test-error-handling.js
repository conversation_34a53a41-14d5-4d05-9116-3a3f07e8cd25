#!/usr/bin/env node

/**
 * Error Handling Testing Script
 * Comprehensive tests for the Mokhba Wallet error handling system
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_ENDPOINTS = [
  '/api/health',
  '/api/card-waitlist',
  '/api/support-tickets',
  '/api/feature-requests',
  '/api/status-subscriptions',
  '/api/auth/magic-link'
];

// Test scenarios
const ERROR_SCENARIOS = {
  networkTimeout: {
    name: 'Network Timeout',
    description: 'Test timeout handling and retries',
    timeout: 100 // Very short timeout to force failure
  },
  invalidPayload: {
    name: 'Invalid Payload',
    description: 'Test validation error handling',
    payload: { invalid: 'data' }
  },
  rateLimitExceeded: {
    name: 'Rate Limit Exceeded',
    description: 'Test rate limiting error handling',
    requests: 15 // Exceed the rate limit
  },
  authenticationFailure: {
    name: 'Authentication Failure',
    description: 'Test auth error handling',
    invalidAuth: true
  },
  serverError: {
    name: 'Server Error Simulation',
    description: 'Test server error handling'
  }
};

// Test results tracker
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}]`;
  
  switch (type) {
    case 'success':
      console.log(colors.green(`${prefix} ✅ ${message}`));
      break;
    case 'error':
      console.log(colors.red(`${prefix} ❌ ${message}`));
      break;
    case 'warning':
      console.log(colors.yellow(`${prefix} ⚠️  ${message}`));
      break;
    case 'info':
    default:
      console.log(colors.blue(`${prefix} ℹ️  ${message}`));
      break;
  }
}

function logSection(title) {
  console.log(colors.cyan(`\n${'='.repeat(60)}`));
  console.log(colors.cyan(`${title.toUpperCase()}`));
  console.log(colors.cyan(`${'='.repeat(60)}\n`));
}

function recordTest(testName, passed, error = null) {
  if (passed) {
    testResults.passed++;
    log(`${testName}: PASSED`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push({ test: testName, error });
    log(`${testName}: FAILED - ${error}`, 'error');
  }
}

// Test functions
async function testHealthEndpoint() {
  logSection('Health Endpoint Tests');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/health`, { timeout: 5000 });
    
    if (response.status === 200) {
      recordTest('Health Check - Basic', true);
      log(`Health status: ${response.data.status}`, 'info');
    } else {
      recordTest('Health Check - Basic', false, `Unexpected status: ${response.status}`);
    }
  } catch (error) {
    recordTest('Health Check - Basic', false, error.message);
  }
}

async function testNetworkTimeout() {
  logSection('Network Timeout Tests');
  
  try {
    await axios.get(`${BASE_URL}/api/health`, { timeout: 1 });
    recordTest('Network Timeout', false, 'Expected timeout but request succeeded');
  } catch (error) {
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      recordTest('Network Timeout', true);
      log('Timeout error correctly handled', 'info');
    } else {
      recordTest('Network Timeout', false, `Unexpected error: ${error.message}`);
    }
  }
}

async function testRateLimiting() {
  logSection('Rate Limiting Tests');
  
  const testData = {
    full_name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+**********',
    country: 'US'
  };

  let successfulRequests = 0;
  let rateLimitedRequests = 0;

  // Send multiple requests rapidly
  const promises = Array.from({ length: 15 }, async (_, i) => {
    try {
      const response = await axios.post(`${BASE_URL}/api/card-waitlist`, testData, {
        timeout: 5000
      });
      
      if (response.status === 201 || response.status === 409) {
        successfulRequests++;
      }
    } catch (error) {
      if (error.response?.status === 429) {
        rateLimitedRequests++;
      } else {
        log(`Unexpected error on request ${i + 1}: ${error.message}`, 'warning');
      }
    }
  });

  await Promise.all(promises);

  if (rateLimitedRequests > 0) {
    recordTest('Rate Limiting - Protection Active', true);
    log(`Rate limited ${rateLimitedRequests} requests after ${successfulRequests} successful ones`, 'info');
  } else {
    recordTest('Rate Limiting - Protection Active', false, 'No rate limiting detected');
  }

  // Test rate limit headers
  try {
    const response = await axios.post(`${BASE_URL}/api/card-waitlist`, testData);
    const headers = response.headers;
    
    if (headers['x-ratelimit-limit'] && headers['x-ratelimit-remaining']) {
      recordTest('Rate Limiting - Headers Present', true);
      log(`Rate limit headers: Limit=${headers['x-ratelimit-limit']}, Remaining=${headers['x-ratelimit-remaining']}`, 'info');
    } else {
      recordTest('Rate Limiting - Headers Present', false, 'Rate limit headers missing');
    }
  } catch (error) {
    if (error.response?.status === 429) {
      const headers = error.response.headers;
      if (headers['x-ratelimit-limit']) {
        recordTest('Rate Limiting - Headers Present', true);
        log('Rate limit headers present on 429 response', 'info');
      } else {
        recordTest('Rate Limiting - Headers Present', false, 'Rate limit headers missing on 429');
      }
    }
  }
}

async function testValidationErrors() {
  logSection('Validation Error Tests');
  
  // Test missing required fields
  try {
    await axios.post(`${BASE_URL}/api/card-waitlist`, {});
    recordTest('Validation - Missing Fields', false, 'Expected validation error but request succeeded');
  } catch (error) {
    if (error.response?.status === 400) {
      recordTest('Validation - Missing Fields', true);
      log(`Validation error correctly returned: ${error.response.data.error}`, 'info');
    } else {
      recordTest('Validation - Missing Fields', false, `Unexpected status: ${error.response?.status}`);
    }
  }

  // Test invalid email format
  try {
    await axios.post(`${BASE_URL}/api/support-tickets`, {
      name: 'Test User',
      email: 'invalid-email',
      subject: 'Test',
      message: 'Test message'
    });
    recordTest('Validation - Invalid Email', false, 'Expected validation error but request succeeded');
  } catch (error) {
    if (error.response?.status === 400 && error.response.data.error.includes('email')) {
      recordTest('Validation - Invalid Email', true);
      log('Email validation working correctly', 'info');
    } else {
      recordTest('Validation - Invalid Email', false, `Unexpected response: ${error.response?.data}`);
    }
  }
}

async function testAuthenticationErrors() {
  logSection('Authentication Error Tests');
  
  // Test endpoints that require authentication
  const protectedEndpoints = [
    '/api/card-waitlist?admin=true',
    '/api/support-tickets?admin=true',
    '/api/feature-requests?admin=true'
  ];

  for (const endpoint of protectedEndpoints) {
    try {
      await axios.get(`${BASE_URL}${endpoint}`);
      recordTest(`Auth - ${endpoint}`, false, 'Expected auth error but request succeeded');
    } catch (error) {
      if (error.response?.status === 401) {
        recordTest(`Auth - ${endpoint}`, true);
      } else {
        recordTest(`Auth - ${endpoint}`, false, `Unexpected status: ${error.response?.status}`);
      }
    }
  }

  // Test with invalid auth token
  try {
    await axios.get(`${BASE_URL}/api/card-waitlist`, {
      headers: { Authorization: 'Bearer invalid-token' }
    });
    recordTest('Auth - Invalid Token', false, 'Expected auth error but request succeeded');
  } catch (error) {
    if (error.response?.status === 401) {
      recordTest('Auth - Invalid Token', true);
      log('Invalid token correctly rejected', 'info');
    } else {
      recordTest('Auth - Invalid Token', false, `Unexpected status: ${error.response?.status}`);
    }
  }
}

async function testCaptchaHandling() {
  logSection('CAPTCHA Error Tests');
  
  // Note: These tests will only be meaningful in production mode
  // In development, CAPTCHA is bypassed
  if (process.env.NODE_ENV === 'development') {
    log('CAPTCHA tests skipped in development mode', 'warning');
    return;
  }

  const testData = {
    full_name: 'Test User',
    email: '<EMAIL>',
    phone_number: '+**********',
    country: 'US'
    // Missing captchaToken intentionally
  };

  try {
    await axios.post(`${BASE_URL}/api/card-waitlist`, testData);
    recordTest('CAPTCHA - Missing Token', false, 'Expected CAPTCHA error but request succeeded');
  } catch (error) {
    if (error.response?.status === 400 && error.response.data.error.includes('CAPTCHA')) {
      recordTest('CAPTCHA - Missing Token', true);
      log('CAPTCHA validation working correctly', 'info');
    } else {
      recordTest('CAPTCHA - Missing Token', false, `Unexpected response: ${error.response?.data}`);
    }
  }
}

async function testErrorRecovery() {
  logSection('Error Recovery Tests');
  
  // Test circuit breaker behavior (simplified)
  let consecutiveFailures = 0;
  const maxFailures = 5;

  for (let i = 0; i < maxFailures + 2; i++) {
    try {
      await axios.get(`${BASE_URL}/api/nonexistent-endpoint`, { timeout: 1000 });
      break; // Success, stop testing
    } catch (error) {
      consecutiveFailures++;
      log(`Failure ${consecutiveFailures}: ${error.response?.status || error.code}`, 'warning');
    }
  }

  if (consecutiveFailures >= maxFailures) {
    recordTest('Error Recovery - Failure Detection', true);
    log('Multiple consecutive failures detected as expected', 'info');
  } else {
    recordTest('Error Recovery - Failure Detection', false, 'Not enough failures detected');
  }
}

async function testEndpointAvailability() {
  logSection('Endpoint Availability Tests');
  
  for (const endpoint of TEST_ENDPOINTS) {
    try {
      const response = await axios.get(`${BASE_URL}${endpoint}`, { timeout: 5000 });
      
      if (endpoint === '/api/health') {
        // Health endpoint should always be accessible
        recordTest(`Availability - ${endpoint}`, response.status === 200);
      } else {
        // Other endpoints might require auth or have different responses
        recordTest(`Availability - ${endpoint}`, response.status < 500);
      }
    } catch (error) {
      const status = error.response?.status;
      
      // 4xx errors are acceptable (auth required, validation, etc.)
      // 5xx errors indicate server problems
      if (status && status < 500) {
        recordTest(`Availability - ${endpoint}`, true);
        log(`Endpoint available (${status}): ${endpoint}`, 'info');
      } else {
        recordTest(`Availability - ${endpoint}`, false, `Server error: ${status || error.code}`);
      }
    }
  }
}

async function testPerformance() {
  logSection('Performance Tests');
  
  const performanceTests = [
    { name: 'Health Check Response Time', endpoint: '/api/health', maxTime: 2000 },
    { name: 'API Response Time', endpoint: '/api/card-waitlist', maxTime: 5000, method: 'POST' }
  ];

  for (const test of performanceTests) {
    const startTime = Date.now();
    
    try {
      const config = { timeout: test.maxTime };
      
      if (test.method === 'POST') {
        // Use minimal valid data for POST tests
        await axios.post(`${BASE_URL}${test.endpoint}`, {
          full_name: 'Perf Test',
          email: '<EMAIL>',
          phone_number: '+**********',
          country: 'US'
        }, config);
      } else {
        await axios.get(`${BASE_URL}${test.endpoint}`, config);
      }
      
      const responseTime = Date.now() - startTime;
      
      if (responseTime <= test.maxTime) {
        recordTest(test.name, true);
        log(`Response time: ${responseTime}ms (max: ${test.maxTime}ms)`, 'info');
      } else {
        recordTest(test.name, false, `Too slow: ${responseTime}ms > ${test.maxTime}ms`);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // Check if it's a timeout vs other error
      if (error.code === 'ECONNABORTED') {
        recordTest(test.name, false, `Timeout after ${responseTime}ms`);
      } else {
        // Other errors might be acceptable (auth, validation, etc.)
        log(`${test.name}: ${error.response?.status || error.code} in ${responseTime}ms`, 'warning');
        recordTest(test.name, responseTime <= test.maxTime);
      }
    }
  }
}

// Main test runner
async function runAllTests() {
  console.log(colors.bold.blue('\n🧪 MOKHBA WALLET ERROR HANDLING TESTS\n'));
  console.log(colors.blue(`Testing against: ${BASE_URL}`));
  console.log(colors.blue(`Started at: ${new Date().toISOString()}\n`));

  const tests = [
    testHealthEndpoint,
    testEndpointAvailability,
    testNetworkTimeout,
    testRateLimiting,
    testValidationErrors,
    testAuthenticationErrors,
    testCaptchaHandling,
    testErrorRecovery,
    testPerformance
  ];

  for (const test of tests) {
    try {
      await test();
    } catch (error) {
      log(`Test suite error: ${error.message}`, 'error');
    }
    
    // Small delay between test suites
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Print summary
  logSection('Test Results Summary');
  
  const total = testResults.passed + testResults.failed;
  const passRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;
  
  console.log(colors.green(`✅ Passed: ${testResults.passed}`));
  console.log(colors.red(`❌ Failed: ${testResults.failed}`));
  console.log(colors.blue(`📊 Pass Rate: ${passRate}%`));
  
  if (testResults.errors.length > 0) {
    console.log(colors.red('\n🚨 FAILED TESTS:'));
    testResults.errors.forEach(({ test, error }) => {
      console.log(colors.red(`   • ${test}: ${error}`));
    });
  }

  console.log(colors.blue(`\nCompleted at: ${new Date().toISOString()}`));
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Handle CLI arguments
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node test-error-handling.js [options]

Options:
  --help, -h          Show this help message
  --base-url <url>    Base URL for testing (default: http://localhost:3000)
  --verbose, -v       Enable verbose logging

Environment Variables:
  TEST_BASE_URL       Base URL for testing
  NODE_ENV           Environment (affects CAPTCHA testing)

Examples:
  node test-error-handling.js
  node test-error-handling.js --base-url http://localhost:3000
  TEST_BASE_URL=https://api.mokhba.com node test-error-handling.js
    `);
    process.exit(0);
  }
  
  const baseUrlIndex = args.indexOf('--base-url');
  if (baseUrlIndex !== -1 && args[baseUrlIndex + 1]) {
    process.env.TEST_BASE_URL = args[baseUrlIndex + 1];
  }
  
  runAllTests().catch(error => {
    console.error(colors.red(`Fatal error: ${error.message}`));
    process.exit(1);
  });
} 