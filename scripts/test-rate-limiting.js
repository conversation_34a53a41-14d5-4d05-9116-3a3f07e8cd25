#!/usr/bin/env node

/**
 * Rate Limiting and CAPTCHA Protection Test Suite
 * 
 * This script tests the rate limiting and CAPTCHA protection implementation
 * across all protected endpoints in the Mokhba Wallet project.
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const OUTPUT_FILE = path.join(__dirname, '../docs/rate-limiting-test-results.md');

// Test results tracking
let testResults = {
  summary: {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    timestamp: new Date().toISOString()
  },
  endpoints: {}
};

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName, passed, details = '') {
  const icon = passed ? '✅' : '❌';
  const color = passed ? 'green' : 'red';
  log(`${icon} ${testName}`, color);
  if (details) {
    log(`   ${details}`, 'blue');
  }
  
  testResults.summary.totalTests++;
  if (passed) {
    testResults.summary.passedTests++;
  } else {
    testResults.summary.failedTests++;
  }
}

async function makeRequest(endpoint, method = 'POST', data = {}, headers = {}) {
  try {
    const url = `${BASE_URL}${endpoint}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (method !== 'GET' && Object.keys(data).length > 0) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let jsonData;
    try {
      jsonData = JSON.parse(responseData);
    } catch {
      jsonData = { raw: responseData };
    }

    return {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      data: jsonData,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      headers: {},
      data: { error: error.message },
      ok: false
    };
  }
}

async function testRateLimit(endpoint, config) {
  log(`\n🔄 Testing rate limit for ${endpoint}`, 'bold');
  
  const { maxRequests, testData } = config;
  const results = {
    endpoint,
    maxRequests,
    requestsMade: 0,
    rateLimitHit: false,
    expectedHeaders: false,
    banDetected: false,
    details: []
  };

  // Make requests up to the limit + a few extra
  for (let i = 1; i <= maxRequests + 3; i++) {
    const response = await makeRequest(endpoint, 'POST', testData);
    results.requestsMade = i;
    
    // Check for rate limit headers
    const hasRateLimitHeaders = 
      response.headers['x-ratelimit-limit'] &&
      response.headers['x-ratelimit-remaining'] &&
      response.headers['x-ratelimit-reset'];
    
    if (hasRateLimitHeaders && !results.expectedHeaders) {
      results.expectedHeaders = true;
      logTest(`Rate limit headers present`, true, 
        `Limit: ${response.headers['x-ratelimit-limit']}, ` +
        `Remaining: ${response.headers['x-ratelimit-remaining']}`);
    }

    // Check if rate limit was hit
    if (response.status === 429) {
      results.rateLimitHit = true;
      results.rateLimitAt = i;
      
      logTest(`Rate limit enforced at request ${i}`, true,
        `Expected around ${maxRequests}, got ${i}`);
      
      // Check for proper rate limit response
      if (response.headers['retry-after']) {
        logTest(`Retry-After header present`, true, 
          `${response.headers['retry-after']} seconds`);
      }
      
      break;
    }

    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Test results
  if (!results.rateLimitHit) {
    logTest(`Rate limit enforcement`, false, 
      `Made ${results.requestsMade} requests without hitting limit`);
  }

  if (!results.expectedHeaders) {
    logTest(`Rate limit headers`, false, 'Missing required headers');
  }

  testResults.endpoints[endpoint] = results;
  return results;
}

async function testCaptchaProtection(endpoint, testData) {
  log(`\n🤖 Testing CAPTCHA protection for ${endpoint}`, 'bold');
  
  // Test 1: Request without CAPTCHA token (in production mode)
  const noCaptchaResponse = await makeRequest(endpoint, 'POST', testData);
  
  if (process.env.NODE_ENV === 'development') {
    logTest(`CAPTCHA bypass in development`, true, 
      'CAPTCHA should be bypassed in development mode');
  } else {
    const captchaRequired = noCaptchaResponse.status === 400 && 
      noCaptchaResponse.data.error?.includes('CAPTCHA');
    
    logTest(`CAPTCHA requirement enforced`, captchaRequired,
      captchaRequired ? 'Properly rejected request without CAPTCHA' : 
      `Status: ${noCaptchaResponse.status}, Error: ${noCaptchaResponse.data.error}`);
  }

  // Test 2: Request with invalid CAPTCHA token
  const invalidCaptchaResponse = await makeRequest(endpoint, 'POST', {
    ...testData,
    captchaToken: 'invalid_token_12345'
  });

  if (process.env.NODE_ENV !== 'development') {
    const invalidRejected = invalidCaptchaResponse.status === 400 && 
      invalidCaptchaResponse.data.error?.includes('CAPTCHA');
    
    logTest(`Invalid CAPTCHA rejection`, invalidRejected,
      invalidRejected ? 'Properly rejected invalid CAPTCHA' : 
      `Unexpected response: ${invalidCaptchaResponse.status}`);
  }
}

async function testEndpointSecurity(endpoint, config) {
  log(`\n🛡️  Testing endpoint security: ${endpoint}`, 'yellow');
  
  // Rate limiting tests
  await testRateLimit(endpoint, config);
  
  // CAPTCHA protection tests
  await testCaptchaProtection(endpoint, config.testData);
  
  // Test malicious payloads
  await testMaliciousInputs(endpoint);
}

async function testMaliciousInputs(endpoint) {
  log(`\n⚠️  Testing malicious input handling for ${endpoint}`, 'bold');
  
  const maliciousPayloads = [
    {
      name: 'XSS Script Injection',
      data: {
        name: '<script>alert("xss")</script>',
        email: '<EMAIL>',
        subject: 'Test',
        message: 'Test message'
      }
    },
    {
      name: 'SQL Injection Attempt',
      data: {
        name: "'; DROP TABLE users; --",
        email: '<EMAIL>',
        subject: 'Test',
        message: 'Test message'
      }
    },
    {
      name: 'Extremely Long Input',
      data: {
        name: 'A'.repeat(10000),
        email: '<EMAIL>',
        subject: 'Test',
        message: 'Test message'
      }
    },
    {
      name: 'Invalid Email Format',
      data: {
        name: 'Test User',
        email: 'not-an-email',
        subject: 'Test',
        message: 'Test message'
      }
    }
  ];

  for (const payload of maliciousPayloads) {
    const response = await makeRequest(endpoint, 'POST', payload.data);
    
    // Check if the server properly handles malicious input
    const properlyHandled = response.status === 400 || response.status === 422;
    
    logTest(`${payload.name} handling`, properlyHandled,
      properlyHandled ? `Rejected with status ${response.status}` : 
      `Unexpected status: ${response.status}`);
    
    await new Promise(resolve => setTimeout(resolve, 50));
  }
}

async function testHealthAndStatus() {
  log(`\n❤️  Testing health and status endpoints`, 'bold');
  
  // Test health endpoint
  const healthResponse = await makeRequest('/api/health', 'GET');
  logTest(`Health endpoint availability`, healthResponse.ok,
    `Status: ${healthResponse.status}`);
  
  // Test that health endpoint is not rate limited aggressively
  let healthRequests = 0;
  for (let i = 0; i < 10; i++) {
    const response = await makeRequest('/api/health', 'GET');
    if (response.ok) healthRequests++;
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  logTest(`Health endpoint rate limits`, healthRequests >= 8,
    `${healthRequests}/10 requests succeeded`);
}

async function generateReport() {
  const reportContent = `# Rate Limiting and CAPTCHA Test Results

Generated: ${testResults.summary.timestamp}
Base URL: ${BASE_URL}
Environment: ${process.env.NODE_ENV || 'development'}

## Summary

- **Total Tests**: ${testResults.summary.totalTests}
- **Passed**: ${testResults.summary.passedTests} ✅
- **Failed**: ${testResults.summary.failedTests} ❌
- **Success Rate**: ${((testResults.summary.passedTests / testResults.summary.totalTests) * 100).toFixed(1)}%

## Endpoint Test Results

${Object.entries(testResults.endpoints).map(([endpoint, results]) => `
### ${endpoint}

- **Max Requests**: ${results.maxRequests}
- **Requests Made**: ${results.requestsMade}
- **Rate Limit Hit**: ${results.rateLimitHit ? '✅' : '❌'}
- **Headers Present**: ${results.expectedHeaders ? '✅' : '❌'}
${results.rateLimitAt ? `- **Rate Limited At**: Request ${results.rateLimitAt}` : ''}

`).join('')}

## Security Analysis

${testResults.summary.failedTests === 0 ? 
'🔒 **All security tests passed!** The rate limiting and CAPTCHA protection is working correctly.' :
`⚠️ **${testResults.summary.failedTests} tests failed.** Please review the implementation.`}

## Recommendations

${testResults.summary.failedTests > 0 ? `
- Review failed test cases above
- Check rate limit configurations
- Verify CAPTCHA environment variables
- Ensure proper error handling
` : `
- Monitor rate limit violations in production logs
- Adjust thresholds based on actual usage patterns
- Consider implementing additional security measures for high-risk endpoints
`}

---

*Report generated by rate limiting test suite*
`;

  fs.writeFileSync(OUTPUT_FILE, reportContent);
  log(`\n📄 Test report saved to: ${OUTPUT_FILE}`, 'green');
}

async function main() {
  log('🚀 Starting Rate Limiting and CAPTCHA Protection Tests\n', 'bold');
  
  // Test configuration for each endpoint
  const testConfigs = {
    '/api/card-waitlist': {
      maxRequests: 10,
      testData: {
        full_name: 'Test User',
        email: '<EMAIL>',
        phone_number: '+1234567890',
        country: 'US',
        interest: 'online_purchases',
        card_type: 'virtual'
      }
    },
    '/api/support-tickets': {
      maxRequests: 10,
      testData: {
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'Test message for rate limiting'
      }
    },
    '/api/feature-requests': {
      maxRequests: 10,
      testData: {
        name: 'Test User',
        email: '<EMAIL>',
        category: 'feature',
        title: 'Test Feature',
        description: 'Test feature description'
      }
    },
    '/api/status-subscriptions': {
      maxRequests: 10,
      testData: {
        email: '<EMAIL>'
      }
    },
    '/api/auth/magic-link': {
      maxRequests: 5,
      testData: {
        email: '<EMAIL>'
      }
    }
  };

  // Test health endpoint first
  await testHealthAndStatus();

  // Test each protected endpoint
  for (const [endpoint, config] of Object.entries(testConfigs)) {
    await testEndpointSecurity(endpoint, config);
    
    // Wait between endpoint tests to avoid interference
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Generate and save report
  await generateReport();

  // Final summary
  log(`\n📊 Test Summary:`, 'bold');
  log(`   Total Tests: ${testResults.summary.totalTests}`);
  log(`   Passed: ${testResults.summary.passedTests}`, 'green');
  log(`   Failed: ${testResults.summary.failedTests}`, testResults.summary.failedTests > 0 ? 'red' : 'green');
  log(`   Success Rate: ${((testResults.summary.passedTests / testResults.summary.totalTests) * 100).toFixed(1)}%`);

  process.exit(testResults.summary.failedTests > 0 ? 1 : 0);
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  log(`\n❌ Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  main().catch(error => {
    log(`\n❌ Test suite failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { main, testRateLimit, testCaptchaProtection }; 