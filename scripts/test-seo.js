#!/usr/bin/env node

/**
 * SEO Testing Script for Mokhba Wallet
 * Tests meta tags, Open Graph, Twitter Cards, structured data, and more
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  testPages: [
    { path: '/', name: 'Home (English)' },
    { path: '/ar/', name: 'Home (Arabic)' },
    { path: '/about', name: 'About (English)' },
    { path: '/ar/about', name: 'About (Arabic)' },
    { path: '/blog', name: '<PERSON><PERSON> (English)' },
    { path: '/ar/blog', name: 'Blog (Arabic)' },
    { path: '/docs', name: 'Documentation' },
    { path: '/support', name: 'Support' },
    { path: '/status', name: 'Status' }
  ],
  maxDescriptionLength: 160,
  maxTitleLength: 60,
  requiredOgDimensions: { width: 1200, height: 630 }
};

class SEOTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const colorMap = {
      success: colors.green,
      error: colors.red,
      warning: colors.yellow,
      info: colors.blue,
      highlight: colors.cyan
    };
    
    const color = colorMap[type] || colors.reset;
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
  }

  async fetchPage(url) {
    return new Promise((resolve, reject) => {
      const request = https.get(url, (response) => {
        let data = '';
        
        response.on('data', (chunk) => {
          data += chunk;
        });
        
        response.on('end', () => {
          resolve({
            statusCode: response.statusCode,
            headers: response.headers,
            body: data
          });
        });
      });
      
      request.on('error', reject);
      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  parseHTML(html) {
    // Simple HTML parsing for basic meta tag extraction
    const metaTags = {};
    const linkTags = {};
    
    // Extract title
    const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
    metaTags.title = titleMatch ? titleMatch[1].trim() : null;
    
    // Extract meta tags
    const metaMatches = html.matchAll(/<meta\s+([^>]+)>/gi);
    for (const match of metaMatches) {
      const attributes = match[1];
      const nameMatch = attributes.match(/name=["']([^"']+)["']/i);
      const propertyMatch = attributes.match(/property=["']([^"']+)["']/i);
      const contentMatch = attributes.match(/content=["']([^"']+)["']/i);
      
      if (contentMatch) {
        if (nameMatch) {
          metaTags[nameMatch[1]] = contentMatch[1];
        } else if (propertyMatch) {
          metaTags[propertyMatch[1]] = contentMatch[1];
        }
      }
    }
    
    // Extract link tags
    const linkMatches = html.matchAll(/<link\s+([^>]+)>/gi);
    for (const match of linkMatches) {
      const attributes = match[1];
      const relMatch = attributes.match(/rel=["']([^"']+)["']/i);
      const hrefMatch = attributes.match(/href=["']([^"']+)["']/i);
      const hreflangMatch = attributes.match(/hreflang=["']([^"']+)["']/i);
      
      if (relMatch) {
        if (!linkTags[relMatch[1]]) {
          linkTags[relMatch[1]] = [];
        }
        linkTags[relMatch[1]].push({
          href: hrefMatch ? hrefMatch[1] : null,
          hreflang: hreflangMatch ? hreflangMatch[1] : null
        });
      }
    }
    
    // Extract structured data
    const structuredDataMatches = html.matchAll(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([^<]*)<\/script>/gi);
    const structuredData = [];
    for (const match of structuredDataMatches) {
      try {
        structuredData.push(JSON.parse(match[1]));
      } catch (e) {
        // Invalid JSON
      }
    }
    
    return {
      metaTags,
      linkTags,
      structuredData,
      html
    };
  }

  async testPage(pageConfig) {
    const url = `${config.baseUrl}${pageConfig.path}`;
    this.log(`Testing: ${pageConfig.name} (${url})`, 'highlight');
    
    try {
      const response = await this.fetchPage(url);
      
      if (response.statusCode !== 200) {
        throw new Error(`HTTP ${response.statusCode}`);
      }
      
      const parsed = this.parseHTML(response.body);
      
      // Run all tests
      const testResults = {
        page: pageConfig.name,
        url: url,
        basicMeta: this.testBasicMetaTags(parsed),
        openGraph: this.testOpenGraph(parsed),
        twitterCards: this.testTwitterCards(parsed),
        structuredData: this.testStructuredData(parsed),
        multilingual: this.testMultilingualSEO(parsed, pageConfig.path),
        technical: this.testTechnicalSEO(parsed, response.headers)
      };
      
      this.results.details.push(testResults);
      
      // Count results
      Object.values(testResults).forEach(category => {
        if (typeof category === 'object' && category.status) {
          if (category.status === 'pass') this.results.passed++;
          else if (category.status === 'fail') this.results.failed++;
          else if (category.status === 'warning') this.results.warnings++;
        }
      });
      
      this.log(`✓ Completed testing: ${pageConfig.name}`, 'success');
      
    } catch (error) {
      this.log(`✗ Failed to test ${pageConfig.name}: ${error.message}`, 'error');
      this.results.failed++;
    }
  }

  testBasicMetaTags(parsed) {
    const results = { status: 'pass', issues: [], recommendations: [] };
    const { metaTags } = parsed;
    
    // Test title tag
    if (!metaTags.title || !metaTags.title.trim()) {
      results.issues.push('Missing title tag');
      results.status = 'fail';
    } else if (metaTags.title.length > config.maxTitleLength) {
      results.issues.push(`Title too long (${metaTags.title.length} chars, max ${config.maxTitleLength})`);
      results.status = 'warning';
    }
    
    // Test meta description
    if (!metaTags.description || !metaTags.description.trim()) {
      results.issues.push('Missing meta description');
      results.status = 'fail';
    } else if (metaTags.description.length > config.maxDescriptionLength) {
      results.issues.push(`Description too long (${metaTags.description.length} chars, max ${config.maxDescriptionLength})`);
      results.status = 'warning';
    }
    
    // Test viewport meta tag
    if (!metaTags.viewport) {
      results.issues.push('Missing viewport meta tag');
      results.status = 'fail';
    }
    
    return results;
  }

  testOpenGraph(parsed) {
    const results = { status: 'pass', issues: [], recommendations: [] };
    const { metaTags } = parsed;
    
    const requiredOgTags = ['og:title', 'og:description', 'og:url', 'og:type', 'og:image'];
    
    // Check required tags
    requiredOgTags.forEach(tag => {
      if (!metaTags[tag]) {
        results.issues.push(`Missing ${tag}`);
        results.status = 'fail';
      }
    });
    
    // Test image dimensions if specified
    if (metaTags['og:image:width'] && metaTags['og:image:height']) {
      const width = parseInt(metaTags['og:image:width']);
      const height = parseInt(metaTags['og:image:height']);
      
      if (width !== config.requiredOgDimensions.width || height !== config.requiredOgDimensions.height) {
        results.recommendations.push(
          `OG image dimensions (${width}x${height}) don't match recommended (${config.requiredOgDimensions.width}x${config.requiredOgDimensions.height})`
        );
      }
    }
    
    return results;
  }

  testTwitterCards(parsed) {
    const results = { status: 'pass', issues: [], recommendations: [] };
    const { metaTags } = parsed;
    
    const requiredTwitterTags = ['twitter:card', 'twitter:title', 'twitter:description'];
    
    // Check required tags
    requiredTwitterTags.forEach(tag => {
      if (!metaTags[tag]) {
        results.issues.push(`Missing ${tag}`);
        results.status = 'fail';
      }
    });
    
    // Check card type
    if (metaTags['twitter:card'] && !['summary', 'summary_large_image', 'app', 'player'].includes(metaTags['twitter:card'])) {
      results.issues.push(`Invalid twitter:card value: ${metaTags['twitter:card']}`);
      results.status = 'fail';
    }
    
    return results;
  }

  testStructuredData(parsed) {
    const results = { status: 'pass', issues: [], recommendations: [] };
    const { structuredData } = parsed;
    
    if (structuredData.length === 0) {
      results.issues.push('No structured data found');
      results.status = 'fail';
      return results;
    }
    
    const validSchemas = [];
    structuredData.forEach((data, index) => {
      // Validate basic structure
      if (!data['@context'] || !data['@type']) {
        results.issues.push(`Structured data ${index + 1}: Missing @context or @type`);
        results.status = 'fail';
        return;
      }
      
      validSchemas.push(data['@type']);
      
      // Validate Organization schema
      if (data['@type'] === 'Organization') {
        if (!data.name || !data.url) {
          results.issues.push('Organization schema missing required properties');
          results.status = 'fail';
        }
      }
    });
    
    return results;
  }

  testMultilingualSEO(parsed, path) {
    const results = { status: 'pass', issues: [], recommendations: [] };
    const { linkTags } = parsed;
    
    // Check hreflang tags
    const alternateLinks = linkTags.alternate || [];
    const hreflangLinks = alternateLinks.filter(link => link.hreflang);
    
    if (hreflangLinks.length === 0) {
      results.issues.push('No hreflang tags found');
      results.status = 'fail';
    } else {
      const languages = hreflangLinks.map(link => link.hreflang);
      
      // Check for main languages
      if (!languages.includes('en')) {
        results.issues.push('Missing hreflang for English');
        results.status = 'fail';
      }
      
      if (!languages.includes('ar')) {
        results.issues.push('Missing hreflang for Arabic');
        results.status = 'fail';
      }
    }
    
    return results;
  }

  testTechnicalSEO(parsed, headers) {
    const results = { status: 'pass', issues: [], recommendations: [] };
    
    // Check content-type
    const contentType = headers['content-type'];
    if (!contentType || !contentType.includes('text/html')) {
      results.issues.push('Invalid or missing content-type header');
      results.status = 'fail';
    }
    
    // Check for canonical link
    const { linkTags } = parsed;
    if (!linkTags.canonical || linkTags.canonical.length === 0) {
      results.issues.push('Missing canonical link');
      results.status = 'fail';
    }
    
    return results;
  }

  async testSitemap() {
    this.log('Testing sitemap accessibility...', 'highlight');
    
    try {
      const sitemapUrl = `${config.baseUrl}/sitemap.xml`;
      const response = await this.fetchPage(sitemapUrl);
      
      if (response.statusCode === 200) {
        this.log('✓ Sitemap accessible', 'success');
        this.results.passed++;
      } else {
        this.log(`✗ Sitemap not accessible (HTTP ${response.statusCode})`, 'error');
        this.results.failed++;
      }
    } catch (error) {
      this.log(`✗ Sitemap test failed: ${error.message}`, 'error');
      this.results.failed++;
    }
  }

  async testRobots() {
    this.log('Testing robots.txt...', 'highlight');
    
    try {
      const robotsUrl = `${config.baseUrl}/robots.txt`;
      const response = await this.fetchPage(robotsUrl);
      
      if (response.statusCode === 200) {
        this.log('✓ Robots.txt accessible', 'success');
        this.results.passed++;
      } else {
        this.log(`✗ Robots.txt not accessible (HTTP ${response.statusCode})`, 'error');
        this.results.failed++;
      }
    } catch (error) {
      this.log(`✗ Robots.txt test failed: ${error.message}`, 'error');
      this.results.failed++;
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.results.passed + this.results.failed + this.results.warnings,
        passed: this.results.passed,
        failed: this.results.failed,
        warnings: this.results.warnings,
        score: Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100) || 0
      },
      details: this.results.details
    };
    
    // Write detailed report to file
    const reportPath = path.join(process.cwd(), 'seo-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Console summary
    this.log('\n' + '='.repeat(60), 'highlight');
    this.log('SEO TEST SUMMARY', 'highlight');
    this.log('='.repeat(60), 'highlight');
    this.log(`Total Tests: ${report.summary.totalTests}`, 'info');
    this.log(`Passed: ${report.summary.passed}`, 'success');
    this.log(`Failed: ${report.summary.failed}`, 'error');
    this.log(`Warnings: ${report.summary.warnings}`, 'warning');
    this.log(`Overall Score: ${report.summary.score}%`, report.summary.score >= 80 ? 'success' : 'warning');
    this.log(`Detailed report saved to: ${reportPath}`, 'info');
    
    return report;
  }

  async runAllTests() {
    this.log('Starting comprehensive SEO testing...', 'highlight');
    this.log(`Base URL: ${config.baseUrl}`, 'info');
    
    // Test each page
    for (const pageConfig of config.testPages) {
      await this.testPage(pageConfig);
    }
    
    // Test sitemap and robots.txt
    await this.testSitemap();
    await this.testRobots();
    
    // Generate and return report
    return this.generateReport();
  }
}

// CLI handling
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
${colors.bold}Mokhba SEO Testing Tool${colors.reset}

Usage: node scripts/test-seo.js [options]

Options:
  --help, -h          Show this help message
  --url <url>         Set base URL (default: http://localhost:3000)
  --page <path>       Test specific page only

Examples:
  node scripts/test-seo.js
  node scripts/test-seo.js --url https://mokhba.com
  node scripts/test-seo.js --page /about
    `);
    process.exit(0);
  }
  
  // Parse arguments
  const urlIndex = args.indexOf('--url');
  if (urlIndex !== -1 && args[urlIndex + 1]) {
    config.baseUrl = args[urlIndex + 1];
  }
  
  const pageIndex = args.indexOf('--page');
  if (pageIndex !== -1 && args[pageIndex + 1]) {
    const testPath = args[pageIndex + 1];
    config.testPages = [{ path: testPath, name: `Custom: ${testPath}` }];
  }
  
  const tester = new SEOTester();
  
  try {
    const report = await tester.runAllTests();
    
    // Exit with appropriate code
    process.exit(report.summary.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error(`${colors.red}Fatal error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { SEOTester, config }; 