#!/usr/bin/env node

/**
 * Security Headers Testing Script
 * 
 * This script tests the HTTP security headers implementation for Mokhba Wallet.
 * It verifies that all required security headers are present and properly configured.
 * 
 * Usage:
 *   node scripts/test-security-headers.js
 *   npm run test:security-headers
 * 
 * Requirements:
 *   - Application must be running (npm run dev or npm start)
 *   - Default test URL: http://localhost:3000
 */

const http = require('http');
const https = require('https');
const url = require('url');

// Configuration
const TEST_URL = process.env.TEST_URL || 'http://localhost:3000';
const TIMEOUT = 10000; // 10 seconds

// Expected security headers and their validation rules
const EXPECTED_HEADERS = {
  'content-security-policy': {
    required: true,
    description: 'Content Security Policy - Prevents XSS and code injection attacks',
    validate: (value) => {
      const requiredDirectives = [
        "default-src 'self'",
        "script-src",
        "style-src",
        "img-src",
        "connect-src",
        "frame-ancestors 'none'"
      ];
      return requiredDirectives.every(directive => 
        value.toLowerCase().includes(directive.toLowerCase())
      );
    }
  },
  'strict-transport-security': {
    required: true,
    description: 'Strict Transport Security - Enforces HTTPS connections',
    validate: (value) => value.includes('max-age=') && parseInt(value.match(/max-age=(\d+)/)?.[1] || '0') > 31536000
  },
  'x-frame-options': {
    required: true,
    description: 'X-Frame-Options - Prevents clickjacking attacks',
    validate: (value) => ['DENY', 'SAMEORIGIN'].includes(value.toUpperCase())
  },
  'x-content-type-options': {
    required: true,
    description: 'X-Content-Type-Options - Prevents MIME type sniffing',
    validate: (value) => value.toLowerCase() === 'nosniff'
  },
  'referrer-policy': {
    required: true,
    description: 'Referrer Policy - Controls referrer information leakage',
    validate: (value) => [
      'no-referrer',
      'no-referrer-when-downgrade', 
      'origin',
      'origin-when-cross-origin',
      'same-origin',
      'strict-origin',
      'strict-origin-when-cross-origin'
    ].includes(value.toLowerCase())
  },
  'permissions-policy': {
    required: true,
    description: 'Permissions Policy - Restricts browser feature access',
    validate: (value) => value.includes('camera=') && value.includes('microphone=')
  },
  'x-dns-prefetch-control': {
    required: false,
    description: 'X-DNS-Prefetch-Control - Controls DNS prefetching',
    validate: (value) => ['on', 'off'].includes(value.toLowerCase())
  },
  'x-xss-protection': {
    required: false,
    description: 'X-XSS-Protection - Legacy XSS protection for older browsers',
    validate: (value) => value.includes('1')
  },
  'cross-origin-embedder-policy': {
    required: false,
    description: 'Cross-Origin-Embedder-Policy - Enables cross-origin isolation',
    validate: (value) => ['unsafe-none', 'require-corp', 'credentialless'].includes(value.toLowerCase())
  },
  'cross-origin-opener-policy': {
    required: false,
    description: 'Cross-Origin-Opener-Policy - Isolates browsing context',
    validate: (value) => ['unsafe-none', 'same-origin-allow-popups', 'same-origin'].includes(value.toLowerCase())
  },
  'cross-origin-resource-policy': {
    required: false,
    description: 'Cross-Origin-Resource-Policy - Controls cross-origin resource sharing',
    validate: (value) => ['same-site', 'same-origin', 'cross-origin'].includes(value.toLowerCase())
  }
};

console.log('🔒 Testing Security Headers for Mokhba Wallet');
console.log(`Target URL: ${TEST_URL}`);
console.log('─'.repeat(80));

/**
 * Make HTTP request and analyze headers
 */
function testSecurityHeaders(testUrl) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(testUrl);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
      path: parsedUrl.path || '/',
      method: 'GET',
      timeout: TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Security-Headers-Test/1.0)'
      }
    };

    const req = client.request(options, (res) => {
      const headers = {};
      
      // Normalize header names to lowercase
      for (const [key, value] of Object.entries(res.headers)) {
        headers[key.toLowerCase()] = value;
      }
      
      resolve({
        statusCode: res.statusCode,
        headers: headers
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${TIMEOUT}ms`));
    });

    req.end();
  });
}

/**
 * Analyze and report on security headers
 */
function analyzeHeaders(responseHeaders) {
  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
  };

  console.log('📊 Security Headers Analysis:\n');

  // Check each expected header
  for (const [headerName, config] of Object.entries(EXPECTED_HEADERS)) {
    const headerValue = responseHeaders[headerName];
    const present = !!headerValue;
    
    let status = 'PASS';
    let message = '';
    
    if (!present) {
      if (config.required) {
        status = 'FAIL';
        message = 'Header is missing';
        results.failed++;
      } else {
        status = 'WARN';
        message = 'Optional header is missing';
        results.warnings++;
      }
    } else {
      // Validate header value
      if (config.validate && !config.validate(headerValue)) {
        status = 'FAIL';
        message = 'Header value is invalid or insecure';
        results.failed++;
      } else {
        results.passed++;
      }
    }

    // Format status with colors
    const statusIcon = {
      'PASS': '✅',
      'FAIL': '❌',
      'WARN': '⚠️'
    }[status];

    console.log(`${statusIcon} ${headerName.toUpperCase()}`);
    console.log(`   ${config.description}`);
    if (present) {
      console.log(`   Value: ${headerValue}`);
    }
    if (message) {
      console.log(`   Issue: ${message}`);
    }
    console.log('');

    results.details.push({
      header: headerName,
      status,
      present,
      value: headerValue,
      message,
      description: config.description
    });
  }

  return results;
}

/**
 * Generate security recommendations
 */
function generateRecommendations(results) {
  console.log('🛡️ Security Recommendations:\n');
  
  const failedHeaders = results.details.filter(h => h.status === 'FAIL');
  const warningHeaders = results.details.filter(h => h.status === 'WARN');
  
  if (failedHeaders.length === 0 && warningHeaders.length === 0) {
    console.log('✅ Excellent! All security headers are properly configured.');
    console.log('   Your application follows security best practices.');
    return;
  }

  if (failedHeaders.length > 0) {
    console.log('🚨 Critical Issues:');
    failedHeaders.forEach(header => {
      console.log(`   • Fix ${header.header}: ${header.message}`);
    });
    console.log('');
  }

  if (warningHeaders.length > 0) {
    console.log('⚠️ Recommendations:');
    warningHeaders.forEach(header => {
      console.log(`   • Consider adding ${header.header}: ${header.description}`);
    });
    console.log('');
  }

  console.log('📚 Resources:');
  console.log('   • OWASP Security Headers: https://owasp.org/www-project-secure-headers/');
  console.log('   • MDN Web Security: https://developer.mozilla.org/en-US/docs/Web/Security');
  console.log('   • CSP Evaluator: https://csp-evaluator.withgoogle.com/');
}

/**
 * Main testing function
 */
async function runSecurityTest() {
  try {
    console.log('🔍 Fetching headers...\n');
    
    const response = await testSecurityHeaders(TEST_URL);
    
    if (response.statusCode >= 400) {
      console.log(`⚠️ Received HTTP ${response.statusCode} response`);
      console.log('   This may indicate an application error, but headers can still be tested.\n');
    }
    
    const results = analyzeHeaders(response.headers);
    
    console.log('─'.repeat(80));
    console.log('📈 Summary:');
    console.log(`   ✅ Passed: ${results.passed}`);
    console.log(`   ❌ Failed: ${results.failed}`);
    console.log(`   ⚠️ Warnings: ${results.warnings}`);
    console.log('');
    
    generateRecommendations(results);
    
    // Exit with appropriate code
    if (results.failed > 0) {
      console.log('❌ Security header test failed');
      process.exit(1);
    } else if (results.warnings > 0) {
      console.log('⚠️ Security header test passed with warnings');
      process.exit(0);
    } else {
      console.log('✅ Security header test passed');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('');
    console.error('💡 Troubleshooting:');
    console.error('   • Ensure the application is running (npm run dev)');
    console.error('   • Check if the URL is accessible');
    console.error('   • Verify firewall settings');
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Security Headers Testing Script');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/test-security-headers.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('');
  console.log('Environment Variables:');
  console.log('  TEST_URL       URL to test (default: http://localhost:3000)');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/test-security-headers.js');
  console.log('  TEST_URL=https://mokhba.com node scripts/test-security-headers.js');
  process.exit(0);
}

// Run the test
runSecurityTest(); 