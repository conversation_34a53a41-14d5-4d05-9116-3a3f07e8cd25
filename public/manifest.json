{"name": "Mokhba - Arabic Cryptocurrency Wallet", "short_name": "<PERSON><PERSON><PERSON>", "description": "The first Arabic cryptocurrency wallet that empowers users to securely store and effortlessly manage their crypto assets.", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#73AED2", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["finance", "productivity", "utilities"], "icons": [{"src": "/favicon-16x16.png", "sizes": "16x16", "type": "image/png"}, {"src": "/favicon-32x32.png", "sizes": "32x32", "type": "image/png"}, {"src": "/apple-touch-icon.png", "sizes": "180x180", "type": "image/png", "purpose": "apple"}, {"src": "/logo.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "/favicon.ico", "sizes": "32x32", "type": "image/x-icon"}], "screenshots": [{"src": "/screenshots/desktop-home.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Home page on desktop"}, {"src": "/screenshots/mobile-wallet.png", "sizes": "375x812", "type": "image/png", "platform": "narrow", "label": "Wallet interface on mobile"}], "related_applications": [{"platform": "webapp", "url": "https://mokhba.com"}], "prefer_related_applications": false, "shortcuts": [{"name": "Wallet", "short_name": "Wallet", "description": "Access your cryptocurrency wallet", "url": "/app", "icons": [{"src": "/logo.svg", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Blog", "short_name": "Blog", "description": "Read latest crypto news and tutorials", "url": "/blog", "icons": [{"src": "/logo.svg", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Support", "short_name": "Support", "description": "Get help and support", "url": "/support", "icons": [{"src": "/logo.svg", "sizes": "96x96", "type": "image/svg+xml"}]}], "edge_side_panel": {"preferred_width": 400}, "protocol_handlers": [{"protocol": "web+mokhba", "url": "/app?action=%s"}], "file_handlers": [{"action": "/app/import", "accept": {"application/json": [".json"], "text/csv": [".csv"]}}]}