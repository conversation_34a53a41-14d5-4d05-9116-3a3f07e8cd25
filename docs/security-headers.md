# Security Headers Implementation

This document describes the HTTP security headers implementation for Mokhba Wallet, explaining the purpose of each header and how to adjust them for future needs.

## 🛡️ Overview

HTTP security headers are crucial for protecting web applications against common attacks like XSS, clickjacking, and data injection. Our implementation includes industry-standard headers that meet modern security requirements.

## 📋 Implemented Headers

### 1. Content-Security-Policy (CSP)
**Purpose**: Prevents Cross-Site Scripting (XSS) and code injection attacks by controlling which resources can be loaded.

**Current Configuration**:
```
default-src 'self';
script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live https://fonts.googleapis.com https://fonts.gstatic.com;
style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com;
img-src 'self' data: https: blob:;
font-src 'self' https://fonts.gstatic.com data:;
connect-src 'self' https://*.supabase.co https://supabase.com wss://*.supabase.co https://api.coingecko.com https://api.coinbase.com https://mainnet.infura.io https://polygon-rpc.com https://arbitrum-mainnet.infura.io https://optimism-mainnet.infura.io https://base-mainnet.g.alchemy.com;
frame-src 'none';
object-src 'none';
base-uri 'self';
form-action 'self';
frame-ancestors 'none';
upgrade-insecure-requests
```

**What it does**:
- Restricts script execution to trusted sources
- Allows Google Fonts for typography
- Permits images from HTTPS sources and data URLs
- Enables connections to crypto APIs and Supabase
- Prevents framing and object embedding
- Forces HTTPS upgrades

**When to modify**: Add new domains to `connect-src` when integrating new APIs or services.

### 2. Strict-Transport-Security (HSTS)
**Purpose**: Enforces HTTPS connections and prevents man-in-the-middle attacks.

**Current Configuration**: `max-age=63072000; includeSubDomains; preload`

**What it does**:
- Forces HTTPS for 2 years (63072000 seconds)
- Applies to all subdomains
- Enables HSTS preload list inclusion

**When to modify**: Rarely needed. Only adjust `max-age` if security policies require different duration.

### 3. X-Frame-Options
**Purpose**: Prevents clickjacking attacks by controlling iframe embedding.

**Current Configuration**: `DENY`

**What it does**:
- Completely prevents the page from being embedded in frames
- Protects against clickjacking attacks

**When to modify**: Change to `SAMEORIGIN` if you need to embed pages within your own domain.

### 4. X-Content-Type-Options
**Purpose**: Prevents MIME type sniffing attacks.

**Current Configuration**: `nosniff`

**What it does**:
- Forces browsers to respect the declared content type
- Prevents execution of files with incorrect MIME types

**When to modify**: Never change this - it should always be `nosniff`.

### 5. Referrer-Policy
**Purpose**: Controls how much referrer information is shared with external sites.

**Current Configuration**: `strict-origin-when-cross-origin`

**What it does**:
- Sends full URL for same-origin requests
- Sends only origin for cross-origin HTTPS requests
- Sends nothing when downgrading from HTTPS to HTTP

**When to modify**: Consider `no-referrer` for maximum privacy or `same-origin` for stricter control.

### 6. Permissions-Policy
**Purpose**: Restricts access to browser features and APIs.

**Current Configuration**: 
```
camera=(), microphone=(), geolocation=(), interest-cohort=(), 
payment=(self), usb=(), serial=(), bluetooth=(), 
magnetometer=(), gyroscope=(), accelerometer=()
```

**What it does**:
- Disables camera and microphone access
- Blocks geolocation tracking
- Prevents FLoC tracking (interest-cohort)
- Allows payment APIs for the current origin only
- Disables hardware device access

**When to modify**: Enable specific features if needed (e.g., `camera=(self)` for video features).

### 7. Additional Security Headers

#### X-DNS-Prefetch-Control
**Purpose**: Controls DNS prefetching behavior.
**Configuration**: `on`
**Effect**: Allows DNS prefetching for performance while maintaining security.

#### X-XSS-Protection
**Purpose**: Legacy XSS protection for older browsers.
**Configuration**: `1; mode=block`
**Effect**: Enables XSS filtering and blocks suspicious content.

#### Cross-Origin-Embedder-Policy
**Purpose**: Enables cross-origin isolation for enhanced security.
**Configuration**: `credentialless`
**Effect**: Allows cross-origin resources without credentials.

#### Cross-Origin-Opener-Policy
**Purpose**: Isolates browsing contexts from cross-origin windows.
**Configuration**: `same-origin`
**Effect**: Prevents cross-origin windows from accessing the current context.

#### Cross-Origin-Resource-Policy
**Purpose**: Controls how resources can be accessed cross-origin.
**Configuration**: `same-origin`
**Effect**: Restricts resource access to same-origin requests only.

## 🔧 Configuration Management

### Location
Security headers are configured in `next.config.js` in the `headers()` function.

### Environment-Specific Adjustments

#### Development
- Consider relaxing CSP for hot-reload functionality
- May need additional localhost entries

#### Staging
- Test with production-like restrictions
- Verify all external integrations work

#### Production
- Use strictest possible settings
- Regularly audit and update allowed sources

## 🛠️ Common Modifications

### Adding New External Services

When integrating new external services, update the CSP directives:

```javascript
// Example: Adding a new analytics service
"connect-src 'self' https://*.supabase.co https://analytics.example.com"

// Example: Adding a new font provider
"font-src 'self' https://fonts.gstatic.com https://cdn.example.com data:"
```

### Enabling Browser Features

To enable restricted browser features:

```javascript
// Example: Enable camera for video calling
"camera=(self)"

// Example: Enable geolocation for location-based features
"geolocation=(self)"
```

### Adjusting Frame Options

If you need to embed content:

```javascript
// Allow framing from same origin
"X-Frame-Options": "SAMEORIGIN"

// Or use CSP frame-ancestors instead
"frame-ancestors 'self'"
```

## 🧪 Testing

### Local Testing
```bash
# Test all security headers
npm run test:security-headers

# Test specific URL
TEST_URL=http://localhost:3000/specific-page npm run test:security-headers
```

### Browser Testing
1. Open DevTools → Network tab
2. Refresh the page
3. Check response headers in any request
4. Verify all security headers are present

### Online Testing Tools
- [Security Headers Scanner](https://securityheaders.com/)
- [Observatory by Mozilla](https://observatory.mozilla.org/)
- [CSP Evaluator](https://csp-evaluator.withgoogle.com/)

## 🚨 Troubleshooting

### Common Issues

#### Content Blocked by CSP
**Symptoms**: Resources fail to load, console shows CSP violations
**Solution**: Add the blocked domain to appropriate CSP directive

```javascript
// If external scripts are blocked
"script-src 'self' https://trusted-domain.com"

// If external stylesheets are blocked  
"style-src 'self' https://trusted-domain.com"
```

#### Wallet Connection Issues
**Symptoms**: Crypto wallet connections fail
**Solution**: Ensure wallet provider domains are in `connect-src`

```javascript
"connect-src 'self' https://wallet-provider.com wss://wallet-provider.com"
```

#### Font Loading Issues
**Symptoms**: Custom fonts don't load
**Solution**: Add font provider to `font-src` and `style-src`

```javascript
"font-src 'self' https://fonts.gstatic.com data:"
"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com"
```

### CSP Violation Reporting

To enable CSP violation reporting (development only):

```javascript
"Content-Security-Policy": `${cspDirectives}; report-uri /api/csp-report`
```

## 📊 Security Audit Compliance

Our implementation meets these security standards:

- ✅ **OWASP Top 10** - Addresses injection and XSS vulnerabilities
- ✅ **CSP Level 3** - Modern Content Security Policy implementation
- ✅ **HSTS Preload** - Maximum transport security
- ✅ **Mozilla Observatory** - A+ rating achievable
- ✅ **Security Headers** - 100% compliance possible

## 🔄 Maintenance

### Regular Tasks

1. **Monthly**: Review CSP violations in logs
2. **Quarterly**: Audit external domains in CSP
3. **Annually**: Update HSTS max-age and review all headers

### Updates for New Features

When adding new features:

1. Identify required external resources
2. Update CSP directives accordingly
3. Test thoroughly in development
4. Verify headers in staging
5. Monitor for violations in production

### Security Considerations

- **Never** use `'unsafe-inline'` or `'unsafe-eval'` unless absolutely necessary
- **Always** use HTTPS for external resources
- **Regularly** audit and remove unused domains from CSP
- **Monitor** CSP violations to detect attacks

## 📚 Additional Resources

- [MDN Web Security](https://developer.mozilla.org/en-US/docs/Web/Security)
- [OWASP Secure Headers Project](https://owasp.org/www-project-secure-headers/)
- [CSP Quick Reference](https://content-security-policy.com/)
- [HSTS Preload List](https://hstspreload.org/)

---

## 🆘 Support

For security header issues:
1. Check browser console for CSP violations
2. Test with security header testing tools
3. Review this documentation for common solutions
4. Contact the security team for complex issues 