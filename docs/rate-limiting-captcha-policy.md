# Rate Limiting and CAPTCHA Protection Policy

## Overview

This document outlines the comprehensive rate limiting and CAPTCHA protection implementation for the Mokhba Wallet project. These security measures protect against automated abuse, spam, and brute force attacks while maintaining a good user experience.

## 🚦 Rate Limiting Implementation

### Rate Limit Configurations

| Endpoint Type | Max Requests | Time Window | Use Case |
|---------------|--------------|-------------|----------|
| **Authentication** | 5 requests | 15 minutes | Magic link, login attempts |
| **Forms** | 10 requests | 10 minutes | Support, waitlist, feature requests |
| **General API** | 100 requests | 15 minutes | Standard API access |
| **Sensitive Operations** | 3 requests | 1 hour | Admin functions, high-risk actions |

### Protected Endpoints

#### 🔐 Authentication Endpoints
- `/api/auth/magic-link` - **STRICT** (5 req/15min)
  - Higher CAPTCHA threshold (0.7)
  - Required even in development

#### 📝 Form Submission Endpoints
- `/api/card-waitlist` - **MODERATE** (10 req/10min)
- `/api/support-tickets` - **MODERATE** (10 req/10min)
- `/api/feature-requests` - **MODERATE** (10 req/10min)
- `/api/status-subscriptions` - **MODERATE** (10 req/10min)

### Rate Limiting Features

#### ⚡ Intelligent Throttling
- **Sliding Window**: Uses rolling time windows instead of fixed intervals
- **Memory Caching**: Fast in-memory storage with automatic cleanup
- **IP-based Tracking**: Identifies users by IP address with proxy support

#### 🛡️ Security Escalation
- **Violation Tracking**: Monitors repeated limit violations
- **Automatic IP Banning**: Temporary bans after 5 violations
- **Progressive Penalties**: Increasing restrictions for persistent violators

#### 📊 Response Headers
```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 2024-01-20T10:30:00.000Z
Retry-After: 120
```

## 🤖 CAPTCHA Protection

### Implementation Strategy

#### CAPTCHA Integration
- **Google reCAPTCHA v3**: Invisible protection with risk scoring
- **Fallback Support**: Compatible with reCAPTCHA v2 checkbox
- **Development Mode**: Configurable bypass for local development

#### Security Thresholds

| Operation Type | Minimum Score | Skip in Dev | Notes |
|----------------|---------------|-------------|-------|
| **Form Submissions** | 0.5 | ✅ Yes | Standard protection |
| **Authentication** | 0.7 | ❌ No | Enhanced security |
| **Sensitive Operations** | 0.8 | ❌ No | Maximum protection |

### CAPTCHA Features

#### 🔍 Verification Process
1. **Token Validation**: Server-side verification with Google
2. **Score Analysis**: Risk assessment for reCAPTCHA v3
3. **Action Matching**: Ensures CAPTCHA action matches endpoint
4. **IP Logging**: Security event logging for monitoring

#### 🎨 User Experience
- **Invisible Integration**: Seamless user experience
- **Error Handling**: Clear feedback on verification failures
- **Loading States**: Visual indicators during verification
- **Accessibility**: Compliant with web accessibility standards

## 🛠️ Technical Implementation

### Rate Limiting Architecture

```typescript
// Example usage
export const POST = withRateLimit(rateLimitConfigs.forms, handlePOST);

// Custom configuration
const customLimit = {
  maxRequests: 20,
  windowMs: 5 * 60 * 1000, // 5 minutes
  message: 'Custom rate limit exceeded'
};
```

### CAPTCHA Integration

```typescript
// Server-side verification
const captchaResult = await verifyCaptcha(token, clientIp);
if (!captchaResult.success || !isValidCaptchaScore(captchaResult, 0.5)) {
  return NextResponse.json({ error: 'CAPTCHA failed' }, { status: 400 });
}
```

```tsx
// Client-side component
import CaptchaWrapper, { useCaptcha } from '@/components/CaptchaWrapper';

function MyForm() {
  const { captchaToken, handleCaptchaChange, isCaptchaValid } = useCaptcha();
  
  return (
    <form>
      {/* Form fields */}
      <CaptchaWrapper onCaptchaChange={handleCaptchaChange} />
      <button disabled={!isCaptchaValid()}>Submit</button>
    </form>
  );
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Required for production
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_site_key_here
RECAPTCHA_SECRET_KEY=your_secret_key_here

# Development can work without these (CAPTCHA bypass)
```

### Development vs Production

#### Development Mode
- **CAPTCHA**: Bypassed if no site key configured
- **Rate Limiting**: Active but with relaxed thresholds
- **Logging**: Enhanced debug information

#### Production Mode
- **CAPTCHA**: Strictly enforced
- **Rate Limiting**: Full protection active
- **Logging**: Security-focused structured logs

## 📈 Monitoring and Analytics

### Security Logging

All rate limiting and CAPTCHA events are logged with structured data:

```json
{
  "timestamp": "2024-01-20T10:30:00.000Z",
  "level": "security",
  "message": "Rate limit exceeded",
  "context": {
    "ip": "[PROVIDED]",
    "endpoint": "/api/card-waitlist",
    "requestCount": 11,
    "maxRequests": 10,
    "windowMs": 600000
  }
}
```

### Metrics Tracked

- **Rate Limit Violations**: IP, endpoint, frequency
- **CAPTCHA Failures**: Score distribution, failure reasons
- **IP Bans**: Duration, violation patterns
- **Successful Verifications**: Baseline performance metrics

## 🚨 Security Response

### Automated Actions

#### Rate Limit Violations
1. **First Violation**: Warning logged, request blocked
2. **Multiple Violations**: Violation counter incremented
3. **5+ Violations**: Temporary IP ban (1 hour default)
4. **Persistent Abuse**: Extended ban duration

#### CAPTCHA Failures
1. **Low Score**: Request rejected, user notified
2. **Verification Error**: Technical issue logged
3. **Missing Token**: Development bypass or error
4. **Repeated Failures**: Rate limit escalation

### Manual Administration

#### Admin Functions
```typescript
// Clear rate limits for specific IP
clearRateLimit('***********');

// Get rate limit statistics
const stats = getRateLimitStats();
```

#### Monitoring Dashboard
- Active rate limit violations
- CAPTCHA success/failure rates
- Currently banned IPs
- Cache performance metrics

## 🧪 Testing

### Rate Limit Testing

```javascript
// Test script: scripts/test-rate-limiting.js
// Automated validation of rate limit configurations
npm run test:rate-limits
```

### CAPTCHA Testing

```javascript
// Test various CAPTCHA scenarios
npm run test:captcha
```

### Load Testing

- **Burst Testing**: Validate rate limit enforcement
- **Sustained Load**: Ensure performance under pressure
- **Attack Simulation**: Test security response

## 📋 Best Practices

### Implementation Guidelines

1. **Gradual Rollout**: Start with warnings, then enforcement
2. **User Communication**: Clear error messages and retry guidance
3. **Performance Monitoring**: Track response times and resource usage
4. **Regular Review**: Adjust thresholds based on usage patterns

### Security Considerations

1. **IP Spoofing**: Consider additional authentication factors
2. **Distributed Attacks**: Monitor for coordinated abuse
3. **False Positives**: Provide appeals process for legitimate users
4. **GDPR Compliance**: Ensure IP logging meets privacy requirements

## 🔄 Maintenance

### Regular Tasks

- **Log Analysis**: Weekly review of security events
- **Threshold Tuning**: Monthly adjustment based on metrics
- **Cache Cleanup**: Automated cleanup of expired rate limit data
- **Documentation Updates**: Keep policies current with changes

### Emergency Procedures

- **DDoS Response**: Emergency rate limit tightening
- **CAPTCHA Bypass**: Temporary disabling for critical issues
- **IP Whitelist**: Emergency access for legitimate users
- **Rollback Plan**: Quick reversion to previous configuration

---

## 📞 Support

For questions about rate limiting or CAPTCHA implementation:

- **Technical Issues**: Check logs in development console
- **Configuration Help**: Review environment variable setup
- **Security Events**: Monitor structured security logs
- **Performance Concerns**: Analyze rate limit statistics

This implementation provides robust protection against automated abuse while maintaining excellent user experience for legitimate users. 