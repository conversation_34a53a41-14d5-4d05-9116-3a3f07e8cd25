# Logging Policy and Best Practices

This document outlines the logging standards and security practices for Mokhba Wallet to ensure secure, effective logging across all environments.

## 🔒 Security-First Principles

### ❌ Never Log Sensitive Data

**Prohibited Information**:
- API keys, secrets, tokens
- User passwords or authentication credentials
- Private keys, mnemonics, seed phrases
- Credit card numbers, PII (personally identifiable information)
- Session tokens, JWT contents
- Supabase service role keys
- Database connection strings with credentials
- Raw user input that might contain sensitive data

**Example - ❌ WRONG**:
```typescript
console.log('User login attempt:', { email, password, token }); // NEVER DO THIS
console.log('API request:', { headers: req.headers }); // May contain auth tokens
console.log('Database query:', query, params); // May contain sensitive data
```

**Example - ✅ CORRECT**:
```typescript
log.security('User login attempt', { 
  email: email.split('@')[0] + '@***', // Partially redacted
  userAgent: req.headers['user-agent'],
  ip: req.ip 
});
log.api('API request processed', { 
  method: req.method, 
  path: req.path, 
  statusCode: res.statusCode 
});
log.database('Query executed', { 
  table: 'users', 
  operation: 'select', 
  resultCount: results.length 
});
```

## 📊 Log Levels and Usage

### Production Environment (NODE_ENV=production)

**Allowed Levels**: ERROR, WARN, STARTUP, SECURITY
- **ERROR**: Application errors, failed operations, exceptions
- **WARN**: Unexpected conditions that don't break functionality
- **STARTUP**: Application lifecycle events (start, stop, health checks)
- **SECURITY**: Authentication, authorization, security events

**Forbidden in Production**: INFO, DEBUG, API (unless errors)

### Development Environment (NODE_ENV=development)

**All Levels Available**: ERROR, WARN, INFO, DEBUG, STARTUP, SECURITY, API, DATABASE

### Log Level Guidelines

#### ERROR - Always Logged
```typescript
log.error('Database connection failed', {
  database: 'users',
  error: error.message,
  retryAttempt: 3
});
```

#### WARN - Production + Development
```typescript
log.warn('Rate limit approaching', {
  userId: userId,
  currentRequests: 95,
  limit: 100,
  timeWindow: '1hour'
});
```

#### INFO - Development Only
```typescript
log.info('User profile updated', {
  userId: userId,
  fieldsUpdated: ['name', 'preferences'],
  timestamp: new Date().toISOString()
});
```

#### DEBUG - Development Only
```typescript
log.debug('Crypto price fetched', {
  symbol: 'BTC',
  price: 45000,
  source: 'coingecko',
  cached: false
});
```

#### STARTUP - Always Logged
```typescript
log.startup('Environment variables validated');
log.startup('Database connection established');
log.startup('Server listening on port 3000');
```

#### SECURITY - Always Logged
```typescript
log.security('Failed login attempt', {
  email: 'user@***',
  ip: '*************',
  attempts: 3,
  blocked: true
});
```

#### API - Development Only (unless errors)
```typescript
log.api('Wallet connection attempt', {
  walletType: 'metamask',
  chainId: 1,
  success: true
});
```

#### DATABASE - Smart Logging
```typescript
// Development: All operations
log.database('User created', { userId: newUser.id });

// Production: Only errors
log.database('User creation failed', { 
  error: error.message,
  constraint: 'unique_email' 
});
```

## 🏗️ Implementation Standards

### Use the Secure Logger

**Import the logger**:
```typescript
import { log } from '@/lib/logger';
```

**Structured Logging**:
```typescript
// ✅ GOOD - Structured with context
log.error('Payment processing failed', {
  orderId: order.id,
  amount: order.amount,
  currency: order.currency,
  errorCode: 'INSUFFICIENT_FUNDS',
  retryable: false
});

// ❌ BAD - Unstructured
console.log('Payment failed for order ' + order.id + ' with error: ' + error);
```

### Context Objects

Always provide relevant context without exposing sensitive data:

```typescript
// User operations
log.info('Profile updated', {
  userId: user.id,
  fieldsChanged: ['name', 'avatar'],
  timestamp: new Date().toISOString()
});

// API operations
log.api('Crypto transaction initiated', {
  transactionType: 'swap',
  fromToken: 'ETH',
  toToken: 'USDC',
  network: 'ethereum',
  estimatedGas: '21000'
});

// Database operations
log.database('Migration completed', {
  version: '2024_01_15_create_wallets',
  tablesAffected: ['wallets', 'transactions'],
  duration: '2.3s'
});
```

### Error Handling

**Centralized Error Logging**:
```typescript
try {
  await processPayment(order);
  log.info('Payment processed successfully', { orderId: order.id });
} catch (error) {
  log.error('Payment processing failed', {
    orderId: order.id,
    error: error instanceof Error ? error.message : String(error),
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });
  throw error; // Re-throw for proper error handling
}
```

## 🚫 Deprecated Patterns

### Console.* Methods

**Replace these patterns**:

```typescript
// ❌ DEPRECATED
console.log('User logged in');
console.error('Database error:', error);
console.warn('Memory usage high');
console.debug('API response:', response);

// ✅ NEW STANDARD
log.info('User logged in', { userId: user.id });
log.error('Database error', { error: error.message, table: 'users' });
log.warn('Memory usage high', { usage: '85%', threshold: '80%' });
log.debug('API response received', { statusCode: 200, endpoint: '/api/users' });
```

### Direct Environment Variable Logging

```typescript
// ❌ NEVER DO THIS
console.log('Config loaded:', process.env);
console.log('Database URL:', process.env.DATABASE_URL);

// ✅ SAFE ALTERNATIVE
log.startup('Configuration loaded', {
  environment: process.env.NODE_ENV,
  features: {
    analytics: !!process.env.ANALYTICS_ID,
    email: !!process.env.EMAIL_SERVICE_API_KEY
  }
});
```

## 🧪 Testing Logging

### Test Production Logging

```bash
# Test with production environment
NODE_ENV=production npm run dev

# Test with production build
npm run build
npm start
```

**Expected Production Behavior**:
- No DEBUG, INFO, or API logs (except errors)
- Structured JSON output
- No sensitive data in logs
- Startup and error logs only

### Test Development Logging

```bash
# Test with debug enabled
DEBUG=true npm run dev
```

**Expected Development Behavior**:
- All log levels visible
- Human-readable format
- Detailed context information
- Stack traces for errors

### Log Testing Script

```bash
# Test logging behavior
npm run test:logging
```

Create this script in `package.json`:
```json
{
  "scripts": {
    "test:logging": "node scripts/test-logging.js"
  }
}
```

## 📈 Monitoring and Alerting

### Production Log Monitoring

**Critical Alerts** (trigger immediate response):
- ERROR level logs
- SECURITY level logs with failed authentication
- Multiple failed startup validations

**Warning Alerts** (monitor closely):
- High frequency of WARN logs
- Database connection issues
- API rate limiting

### Log Aggregation

**Structured JSON Format** for easy parsing:
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "error",
  "message": "Payment processing failed",
  "context": {
    "orderId": "ord_123456",
    "errorCode": "INSUFFICIENT_FUNDS",
    "retryable": false
  },
  "environment": "production",
  "service": "mokhba-wallet"
}
```

## 🔄 Migration Guide

### Phase 1: Add Secure Logger

1. Import logger in each file: `import { log } from '@/lib/logger';`
2. Replace console.error with log.error
3. Replace console.warn with log.warn

### Phase 2: Structure Existing Logs

1. Add context objects to all log calls
2. Remove sensitive data from log messages
3. Use appropriate log levels

### Phase 3: Remove Console Methods

1. Search for remaining console.* calls
2. Replace with structured logging
3. Test in both development and production

### Phase 4: Validation

1. Run production build locally
2. Verify no sensitive data in logs
3. Confirm log levels work correctly

## 📚 Examples by Use Case

### Authentication Events

```typescript
// Successful login
log.security('User authentication successful', {
  userId: user.id,
  method: 'email',
  ip: request.ip,
  userAgent: request.headers['user-agent']
});

// Failed login
log.security('Authentication failed', {
  email: email.split('@')[0] + '@***',
  reason: 'invalid_password',
  attempts: 3,
  ip: request.ip
});
```

### Crypto Operations

```typescript
// Wallet connection
log.info('Wallet connected', {
  walletType: 'metamask',
  address: address.substring(0, 6) + '...' + address.substring(38),
  chainId: chainId
});

// Transaction
log.info('Transaction initiated', {
  type: 'transfer',
  network: 'ethereum',
  fromToken: 'ETH',
  toToken: 'USDC',
  estimatedGas: gasEstimate
});
```

### API Operations

```typescript
// Request processing
log.api('API request received', {
  method: request.method,
  path: request.path,
  userId: user?.id,
  ip: request.ip
});

// Response
log.api('API response sent', {
  path: request.path,
  statusCode: response.statusCode,
  duration: `${Date.now() - startTime}ms`
});
```

### Database Operations

```typescript
// Query execution
log.database('Query executed', {
  table: 'transactions',
  operation: 'select',
  duration: '45ms',
  rowCount: results.length
});

// Migration
log.database('Migration started', {
  version: '2024_01_15_add_indexes',
  estimatedDuration: '30s'
});
```

## 🔍 Code Review Checklist

**Before merging code, verify**:

- [ ] No console.log, console.error, console.warn statements
- [ ] All logging uses the secure logger (`log.*`)
- [ ] No sensitive data in log messages
- [ ] Appropriate log levels used
- [ ] Context objects provided where relevant
- [ ] Error handling includes proper logging
- [ ] Production logging tested

## 🆘 Emergency Procedures

### Sensitive Data Accidentally Logged

1. **Immediate**: Rotate any exposed credentials
2. **Alert**: Notify security team
3. **Cleanup**: Remove logs from aggregation systems
4. **Prevention**: Add patterns to sensitive data detection

### Log System Failure

1. **Fallback**: Ensure errors still reach monitoring
2. **Investigation**: Check logger configuration
3. **Recovery**: Restore logging as soon as possible

---

## 📞 Support

For logging-related questions:
- Review this document first
- Check existing code examples
- Ask in team chat for clarification
- Escalate security concerns immediately

**Remember**: When in doubt about logging sensitive data, don't log it. It's better to have less information than to compromise security. 