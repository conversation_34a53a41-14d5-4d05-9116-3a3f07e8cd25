# SEO Environment Variables

This document outlines the environment variables needed for proper SEO configuration in the Mokhba Wallet application.

## Required Variables

### Site Configuration
```bash
# Base URL for your site (used for canonical URLs, sitemaps, etc.)
NEXT_PUBLIC_SITE_URL=https://mokhba.com
```

### Search Engine Verification
```bash
# Google Search Console verification code
NEXT_PUBLIC_GOOGLE_VERIFICATION=your_google_verification_code

# Facebook domain verification code
NEXT_PUBLIC_FACEBOOK_VERIFICATION=your_facebook_verification_code
```

### Social Media
```bash
# Twitter handle for Twitter Card metadata
NEXT_PUBLIC_TWITTER_HANDLE=@mokhbawallet
```

## Optional Variables

### Analytics
```bash
# Google Analytics 4 Measurement ID
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Tag Manager Container ID
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
```

### SEO Tools
```bash
# Schema.org API key (if using validation services)
SCHEMA_ORG_API_KEY=your_schema_api_key

# SEMrush API key (for rank tracking)
SEMRUSH_API_KEY=your_semrush_api_key
```

## Environment File Setup

1. Copy the example environment file:
```bash
cp .env.example .env.local
```

2. Fill in your actual values:
```bash
# .env.local
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_GOOGLE_VERIFICATION=abc123def456
NEXT_PUBLIC_FACEBOOK_VERIFICATION=xyz789uvw012
NEXT_PUBLIC_TWITTER_HANDLE=@yourbrand
```

## Verification Setup

### Google Search Console
1. Go to [Google Search Console](https://search.google.com/search-console)
2. Add your property
3. Choose "HTML tag" verification method
4. Copy the verification code from the meta tag
5. Add it to your environment variables

### Facebook Domain Verification
1. Go to [Facebook Business Settings](https://business.facebook.com/settings/domains)
2. Add your domain
3. Choose "Meta tag" verification
4. Copy the verification code
5. Add it to your environment variables

## Usage in Application

The SEO system automatically uses these environment variables:

```typescript
// In src/lib/seo.ts
const SITE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com',
  // ... other config
};

// In src/app/layout.tsx
export const metadata: Metadata = {
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    other: {
      'facebook-domain-verification': process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION || ''
    }
  }
};
```

## Testing

Test your SEO configuration:

```bash
# Test with local environment
npm run test:seo

# Test with production URL
npm run test:seo:production
```

## Security Notes

- Never commit actual environment values to version control
- Use different verification codes for staging and production
- Keep API keys secure and rotate them regularly
- Monitor usage of any paid API services 