# CI/CD Environment Variable Enforcement

This document provides recommendations for enforcing environment variable presence and validation in your CI/CD pipelines for the Mokhba Wallet project.

## 🎯 Overview

Environment variable validation should be enforced at multiple stages:
1. **Build Time** - Validate during application build
2. **Deployment Time** - Ensure all required variables are set
3. **Runtime** - Health checks and monitoring
4. **Testing** - Validate test environment configuration

## 🔧 GitHub Actions

### Basic Workflow

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy Mokhba Wallet

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  validate-environment:
    name: Validate Environment Variables
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Validate environment variables (Build)
        run: |
          echo "Validating environment variables..."
          # Set minimal required vars for build validation
          export NEXT_PUBLIC_SUPABASE_URL="${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}"
          export NEXT_PUBLIC_SUPABASE_ANON_KEY="${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}"
          export SUPABASE_SERVICE_ROLE_KEY="${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}"
          export NODE_ENV="production"
          
          # Run build to trigger validation
          npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      
      - name: Test Security Headers
        run: |
          echo "Starting application for security testing..."
          npm start &
          sleep 10
          
          # Test security headers
          echo "Testing security headers..."
          npm run test:security-headers
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      
      - name: Health check validation
        run: |
          echo "Running health check validation..."
          
          # Test health endpoint
          response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)
          if [ $response -eq 200 ]; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed with status $response"
            exit 1
          fi
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: validate-environment
    if: github.ref == 'refs/heads/staging'
    
    steps:
      - name: Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: validate-environment
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
      
      - name: Post-deployment health check
        run: |
          echo "Waiting for deployment to be ready..."
          sleep 30
          
          # Replace with your actual production URL
          PROD_URL="https://mokhba.com"
          
          response=$(curl -s -o /dev/null -w "%{http_code}" $PROD_URL/api/health)
          if [ $response -eq 200 ]; then
            echo "✅ Production health check passed"
          else
            echo "❌ Production health check failed with status $response"
            exit 1
          fi
      
      - name: Post-deployment security headers check
        run: |
          echo "Testing production security headers..."
          
          # Replace with your actual production URL
          PROD_URL="https://mokhba.com"
          
          # Test security headers in production
          TEST_URL=$PROD_URL npm run test:security-headers
```

### Environment Variable Check Script

Create `.github/scripts/check-env-vars.sh`:

```bash
#!/bin/bash

# Environment variable validation script for CI/CD
set -e

echo "🔍 Checking required environment variables..."

# Required variables
REQUIRED_VARS=(
  "NEXT_PUBLIC_SUPABASE_URL"
  "NEXT_PUBLIC_SUPABASE_ANON_KEY" 
  "SUPABASE_SERVICE_ROLE_KEY"
)

# Optional but recommended variables
RECOMMENDED_VARS=(
  "SESSION_SECRET"
  "ALLOWED_ORIGINS"
)

missing_required=()
missing_recommended=()

# Check required variables
for var in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!var}" ]; then
    missing_required+=("$var")
  else
    echo "✅ $var is set"
  fi
done

# Check recommended variables
for var in "${RECOMMENDED_VARS[@]}"; do
  if [ -z "${!var}" ]; then
    missing_recommended+=("$var")
  else
    echo "✅ $var is set"
  fi
done

# Report results
if [ ${#missing_required[@]} -ne 0 ]; then
  echo ""
  echo "❌ Missing required environment variables:"
  printf '   %s\n' "${missing_required[@]}"
  echo ""
  echo "Please set these variables in your CI/CD environment."
  exit 1
fi

if [ ${#missing_recommended[@]} -ne 0 ]; then
  echo ""
  echo "⚠️ Missing recommended environment variables:"
  printf '   %s\n' "${missing_recommended[@]}"
  echo ""
  echo "Consider setting these for production use."
fi

echo ""
echo "🎉 All required environment variables are configured!"
```

## 🚀 Vercel Deployment

### Environment Variable Configuration

1. **Set Variables in Vercel Dashboard**:
   ```bash
   # Navigate to: Project Settings → Environment Variables
   
   # Production Environment
   NEXT_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_prod_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_prod_service_role_key
   NODE_ENV=production
   SESSION_SECRET=your_secure_session_secret
   ALLOWED_ORIGINS=https://mokhba.com,https://www.mokhba.com
   
   # Preview Environment
   NEXT_PUBLIC_SUPABASE_URL=https://your-staging-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_staging_service_role_key
   NODE_ENV=staging
   ```

2. **Vercel Build Command Validation**:
   ```json
   // vercel.json
   {
     "buildCommand": "npm run build && npm run validate-env",
     "installCommand": "npm ci"
   }
   ```

### Custom Build Script

Add to `package.json`:

```json
{
  "scripts": {
    "validate-env": "node scripts/validate-env.js",
    "build:with-validation": "npm run validate-env && npm run build"
  }
}
```

Create `scripts/validate-env.js`:

```javascript
const { validateEnvironment } = require('../src/lib/env-validation');

console.log('🔍 Validating environment variables for deployment...');

try {
  validateEnvironment();
  console.log('✅ Environment validation passed');
  process.exit(0);
} catch (error) {
  console.error('❌ Environment validation failed:', error.message);
  console.error('Deployment aborted due to invalid environment configuration.');
  process.exit(1);
}
```

## 🐳 Docker Deployment

### Dockerfile with Environment Validation

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Environment validation at build time
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY
ARG SUPABASE_SERVICE_ROLE_KEY

ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY
ENV SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY
ENV NODE_ENV=production

# Validate environment and build
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "server.js"]
```

### Docker Compose with Environment Validation

```yaml
# docker-compose.yml
version: '3.8'

services:
  mokhba-wallet:
    build:
      context: .
      args:
        NEXT_PUBLIC_SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${NEXT_PUBLIC_SUPABASE_ANON_KEY}
        SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SESSION_SECRET=${SESSION_SECRET}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    ports:
      - "3000:3000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 🔍 Monitoring & Alerting

### Environment Variable Monitoring Script

Create `scripts/monitor-env.sh`:

```bash
#!/bin/bash

# Environment monitoring script
HEALTH_URL="https://mokhba.com/api/health"
SLACK_WEBHOOK="${SLACK_WEBHOOK_URL}"

echo "🔍 Checking application health..."

response=$(curl -s "$HEALTH_URL")
status=$(echo "$response" | jq -r '.status // "unknown"')

if [ "$status" != "healthy" ]; then
  echo "❌ Health check failed!"
  echo "Response: $response"
  
  # Send Slack notification
  if [ ! -z "$SLACK_WEBHOOK" ]; then
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"🚨 Mokhba Wallet health check failed: $status\"}" \
      "$SLACK_WEBHOOK"
  fi
  
  exit 1
else
  echo "✅ Application is healthy"
fi
```

### Monitoring with GitHub Actions

```yaml
name: Production Health Check

on:
  schedule:
    - cron: '*/5 * * * *'  # Every 5 minutes
  workflow_dispatch:

jobs:
  health-check:
    runs-on: ubuntu-latest
    
    steps:
      - name: Check production health
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" https://mokhba.com/api/health)
          if [ $response -eq 200 ]; then
            echo "✅ Production is healthy"
          else
            echo "❌ Production health check failed with status $response"
            exit 1
          fi
      
      - name: Notify on failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '🚨 Mokhba Wallet production health check failed!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 📋 Best Practices Checklist

### Pre-Deployment

- [ ] All required environment variables are set
- [ ] Environment variables are validated in CI/CD pipeline
- [ ] Health checks pass in staging environment
- [ ] No sensitive values are logged or exposed
- [ ] Build process validates environment at compile time

### Deployment

- [ ] Environment-specific configurations are applied
- [ ] Health checks are enabled post-deployment
- [ ] Monitoring and alerting are configured
- [ ] Rollback plan is prepared for environment issues

### Post-Deployment

- [ ] Health endpoints are accessible
- [ ] Application logs show successful environment validation
- [ ] Monitoring dashboards reflect healthy status
- [ ] Documentation is updated with any environment changes

## 🚨 Troubleshooting

### Common CI/CD Issues

**Build fails with "Environment validation failed"**
- Check that all required secrets are set in your CI/CD platform
- Verify secret names match exactly (case-sensitive)
- Ensure secrets are available to the branch being deployed

**Health check fails in production**
- Verify all environment variables are set correctly
- Check that the health endpoint `/api/health` is accessible
- Review application logs for startup errors

**Environment variables not updating**
- Clear deployment caches
- Restart the application/containers
- Verify environment variables are set at the correct scope (environment/branch)

### Emergency Procedures

If deployment fails due to environment issues:

1. **Immediate**: Roll back to last known good deployment
2. **Investigate**: Check environment variable configuration
3. **Fix**: Update environment variables in deployment platform
4. **Validate**: Run health checks before promoting to production
5. **Document**: Update runbooks with lessons learned

---

## 📞 Support

For CI/CD and environment variable issues:
- Review this documentation
- Check application health at `/api/health`
- Contact the DevOps team for deployment platform access
- Escalate to development team for application-level issues 