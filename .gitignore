# dependencies
node_modules
/node_modules
/.pnp
.pnp.js
**/node_modules
**/node_modules/**
Desktop/mokhba8may/mokhba/node_modules

# testing
/coverage

# next.js
/.next/
/out/
.next/
**/Desktop/mokhba8may/mokhba/.next/
**/Desktop/mokhba8may/mokhba/.next/**

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
