{"name": "mokhba", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "validate-env": "node scripts/validate-env.js", "build:with-validation": "npm run validate-env && npm run build", "postinstall": "npm run validate-env || echo 'Environment validation skipped (install phase)'", "test:security-headers": "node scripts/test-security-headers.js", "security:check": "npm run test:security-headers", "test:logging": "node scripts/test-logging.js", "logging:check": "npm run test:logging", "test:error-handling": "node scripts/test-error-handling.js", "error-handling:check": "npm run test:error-handling", "test:scroll-performance": "node scripts/test-scroll-performance.js", "scroll-performance:check": "npm run test:scroll-performance", "test:rate-limits": "node scripts/test-rate-limiting.js", "rate-limits:check": "npm run test:rate-limits", "test:scroll": "node scripts/test-scroll-performance.js", "test:security": "node scripts/test-security-headers.js", "test:seo": "node scripts/test-seo.js", "test:seo:production": "node scripts/test-seo.js --url https://mokhba.com", "validate:seo": "node scripts/test-seo.js --page /", "test:images": "node scripts/test-image-optimization.js", "optimize:images": "npm run test:images", "test:rpc": "node scripts/test-rpc-connection.js", "rpc:check": "npm run test:rpc", "test:wallet-balance": "node scripts/test-wallet-balance-fix.js", "wallet-balance:check": "npm run test:wallet-balance", "test:add-wallet": "node scripts/test-add-wallet-functionality.js", "add-wallet:check": "npm run test:add-wallet"}, "dependencies": {"@metamask/sdk-react": "^0.33.0", "@rainbow-me/rainbowkit": "^2.2.4", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/wallet-adapter-wallets": "^0.19.37", "@solana/web3.js": "^1.98.2", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.75.7", "@types/dompurify": "^3.0.5", "@types/memory-cache": "^0.2.6", "@types/node-cache": "^4.1.3", "@types/react-google-recaptcha": "^2.1.9", "@types/validator": "^13.15.2", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "dompurify": "^3.2.6", "envalid": "^8.0.0", "ethers": "^6.14.0", "express-rate-limit": "^7.5.0", "framer-motion": "^12.10.5", "memory-cache": "^0.2.0", "next": "^14.2.28", "next-intl": "^3.26.5", "node-cache": "^5.1.2", "pino-pretty": "^13.0.0", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-hot-toast": "^2.5.2", "tailwindcss": "^3.4.17", "validator": "^13.15.15", "viem": "^2.29.1", "wagmi": "^2.15.2", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.17.46", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "eslint": "^8.57.1", "eslint-config-next": "^14.2.28", "typescript": "^5.8.3"}}