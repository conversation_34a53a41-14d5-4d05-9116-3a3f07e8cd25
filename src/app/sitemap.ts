import { MetadataRoute } from 'next';
import { SITE_CONFIG } from '@/lib/seo';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = SITE_CONFIG.url;
  
  // Define static pages
  const staticPages = [
    '',
    '/about',
    '/blog',
    '/docs',
    '/privacy',
    '/terms',
    '/status',
    '/support',
    '/security',
    '/explore',
    '/learn',
    '/app',
    '/feature-request'
  ];

  // Generate sitemap entries for each page in both languages
  const sitemapEntries: MetadataRoute.Sitemap = [];

  staticPages.forEach(page => {
    // English version
    sitemapEntries.push({
      url: `${baseUrl}${page}`,
      lastModified: new Date(),
      changeFrequency: page === '' ? 'daily' : 'weekly',
      priority: page === '' ? 1.0 : 0.8,
      alternates: {
        languages: {
          en: `${baseUrl}${page}`,
          ar: `${baseUrl}/ar${page}`
        }
      }
    });

    // Arabic version
    sitemapEntries.push({
      url: `${baseUrl}/ar${page}`,
      lastModified: new Date(),
      changeFrequency: page === '' ? 'daily' : 'weekly',
      priority: page === '' ? 1.0 : 0.8,
      alternates: {
        languages: {
          en: `${baseUrl}${page}`,
          ar: `${baseUrl}/ar${page}`
        }
      }
    });
  });

  // Add API endpoints that should be indexed
  sitemapEntries.push({
    url: `${baseUrl}/api/health`,
    lastModified: new Date(),
    changeFrequency: 'hourly',
    priority: 0.3
  });

  sitemapEntries.push({
    url: `${baseUrl}/api/status`,
    lastModified: new Date(),
    changeFrequency: 'hourly',
    priority: 0.3
  });

  return sitemapEntries;
} 