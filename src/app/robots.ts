import { MetadataRoute } from 'next';
import { SITE_CONFIG } from '@/lib/seo';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = SITE_CONFIG.url;

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/about',
          '/blog',
          '/docs',
          '/privacy',
          '/terms',
          '/status',
          '/support',
          '/security',
          '/explore',
          '/learn',
          '/ar/',
          '/ar/about',
          '/ar/blog',
          '/ar/docs',
          '/ar/privacy',
          '/ar/terms',
          '/ar/status',
          '/ar/support',
          '/ar/security',
          '/ar/explore',
          '/ar/learn',
          '/api/health',
          '/api/status'
        ],
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/app/',
          '/ar/app/',
          '/*?*',
          '/dashboard',
          '/ar/dashboard'
        ]
      },
      // Specific rules for search engine bots
      {
        userAgent: ['Googlebot', 'Bingbot'],
        allow: [
          '/',
          '/api/health',
          '/api/status'
        ],
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/app/',
          '/ar/app/',
          '/dashboard',
          '/ar/dashboard'
        ],
        crawlDelay: 1
      }
    ],
    sitemap: [
      `${baseUrl}/sitemap.xml`
    ],
    host: baseUrl
  };
} 