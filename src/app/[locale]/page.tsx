'use client';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';
import SelfStorage from '@/components/SelfStorage';
import Footer from '@/components/Footer';
import StructuredData from '@/components/StructuredData';
import { generateStructuredData } from '@/lib/seo';

interface HomePageProps {
  params: { locale: string };
}

export default function Home({ params }: HomePageProps) {
  const locale = params.locale || 'en';
  
  // Generate structured data for the home page
  const websiteStructuredData = generateStructuredData('website');
  const organizationStructuredData = generateStructuredData('organization');
  const breadcrumbData = generateStructuredData('breadcrumb', {
    breadcrumbs: [
      { name: 'Home', url: '/' }
    ]
  });

  return (
    <>
      {/* Structured Data for Home Page */}
      <StructuredData data={websiteStructuredData} />
      <StructuredData data={organizationStructuredData} />
      <StructuredData data={breadcrumbData} />
      
      <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
        {/* Fixed Navbar */}
        <header className="fixed top-0 left-0 right-0 z-50 w-full">
          <Navbar />
        </header>

        {/* Main Content with Scroll Animations */}
        <div className="pt-16"> {/* Add padding to account for fixed navbar */}
          <Hero />
          <SelfStorage />
          <Footer />
        </div>
      </main>
    </>
  );
}
