'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import StructuredData from '@/components/StructuredData';
import { useLanguage } from '@/context/LanguageContext';
import { generateStructuredData } from '@/lib/seo';
import Link from 'next/link';

// Blog posts data
const blogPosts = [
  {
    id: 1,
    titleKey: 'blog.posts.1.title',
    excerptKey: 'blog.posts.1.excerpt',
    author<PERSON><PERSON>: 'blog.posts.1.author',
    date: 'May 5, 2024',
    category: 'blog.categories.announcements',
    image: '/placeholder-blog1.jpg',
    featured: true,
  },
  {
    id: 2,
    titleKey: 'blog.posts.2.title',
    excerptKey: 'blog.posts.2.excerpt',
    author<PERSON>ey: 'blog.posts.2.author',
    date: 'May 3, 2024',
    category: 'blog.categories.security',
    image: '/placeholder-blog2.jpg',
  },
  {
    id: 3,
    title<PERSON>ey: 'blog.posts.3.title',
    excerptKey: 'blog.posts.3.excerpt',
    author<PERSON><PERSON>: 'blog.posts.3.author',
    date: 'April 28, 2024',
    category: 'blog.categories.education',
    image: '/placeholder-blog3.jpg',
  },
  {
    id: 4,
    titleKey: 'blog.posts.4.title',
    excerptKey: 'blog.posts.4.excerpt',
    authorKey: 'blog.posts.4.author',
    date: 'April 22, 2024',
    category: 'blog.categories.marketInsights',
    image: '/placeholder-blog4.jpg',
  },
  {
    id: 5,
    titleKey: 'blog.posts.5.title',
    excerptKey: 'blog.posts.5.excerpt',
    authorKey: 'blog.posts.5.author',
    date: 'April 15, 2024',
    category: 'blog.categories.tutorials',
    image: '/placeholder-blog5.jpg',
  },
  {
    id: 6,
    titleKey: 'blog.posts.6.title',
    excerptKey: 'blog.posts.6.excerpt',
    authorKey: 'blog.posts.6.author',
    date: 'April 10, 2024',
    category: 'blog.categories.announcements',
    image: '/placeholder-blog6.jpg',
  },
];

// Blog categories
const categories = [
  'blog.categories.all',
  'blog.categories.announcements',
  'blog.categories.security',
  'blog.categories.education',
  'blog.categories.tutorials',
  'blog.categories.marketInsights',
];

export default function BlogPage() {
  const { t, isRTL } = useLanguage();
  
  // Generate structured data for the blog page
  const breadcrumbData = generateStructuredData('breadcrumb', {
    breadcrumbs: [
      { name: 'Home', url: '/' },
      { name: 'Blog', url: '/blog' }
    ]
  });

  // Generate structured data for blog articles
  const blogStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Mokhba Blog',
    description: 'Cryptocurrency news, tutorials, and insights from the Mokhba team',
    url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com'}/blog`,
    publisher: {
      '@type': 'Organization',
      name: 'Mokhba',
      logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com'}/logo.svg`
    },
    blogPost: blogPosts.map(post => ({
      '@type': 'BlogPosting',
      headline: t(post.titleKey),
      description: t(post.excerptKey),
      datePublished: new Date(post.date).toISOString(),
      author: {
        '@type': 'Person',
        name: t(post.authorKey)
      },
      publisher: {
        '@type': 'Organization',
        name: 'Mokhba',
        logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com'}/logo.svg`
      },
      articleSection: t(post.category),
      url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com'}/blog/${post.id}`,
      image: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com'}${post.image}`,
      keywords: [
        'cryptocurrency',
        'blockchain',
        'bitcoin',
        'ethereum',
        'wallet',
        'arabic',
        t(post.category).toLowerCase()
      ].join(', ')
    }))
  };

  // FAQ for blog page
  const blogFAQData = generateStructuredData('faq', {
    faqs: [
      {
        question: 'How often is the Mokhba blog updated?',
        answer: 'We publish new articles several times per week, covering the latest cryptocurrency news, tutorials, and market insights.'
      },
      {
        question: 'Can I submit a guest post to the Mokhba blog?',
        answer: 'Yes, we welcome guest contributions from cryptocurrency experts and community members. Please contact our editorial team for submission guidelines.'
      },
      {
        question: 'Are the blog articles available in Arabic?',
        answer: 'Yes, many of our articles are available in both English and Arabic to serve our diverse community.'
      }
    ]
  });
  
  return (
    <>
      {/* Structured Data for Blog Page */}
      <StructuredData data={breadcrumbData} />
      <StructuredData data={blogStructuredData} />
      <StructuredData data={blogFAQData} />
      
      <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
        {/* Fixed Navbar */}
        <header className="fixed top-0 left-0 right-0 z-50 w-full">
          <Navbar />
        </header>

        {/* Main Content with Scroll Animations */}
        <div className="pt-16"> {/* Add padding to account for fixed navbar */}
          <StaticPageLayout
            title={t('blog.title')}
            subtitle={t('blog.subtitle')}
            icon="article"
          >
        {/* Featured Post */}
        {blogPosts.filter(post => post.featured).map((post) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mt-12 bg-white rounded-xl overflow-hidden shadow-md"
          >
            <div className="md:flex">
              <div className="md:w-1/2 h-64 md:h-auto bg-gray-200 relative">
                <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                  <span className="material-symbols-outlined text-6xl text-white/80">
                    article
                  </span>
                </div>
              </div>
              <div className="md:w-1/2 p-8">
                <div className="flex items-center mb-4">
                  <span className="text-xs font-medium px-3 py-1 rounded-full bg-primary/10 text-primary">
                    {t(post.category)}
                  </span>
                  <span className="text-sm text-gray-500 ml-3">{post.date}</span>
                </div>
                <h2 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t(post.titleKey)}
                </h2>
                <p className={`text-blue-900/70 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t(post.excerptKey)}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        {t(post.authorKey).charAt(0)}
                      </span>
                    </div>
                    <span className="text-sm text-gray-600 ml-2">{t(post.authorKey)}</span>
                  </div>
                  <Link 
                    href={`/blog/${post.id}`} 
                    className="text-primary font-medium hover:text-primary/80 transition flex items-center"
                  >
                    {t('blog.readMore')}
                    <span className="material-symbols-outlined text-sm ml-1">arrow_forward</span>
                  </Link>
                </div>
              </div>
            </div>
          </motion.div>
        ))}

        {/* Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mt-16 flex flex-wrap gap-2"
        >
          {categories.map((category, index) => (
            <button
              key={index}
              className={`px-4 py-2 rounded-full text-sm font-medium ${
                category === 'blog.categories.all' 
                  ? 'bg-primary text-white' 
                  : 'bg-white text-blue-950 hover:bg-gray-100'
              } transition`}
            >
              {t(category)}
            </button>
          ))}
        </motion.div>

        {/* Blog Posts Grid */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {blogPosts.filter(post => !post.featured).map((post, index) => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 * index + 0.2 }}
              className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow"
            >
              <div className="h-48 bg-gray-200 relative">
                <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                  <span className="material-symbols-outlined text-4xl text-white/80">
                    article
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="flex items-center mb-3">
                  <span className="text-xs font-medium px-2 py-1 rounded-full bg-primary/10 text-primary">
                    {t(post.category)}
                  </span>
                  <span className="text-xs text-gray-500 ml-2">{post.date}</span>
                </div>
                <h3 className={`text-lg font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t(post.titleKey)}
                </h3>
                <p className={`text-blue-900/70 text-sm mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t(post.excerptKey)}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        {t(post.authorKey).charAt(0)}
                      </span>
                    </div>
                    <span className="text-xs text-gray-600 ml-2">{t(post.authorKey)}</span>
                  </div>
                  <Link 
                    href={`/blog/${post.id}`} 
                    className="text-primary text-sm font-medium hover:text-primary/80 transition flex items-center"
                  >
                    {t('blog.read')}
                    <span className="material-symbols-outlined text-xs ml-1">arrow_forward</span>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-16 bg-primary/5 rounded-xl p-8 border border-primary/10 text-center"
        >
          <h2 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('blog.newsletter.title')}
          </h2>
          <p className={`text-blue-900/70 mb-6 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
            {t('blog.newsletter.description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
            <input 
              type="email" 
              placeholder={t('blog.newsletter.placeholder')} 
              className="flex-1 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
            <button className="btn-primary whitespace-nowrap">
              {t('blog.newsletter.subscribe')}
            </button>
          </div>
        </motion.div>
          </StaticPageLayout>
          <Footer />
        </div>
      </main>
    </>
  );
}
