'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function NFTOwnershipPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Content for both languages
  const content = {
    en: {
      title: "NFTs & Digital Ownership",
      subtitle: "Understanding Non-Fungible Tokens, digital ownership rights, and the revolutionary impact on art, gaming, and digital assets",
      backToLearn: "Back to Learn",
      tableOfContents: "📋 Table of Contents",
      sections: {
        whatAreNFTs: {
          title: "What are NFTs?",
          content: [
            "Non-Fungible Tokens (NFTs) are unique digital assets that represent ownership of specific items or content on the blockchain. Unlike cryptocurrencies, each NFT is distinct and cannot be exchanged on a one-to-one basis.",
            "NFTs use blockchain technology to provide verifiable proof of ownership and authenticity for digital items, creating scarcity in the digital realm where perfect copies were previously possible.",
            "💡 Key Concept: NFTs don't store the actual digital content but rather contain metadata and links that point to where the content is hosted, along with ownership records."
          ],
          characteristics: [
            { title: "🔐 Unique Identity", desc: "Each NFT has a unique token ID and smart contract address that distinguishes it from all others" },
            { title: "🚫 Non-Fungible", desc: "Cannot be exchanged on a like-for-like basis; each token has distinct properties and value" },
            { title: "🔗 Blockchain Verified", desc: "Ownership and transaction history are permanently recorded on the blockchain" },
            { title: "📜 Smart Contract Powered", desc: "Governed by smart contracts that define creation, transfer, and ownership rules" },
            { title: "💰 Tradeable", desc: "Can be bought, sold, and transferred on various NFT marketplaces and platforms" },
            { title: "🎨 Programmable", desc: "Can include additional functionality like royalties, unlockable content, or utility features" }
          ]
        },
        nftStandards: {
          title: "NFT Standards & Technology",
          content: "NFTs are built on various blockchain standards that define their technical specifications:",
          standards: [
            {
              name: "ERC-721",
              blockchain: "Ethereum",
              description: "The original and most widely used NFT standard",
              features: ["Unique token IDs", "Basic ownership functions", "Transfer mechanisms", "Approval systems"],
              use_cases: ["Art NFTs", "Collectibles", "Domain names", "Identity tokens"]
            },
            {
              name: "ERC-1155",
              blockchain: "Ethereum",
              description: "Multi-token standard supporting both fungible and non-fungible tokens",
              features: ["Batch operations", "Gas efficiency", "Multiple token types", "Semi-fungible tokens"],
              use_cases: ["Gaming items", "Utility tokens", "Event tickets", "Membership cards"]
            },
            {
              name: "SPL Token",
              blockchain: "Solana",
              description: "Solana's token standard with low fees and fast transactions",
              features: ["Low transaction costs", "High throughput", "Fractional ownership", "Programmable royalties"],
              use_cases: ["Music NFTs", "Photography", "Digital collectibles", "Utility tokens"]
            },
            {
              name: "BEP-721",
              blockchain: "BSC",
              description: "Binance Smart Chain NFT standard with lower gas fees",
              features: ["EVM compatibility", "Lower fees", "Fast confirmation", "Cross-chain bridges"],
              use_cases: ["Gaming NFTs", "Art collections", "Sports memorabilia", "Virtual assets"]
            }
          ]
        },
        digitalOwnership: {
          title: "Digital Ownership Revolution",
          content: "NFTs fundamentally change how we think about ownership in the digital world:",
          concepts: [
            {
              concept: "Provable Ownership",
              explanation: "Blockchain provides immutable proof of who owns what digital asset",
              impact: "Eliminates disputes about digital asset ownership and authenticity",
              example: "Digital art collectors can prove they own the original even if copies exist everywhere"
            },
            {
              concept: "Transferable Rights",
              explanation: "Ownership can be easily transferred between parties without intermediaries",
              impact: "Creates liquid markets for previously non-transferable digital items",
              example: "Game items can be sold between players across different games and platforms"
            },
            {
              concept: "Programmable Royalties",
              explanation: "Creators can earn ongoing royalties from secondary sales automatically",
              impact: "Artists benefit from appreciation in value of their work over time",
              example: "Musicians earn 10% every time their NFT album is resold on any marketplace"
            },
            {
              concept: "Interoperability",
              explanation: "NFTs can work across different platforms and applications",
              impact: "Digital assets gain utility beyond their original platform",
              example: "Avatar NFTs can be used as profile pictures across social media and virtual worlds"
            }
          ]
        },
        nftUseCases: {
          title: "NFT Use Cases & Applications",
          content: "NFTs have found applications across numerous industries and use cases:",
          categories: [
            {
              category: "Digital Art & Collectibles",
              description: "Original digital artworks and collectible items",
              examples: ["CryptoPunks", "Bored Ape Yacht Club", "Art Blocks", "SuperRare artworks"],
              benefits: ["Artist monetization", "Collector ownership", "Provenance tracking", "Global accessibility"],
              considerations: ["Market volatility", "Storage concerns", "Copyright questions"]
            },
            {
              category: "Gaming & Virtual Worlds",
              description: "In-game items, characters, and virtual real estate",
              examples: ["Axie Infinity pets", "Decentraland land", "Gods Unchained cards", "The Sandbox assets"],
              benefits: ["True ownership", "Cross-game utility", "Player economies", "Asset trading"],
              considerations: ["Game dependency", "Technical integration", "Regulatory uncertainty"]
            },
            {
              category: "Music & Entertainment",
              description: "Albums, concert tickets, and exclusive content access",
              examples: ["Kings of Leon album", "Grimes art collection", "3LAU music NFTs", "Concert ticket NFTs"],
              benefits: ["Fan engagement", "Revenue streams", "Exclusive access", "Collectible value"],
              considerations: ["Distribution complexity", "Rights management", "Platform adoption"]
            },
            {
              category: "Identity & Credentials",
              description: "Digital identity, certificates, and professional credentials",
              examples: ["ENS domains", "Educational certificates", "Professional licenses", "Membership tokens"],
              benefits: ["Verifiable credentials", "Self-sovereign identity", "Portable achievements", "Fraud prevention"],
              considerations: ["Privacy concerns", "Technical barriers", "Adoption challenges"]
            },
            {
              category: "Real Estate & Physical Assets",
              description: "Tokenized real estate and physical item ownership",
              examples: ["Property deeds", "Luxury watches", "Wine collections", "Carbon credits"],
              benefits: ["Fractional ownership", "Global markets", "Reduced friction", "Transparent records"],
              considerations: ["Legal frameworks", "Physical-digital bridge", "Regulatory compliance"]
            },
            {
              category: "Utility & Access",
              description: "Tokens providing access to services, communities, or benefits",
              examples: ["Gym memberships", "Software licenses", "Community access", "Exclusive events"],
              benefits: ["Transferable access", "Community building", "Loyalty programs", "Exclusive benefits"],
              considerations: ["Utility sustainability", "Technical requirements", "User experience"]
            }
          ]
        },
        nftMarketplaces: {
          title: "NFT Marketplaces & Platforms",
          content: "Various platforms facilitate the creation, buying, selling, and trading of NFTs:",
          marketplaces: [
            {
              name: "OpenSea",
              type: "General Marketplace",
              blockchain: ["Ethereum", "Polygon", "Klaytn"],
              features: ["Largest selection", "Easy minting", "Collection management", "Auction system"],
              fees: "2.5% platform fee",
              specialty: "Comprehensive NFT marketplace with broad category support"
            },
            {
              name: "SuperRare",
              type: "Curated Art Platform",
              blockchain: ["Ethereum"],
              features: ["Curated artists", "High-quality art", "Social features", "Artist royalties"],
              fees: "3% platform fee + 15% primary sales",
              specialty: "Premium digital art platform with selective artist approval"
            },
            {
              name: "Foundation",
              type: "Art & Culture",
              blockchain: ["Ethereum"],
              features: ["Invite-only artists", "Auction format", "Creator tools", "Community curation"],
              fees: "5% platform fee",
              specialty: "Exclusive platform focusing on digital art and cultural significance"
            },
            {
              name: "Rarible",
              type: "Community Marketplace",
              blockchain: ["Ethereum", "Flow", "Polygon"],
              features: ["RARI governance token", "Lazy minting", "Collection creation", "Royalty enforcement"],
              fees: "2.5% platform fee",
              specialty: "Community-governed marketplace with creator-friendly features"
            },
            {
              name: "Magic Eden",
              type: "Solana Marketplace",
              blockchain: ["Solana"],
              features: ["Low fees", "Fast transactions", "Launchpad", "Analytics tools"],
              fees: "2% platform fee",
              specialty: "Leading Solana NFT marketplace with gaming focus"
            },
            {
              name: "NBA Top Shot",
              type: "Sports Collectibles",
              blockchain: ["Flow"],
              features: ["Official NBA license", "Moment highlights", "Pack drops", "Challenges"],
              fees: "5% marketplace fee",
              specialty: "Official NBA collectible moments and highlights"
            }
          ]
        },
        creatingNFTs: {
          title: "Creating & Minting NFTs",
          content: "Step-by-step process for creating and launching your own NFTs:",
          steps: [
            {
              step: "1. Concept & Content Creation",
              description: "Develop your NFT concept and create the digital content",
              details: [
                "Define your artistic vision or utility purpose",
                "Create high-quality digital content (art, music, video, etc.)",
                "Consider file formats and technical requirements",
                "Plan collection size and rarity distribution"
              ],
              tools: ["Photoshop", "Procreate", "Blender", "Adobe After Effects"],
              timeframe: "1-4 weeks"
            },
            {
              step: "2. Choose Blockchain & Marketplace",
              description: "Select the blockchain and platform for your NFT",
              details: [
                "Research different blockchain options (Ethereum, Solana, Polygon)",
                "Compare gas fees and transaction speeds",
                "Evaluate marketplace features and audience",
                "Consider long-term platform sustainability"
              ],
              tools: ["OpenSea", "Foundation", "SuperRare", "Magic Eden"],
              timeframe: "1-2 days"
            },
            {
              step: "3. Set Up Wallet & Fund Account",
              description: "Prepare your crypto wallet and acquire necessary tokens",
              details: [
                "Install and set up a compatible wallet (MetaMask, Phantom)",
                "Purchase cryptocurrency for gas fees",
                "Connect wallet to chosen marketplace",
                "Test with small transactions first"
              ],
              tools: ["MetaMask", "Phantom", "WalletConnect", "Coinbase Wallet"],
              timeframe: "1 day"
            },
            {
              step: "4. Mint Your NFT",
              description: "Upload content and create your NFT on the blockchain",
              details: [
                "Upload your digital content to IPFS or marketplace storage",
                "Fill in metadata (name, description, properties)",
                "Set royalty percentages for future sales",
                "Review and confirm minting transaction"
              ],
              tools: ["IPFS", "Marketplace interfaces", "Metadata editors"],
              timeframe: "1-2 hours"
            },
            {
              step: "5. Marketing & Community Building",
              description: "Promote your NFT and build a community around your work",
              details: [
                "Create social media presence and content",
                "Engage with NFT communities and collectors",
                "Share creation process and artist story",
                "Consider collaborations and cross-promotions"
              ],
              tools: ["Twitter", "Discord", "Instagram", "Clubhouse"],
              timeframe: "Ongoing"
            }
          ]
        },
        nftInvesting: {
          title: "NFT Investment & Collecting",
          content: "Understanding the investment landscape and collecting strategies:",
          strategies: [
            {
              strategy: "Blue-Chip Collections",
              description: "Invest in established, high-value NFT collections",
              examples: ["CryptoPunks", "Bored Ape Yacht Club", "Art Blocks Curated"],
              pros: ["Established value", "Strong communities", "Historical significance", "Liquidity"],
              cons: ["High entry costs", "Limited upside", "Market concentration risk"],
              risk_level: "Medium"
            },
            {
              strategy: "Emerging Artists",
              description: "Support and invest in up-and-coming digital artists",
              examples: ["New artists on Foundation", "Emerging SuperRare creators", "Social media discoveries"],
              pros: ["High growth potential", "Artist support", "Lower entry costs", "First-mover advantage"],
              cons: ["High risk", "Limited liquidity", "Uncertain value", "Due diligence required"],
              risk_level: "High"
            },
            {
              strategy: "Utility NFTs",
              description: "Focus on NFTs that provide ongoing utility or benefits",
              examples: ["Gaming items", "Membership tokens", "Access passes", "Yield-generating NFTs"],
              pros: ["Intrinsic value", "Utility beyond speculation", "Regular benefits", "Growing market"],
              cons: ["Platform dependency", "Technical risks", "Evolving standards"],
              risk_level: "Medium"
            },
            {
              strategy: "Fractional Ownership",
              description: "Participate in high-value NFTs through fractional ownership platforms",
              examples: ["Fractional.art", "NIFTEX", "Unicly", "Party Bid"],
              pros: ["Access to expensive NFTs", "Diversification", "Shared costs", "Community governance"],
              cons: ["Complexity", "Liquidity issues", "Governance disputes", "Platform risks"],
              risk_level: "Medium-High"
            }
          ],
          valuation_factors: [
            { factor: "Artist Reputation", impact: "High", description: "Recognition and track record of the creator" },
            { factor: "Rarity", impact: "High", description: "Scarcity within collection and unique attributes" },
            { factor: "Utility", impact: "Medium", description: "Functionality beyond artistic value" },
            { factor: "Community", impact: "Medium", description: "Strength and engagement of holder community" },
            { factor: "Historical Significance", impact: "Medium", description: "Cultural or technological importance" },
            { factor: "Technical Quality", impact: "Low-Medium", description: "Artistic execution and technical specifications" }
          ]
        },
        nftRisks: {
          title: "NFT Risks & Considerations",
          content: "Understanding the risks and challenges in the NFT space:",
          risk_categories: [
            {
              category: "Market & Financial Risks",
              risks: [
                "Extreme price volatility and speculative bubbles",
                "Illiquid markets with difficulty selling",
                "Pump and dump schemes and market manipulation",
                "Celebrity and influencer-driven hype cycles"
              ],
              mitigation: [
                "Only invest what you can afford to lose",
                "Research thoroughly before purchasing",
                "Diversify across different types and creators",
                "Avoid FOMO-driven decisions"
              ]
            },
            {
              category: "Technical & Platform Risks",
              risks: [
                "Smart contract bugs and vulnerabilities",
                "Marketplace shutdowns or changes",
                "Metadata and content hosting failures",
                "Blockchain network issues and high gas fees"
              ],
              mitigation: [
                "Use reputable platforms and wallets",
                "Understand smart contract risks",
                "Backup important information",
                "Monitor platform developments"
              ]
            },
            {
              category: "Legal & Regulatory Risks",
              risks: [
                "Unclear copyright and intellectual property rights",
                "Evolving regulatory landscape and potential restrictions",
                "Tax implications and reporting requirements",
                "Cross-border legal complications"
              ],
              mitigation: [
                "Understand licensing terms and rights",
                "Stay informed about regulations",
                "Keep detailed transaction records",
                "Consult legal and tax professionals"
              ]
            },
            {
              category: "Fraud & Security Risks",
              risks: [
                "Counterfeit and plagiarized NFTs",
                "Phishing attacks and wallet compromises",
                "Fake marketplaces and scam projects",
                "Rug pulls and abandoned projects"
              ],
              mitigation: [
                "Verify authenticity and creator identity",
                "Use secure wallets and practices",
                "Research projects and teams thoroughly",
                "Be cautious of too-good-to-be-true offers"
              ]
            }
          ]
        },
        futureOfNFTs: {
          title: "Future of NFTs & Digital Ownership",
          content: "Emerging trends and future developments in the NFT space:",
          trends: [
            {
              trend: "Utility-First NFTs",
              description: "Shift from speculative art to functional digital assets",
              implications: "More sustainable value propositions and real-world applications",
              timeline: "2024-2026"
            },
            {
              trend: "Cross-Chain Interoperability",
              description: "NFTs that work across multiple blockchain networks",
              implications: "Increased liquidity and reduced platform lock-in",
              timeline: "2024-2025"
            },
            {
              trend: "AI-Generated Content",
              description: "Integration of AI tools in NFT creation and curation",
              implications: "New creative possibilities and ownership questions",
              timeline: "2024-2026"
            },
            {
              trend: "Enterprise Adoption",
              description: "Companies using NFTs for loyalty, access, and brand engagement",
              implications: "Mainstream adoption and new business models",
              timeline: "2024-2027"
            },
            {
              trend: "Regulatory Clarity",
              description: "Clearer legal frameworks for NFT ownership and trading",
              implications: "Increased institutional participation and legitimacy",
              timeline: "2025-2028"
            },
            {
              trend: "Environmental Solutions",
              description: "Carbon-neutral and eco-friendly NFT platforms",
              implications: "Addressing sustainability concerns and ESG compliance",
              timeline: "2024-2025"
            },
            {
              trend: "Metaverse Integration",
              description: "NFTs as core infrastructure for virtual worlds",
              implications: "New digital economies and virtual asset classes",
              timeline: "2025-2030"
            },
            {
              trend: "Physical-Digital Bridge",
              description: "NFTs representing and controlling physical assets",
              implications: "Tokenization of real-world assets and hybrid ownership",
              timeline: "2026-2030"
            }
          ]
        },
        cta: {
          title: "Explore Digital Ownership",
          desc: "Start your journey into NFTs and digital ownership with Mokhba Wallet's integrated NFT features and secure storage.",
          button: "Discover NFTs"
        }
      }
    },
    ar: {
      title: "الرموز غير القابلة للاستبدال والملكية الرقمية",
      subtitle: "فهم الرموز غير القابلة للاستبدال، حقوق الملكية الرقمية، والتأثير الثوري على الفن والألعاب والأصول الرقمية",
      backToLearn: "العودة إلى التعلم",
      tableOfContents: "📋 فهرس المحتويات",
      sections: {
        whatAreNFTs: {
          title: "ما هي الرموز غير القابلة للاستبدال (NFTs)؟",
          content: [
            "الرموز غير القابلة للاستبدال (NFTs) هي أصول رقمية فريدة تمثل ملكية عناصر أو محتوى محدد على البلوك تشين. على عكس العملات المشفرة، كل NFT مميز ولا يمكن تبادله على أساس واحد لواحد.",
            "تستخدم NFTs تقنية البلوك تشين لتوفير إثبات قابل للتحقق من الملكية والأصالة للعناصر الرقمية، مما يخلق ندرة في العالم الرقمي حيث كانت النسخ المثالية ممكنة سابقاً.",
            "💡 المفهوم الأساسي: لا تخزن NFTs المحتوى الرقمي الفعلي ولكنها تحتوي على بيانات وصفية وروابط تشير إلى مكان استضافة المحتوى، إلى جانب سجلات الملكية."
          ],
          characteristics: [
            { title: "🔐 هوية فريدة", desc: "كل NFT له معرف رمز فريد وعنوان عقد ذكي يميزه عن جميع الآخرين" },
            { title: "🚫 غير قابل للاستبدال", desc: "لا يمكن تبادله على أساس مماثل؛ كل رمز له خصائص وقيمة مميزة" },
            { title: "🔗 مُحقق من البلوك تشين", desc: "الملكية وتاريخ المعاملات مسجلان بشكل دائم على البلوك تشين" },
            { title: "📜 مدعوم بعقد ذكي", desc: "محكوم بعقود ذكية تحدد قواعد الإنشاء والنقل والملكية" },
            { title: "💰 قابل للتداول", desc: "يمكن شراؤه وبيعه ونقله في أسواق ومنصات NFT المختلفة" },
            { title: "🎨 قابل للبرمجة", desc: "يمكن أن يتضمن وظائف إضافية مثل الإتاوات أو المحتوى القابل للفتح أو ميزات المنفعة" }
          ]
        },
        nftStandards: {
          title: "معايير وتقنيات NFT",
          content: "تُبنى NFTs على معايير مختلفة للبلوك تشين تحدد مواصفاتها التقنية:",
          standards: [
            {
              name: "ERC-721",
              blockchain: "إيثريوم",
              description: "المعيار الأصلي والأكثر استخداماً لـ NFT",
              features: ["معرفات رمز فريدة", "وظائف الملكية الأساسية", "آليات النقل", "أنظمة الموافقة"],
              use_cases: ["NFTs الفنية", "المقتنيات", "أسماء النطاقات", "رموز الهوية"]
            },
            {
              name: "ERC-1155",
              blockchain: "إيثريوم",
              description: "معيار متعدد الرموز يدعم الرموز القابلة وغير القابلة للاستبدال",
              features: ["عمليات دفعية", "كفاءة الغاز", "أنواع رموز متعددة", "رموز شبه قابلة للاستبدال"],
              use_cases: ["عناصر الألعاب", "رموز المنفعة", "تذاكر الأحداث", "بطاقات العضوية"]
            },
            {
              name: "SPL Token",
              blockchain: "سولانا",
              description: "معيار رمز سولانا برسوم منخفضة ومعاملات سريعة",
              features: ["تكاليف معاملات منخفضة", "إنتاجية عالية", "ملكية جزئية", "إتاوات قابلة للبرمجة"],
              use_cases: ["NFTs موسيقية", "التصوير الفوتوغرافي", "المقتنيات الرقمية", "رموز المنفعة"]
            },
            {
              name: "BEP-721",
              blockchain: "BSC",
              description: "معيار NFT لبينانس سمارت تشين برسوم غاز أقل",
              features: ["توافق EVM", "رسوم أقل", "تأكيد سريع", "جسور عبر السلاسل"],
              use_cases: ["NFTs الألعاب", "مجموعات فنية", "تذكارات رياضية", "أصول افتراضية"]
            }
          ]
        },
        digitalOwnership: {
          title: "ثورة الملكية الرقمية",
          content: "تغير NFTs بشكل جذري كيف نفكر في الملكية في العالم الرقمي:",
          concepts: [
            {
              concept: "الملكية القابلة للإثبات",
              explanation: "يوفر البلوك تشين إثباتاً غير قابل للتغيير لمن يملك أي أصل رقمي",
              impact: "يلغي النزاعات حول ملكية وأصالة الأصول الرقمية",
              example: "يمكن لجامعي الفن الرقمي إثبات ملكيتهم للأصل حتى لو كانت النسخ موجودة في كل مكان"
            },
            {
              concept: "الحقوق القابلة للنقل",
              explanation: "يمكن نقل الملكية بسهولة بين الأطراف دون وسطاء",
              impact: "ينشئ أسواقاً سائلة للعناصر الرقمية غير القابلة للنقل سابقاً",
              example: "يمكن بيع عناصر الألعاب بين اللاعبين عبر ألعاب ومنصات مختلفة"
            },
            {
              concept: "الإتاوات القابلة للبرمجة",
              explanation: "يمكن للمبدعين كسب إتاوات مستمرة من المبيعات الثانوية تلقائياً",
              impact: "يستفيد الفنانون من تقدير قيمة أعمالهم مع مرور الوقت",
              example: "يكسب الموسيقيون 10% في كل مرة يُعاد بيع ألبوم NFT الخاص بهم في أي سوق"
            },
            {
              concept: "قابلية التشغيل البيني",
              explanation: "يمكن أن تعمل NFTs عبر منصات وتطبيقات مختلفة",
              impact: "تكتسب الأصول الرقمية منفعة تتجاوز منصتها الأصلية",
              example: "يمكن استخدام NFTs الصورة الرمزية كصور شخصية عبر وسائل التواصل الاجتماعي والعوالم الافتراضية"
            }
          ]
        },
        nftUseCases: {
          title: "حالات الاستخدام والتطبيقات لـ NFT",
          content: "وجدت NFTs تطبيقات عبر صناعات وحالات استخدام عديدة:",
          categories: [
            {
              category: "الفن الرقمي والمقتنيات",
              description: "الأعمال الفنية الرقمية الأصلية والعناصر القابلة للجمع",
              examples: ["CryptoPunks", "Bored Ape Yacht Club", "Art Blocks", "أعمال SuperRare"],
              benefits: ["تحقيق الدخل للفنانين", "ملكية الجامعين", "تتبع المصدر", "إمكانية الوصول العالمي"],
              considerations: ["تقلبات السوق", "مخاوف التخزين", "أسئلة حقوق النشر"]
            },
            {
              category: "الألعاب والعوالم الافتراضية",
              description: "عناصر داخل اللعبة والشخصيات والعقارات الافتراضية",
              examples: ["حيوانات Axie Infinity", "أراضي Decentraland", "بطاقات Gods Unchained", "أصول The Sandbox"],
              benefits: ["ملكية حقيقية", "منفعة عبر الألعاب", "اقتصاديات اللاعبين", "تداول الأصول"],
              considerations: ["اعتمادية اللعبة", "التكامل التقني", "عدم اليقين التنظيمي"]
            },
            {
              category: "الموسيقى والترفيه",
              description: "الألبومات وتذاكر الحفلات والوصول للمحتوى الحصري",
              examples: ["ألبوم Kings of Leon", "مجموعة Grimes الفنية", "NFTs موسيقى 3LAU", "تذاكر حفلات NFT"],
              benefits: ["مشاركة المعجبين", "تدفقات الإيرادات", "وصول حصري", "قيمة قابلة للجمع"],
              considerations: ["تعقيد التوزيع", "إدارة الحقوق", "تبني المنصة"]
            },
            {
              category: "الهوية والشهادات",
              description: "الهوية الرقمية والشهادات والأوراق الاعتماد المهنية",
              examples: ["نطاقات ENS", "الشهادات التعليمية", "التراخيص المهنية", "رموز العضوية"],
              benefits: ["أوراق اعتماد قابلة للتحقق", "هوية ذاتية السيادة", "إنجازات محمولة", "منع الاحتيال"],
              considerations: ["مخاوف الخصوصية", "حواجز تقنية", "تحديات التبني"]
            },
            {
              category: "العقارات والأصول المادية",
              description: "العقارات المرمزة وملكية الأشياء المادية",
              examples: ["صكوك الملكية", "الساعات الفاخرة", "مجموعات النبيذ", "اعتمادات الكربون"],
              benefits: ["ملكية جزئية", "أسواق عالمية", "احتكاك مخفض", "سجلات شفافة"],
              considerations: ["الأطر القانونية", "جسر المادي-الرقمي", "الامتثال التنظيمي"]
            },
            {
              category: "المنفعة والوصول",
              description: "رموز توفر الوصول للخدمات أو المجتمعات أو الفوائد",
              examples: ["عضويات النادي الرياضي", "تراخيص البرامج", "وصول المجتمع", "الأحداث الحصرية"],
              benefits: ["وصول قابل للنقل", "بناء المجتمع", "برامج الولاء", "فوائد حصرية"],
              considerations: ["استدامة المنفعة", "متطلبات تقنية", "تجربة المستخدم"]
            }
          ]
        },
        nftMarketplaces: {
          title: "أسواق ومنصات NFT",
          content: "منصات مختلفة تسهل إنشاء وشراء وبيع وتداول NFTs:",
          marketplaces: [
            {
              name: "OpenSea",
              type: "سوق عام",
              blockchain: ["إيثريوم", "بوليغون", "كلايتن"],
              features: ["أكبر مجموعة", "سك سهل", "إدارة المجموعات", "نظام المزاد"],
              fees: "رسوم منصة 2.5%",
              specialty: "سوق NFT شامل بدعم فئات واسعة"
            },
            {
              name: "SuperRare",
              type: "منصة فنية منتقاة",
              blockchain: ["إيثريوم"],
              features: ["فنانون منتقون", "فن عالي الجودة", "ميزات اجتماعية", "إتاوات الفنانين"],
              fees: "رسوم منصة 3% + 15% مبيعات أولية",
              specialty: "منصة فن رقمي متميزة بموافقة انتقائية للفنانين"
            },
            {
              name: "Foundation",
              type: "الفن والثقافة",
              blockchain: ["إيثريوم"],
              features: ["فنانون بدعوة فقط", "صيغة مزاد", "أدوات المبدع", "تنسيق المجتمع"],
              fees: "رسوم منصة 5%",
              specialty: "منصة حصرية تركز على الفن الرقمي والأهمية الثقافية"
            },
            {
              name: "Rarible",
              type: "سوق مجتمعي",
              blockchain: ["إيثريوم", "فلو", "بوليغون"],
              features: ["رمز حوكمة RARI", "سك كسول", "إنشاء مجموعات", "إنفاذ الإتاوات"],
              fees: "رسوم منصة 2.5%",
              specialty: "سوق محكوم من المجتمع بميزات صديقة للمبدعين"
            },
            {
              name: "Magic Eden",
              type: "سوق سولانا",
              blockchain: ["سولانا"],
              features: ["رسوم منخفضة", "معاملات سريعة", "منصة إطلاق", "أدوات تحليل"],
              fees: "رسوم منصة 2%",
              specialty: "سوق NFT رائد في سولانا بتركيز على الألعاب"
            },
            {
              name: "NBA Top Shot",
              type: "مقتنيات رياضية",
              blockchain: ["فلو"],
              features: ["ترخيص NBA رسمي", "لحظات مميزة", "إسقاطات الحزم", "تحديات"],
              fees: "رسوم سوق 5%",
              specialty: "لحظات ومقاطع NBA الرسمية القابلة للجمع"
            }
          ]
        },
        creatingNFTs: {
          title: "إنشاء وسك NFTs",
          content: "عملية خطوة بخطوة لإنشاء وإطلاق NFTs الخاصة بك:",
          steps: [
            {
              step: "1. تطوير المفهوم وإنشاء المحتوى",
              description: "طور مفهوم NFT الخاص بك وأنشئ المحتوى الرقمي",
              details: [
                "حدد رؤيتك الفنية أو غرض المنفعة",
                "أنشئ محتوى رقمي عالي الجودة (فن، موسيقى، فيديو، إلخ)",
                "اعتبر صيغ الملفات والمتطلبات التقنية",
                "خطط لحجم المجموعة وتوزيع الندرة"
              ],
              tools: ["فوتوشوب", "Procreate", "Blender", "Adobe After Effects"],
              timeframe: "1-4 أسابيع"
            },
            {
              step: "2. اختيار البلوك تشين والسوق",
              description: "اختر البلوك تشين والمنصة لـ NFT الخاص بك",
              details: [
                "ابحث في خيارات البلوك تشين المختلفة (إيثريوم، سولانا، بوليغون)",
                "قارن رسوم الغاز وسرعات المعاملات",
                "قيم ميزات السوق والجمهور",
                "اعتبر استدامة المنصة طويلة المدى"
              ],
              tools: ["OpenSea", "Foundation", "SuperRare", "Magic Eden"],
              timeframe: "1-2 أيام"
            },
            {
              step: "3. إعداد المحفظة وتمويل الحساب",
              description: "حضر محفظة العملة المشفرة واحصل على الرموز اللازمة",
              details: [
                "ثبت وأعد محفظة متوافقة (MetaMask، Phantom)",
                "اشتر العملة المشفرة لرسوم الغاز",
                "اربط المحفظة بالسوق المختار",
                "اختبر بمعاملات صغيرة أولاً"
              ],
              tools: ["MetaMask", "Phantom", "WalletConnect", "Coinbase Wallet"],
              timeframe: "يوم واحد"
            },
            {
              step: "4. سك NFT الخاص بك",
              description: "ارفع المحتوى وأنشئ NFT الخاص بك على البلوك تشين",
              details: [
                "ارفع محتواك الرقمي إلى IPFS أو تخزين السوق",
                "املأ البيانات الوصفية (الاسم، الوصف، الخصائص)",
                "حدد نسب الإتاوات للمبيعات المستقبلية",
                "راجع وأكد معاملة السك"
              ],
              tools: ["IPFS", "واجهات السوق", "محررات البيانات الوصفية"],
              timeframe: "1-2 ساعات"
            },
            {
              step: "5. التسويق وبناء المجتمع",
              description: "روج لـ NFT الخاص بك وابن مجتمعاً حول عملك",
              details: [
                "أنشئ حضوراً ومحتوى على وسائل التواصل الاجتماعي",
                "تفاعل مع مجتمعات NFT والجامعين",
                "شارك عملية الإنشاء وقصة الفنان",
                "فكر في التعاونات والترويج المتبادل"
              ],
              tools: ["تويتر", "ديسكورد", "إنستغرام", "Clubhouse"],
              timeframe: "مستمر"
            }
          ]
        },
        nftInvesting: {
          title: "الاستثمار والجمع في NFT",
          content: "فهم مشهد الاستثمار واستراتيجيات الجمع:",
          strategies: [
            {
              strategy: "مجموعات الدرجة الأولى",
              description: "استثمر في مجموعات NFT راسخة وعالية القيمة",
              examples: ["CryptoPunks", "Bored Ape Yacht Club", "Art Blocks Curated"],
              pros: ["قيمة راسخة", "مجتمعات قوية", "أهمية تاريخية", "سيولة"],
              cons: ["تكاليف دخول عالية", "نمو محدود", "مخاطر تركز السوق"],
              risk_level: "متوسط"
            },
            {
              strategy: "الفنانون الناشئون",
              description: "ادعم واستثمر في الفنانين الرقميين الصاعدين",
              examples: ["فنانون جدد على Foundation", "مبدعون ناشئون في SuperRare", "اكتشافات وسائل التواصل الاجتماعي"],
              pros: ["إمكانية نمو عالية", "دعم الفنانين", "تكاليف دخول أقل", "ميزة الرائد"],
              cons: ["مخاطر عالية", "سيولة محدودة", "قيمة غير مؤكدة", "مطلوب العناية الواجبة"],
              risk_level: "عالي"
            },
            {
              strategy: "NFTs المنفعة",
              description: "ركز على NFTs التي توفر منفعة أو فوائد مستمرة",
              examples: ["عناصر الألعاب", "رموز العضوية", "تصاريح الوصول", "NFTs مولدة للعائد"],
              pros: ["قيمة جوهرية", "منفعة تتجاوز المضاربة", "فوائد منتظمة", "سوق متنامي"],
              cons: ["اعتمادية المنصة", "مخاطر تقنية", "معايير متطورة"],
              risk_level: "متوسط"
            },
            {
              strategy: "الملكية الجزئية",
              description: "شارك في NFTs عالية القيمة من خلال منصات الملكية الجزئية",
              examples: ["Fractional.art", "NIFTEX", "Unicly", "Party Bid"],
              pros: ["وصول لـ NFTs باهظة الثمن", "تنويع", "تكاليف مشتركة", "حوكمة مجتمعية"],
              cons: ["تعقيد", "مشاكل سيولة", "نزاعات حوكمة", "مخاطر منصة"],
              risk_level: "متوسط-عالي"
            }
          ],
          valuation_factors: [
            { factor: "سمعة الفنان", impact: "عالي", description: "اعتراف وسجل حافل للمبدع" },
            { factor: "الندرة", impact: "عالي", description: "الندرة داخل المجموعة والصفات الفريدة" },
            { factor: "المنفعة", impact: "متوسط", description: "الوظيفة تتجاوز القيمة الفنية" },
            { factor: "المجتمع", impact: "متوسط", description: "قوة ومشاركة مجتمع الحائزين" },
            { factor: "الأهمية التاريخية", impact: "متوسط", description: "الأهمية الثقافية أو التكنولوجية" },
            { factor: "الجودة التقنية", impact: "منخفض-متوسط", description: "التنفيذ الفني والمواصفات التقنية" }
          ]
        },
        nftRisks: {
          title: "مخاطر واعتبارات NFT",
          content: "فهم المخاطر والتحديات في مجال NFT:",
          risk_categories: [
            {
              category: "مخاطر السوق والمالية",
              risks: [
                "تقلبات سعرية شديدة وفقاعات مضاربة",
                "أسواق غير سائلة مع صعوبة في البيع",
                "مخططات الضخ والتفريغ وتلاعب السوق",
                "دورات الضجيج المدفوعة بالمشاهير والمؤثرين"
              ],
              mitigation: [
                "استثمر فقط ما يمكنك تحمل خسارته",
                "ابحث بدقة قبل الشراء",
                "نوع عبر أنواع ومبدعين مختلفين",
                "تجنب القرارات المدفوعة بـ FOMO"
              ]
            },
            {
              category: "المخاطر التقنية ومخاطر المنصة",
              risks: [
                "أخطاء وثغرات العقود الذكية",
                "إغلاق أو تغييرات الأسواق",
                "فشل استضافة البيانات الوصفية والمحتوى",
                "مشاكل شبكة البلوك تشين ورسوم غاز عالية"
              ],
              mitigation: [
                "استخدم منصات ومحافظ موثوقة",
                "افهم مخاطر العقود الذكية",
                "انسخ المعلومات المهمة احتياطياً",
                "راقب تطورات المنصة"
              ]
            },
            {
              category: "المخاطر القانونية والتنظيمية",
              risks: [
                "حقوق النشر والملكية الفكرية غير الواضحة",
                "المشهد التنظيمي المتطور والقيود المحتملة",
                "التداعيات الضريبية ومتطلبات الإبلاغ",
                "تعقيدات قانونية عبر الحدود"
              ],
              mitigation: [
                "افهم شروط الترخيص والحقوق",
                "ابق على علم باللوائح",
                "احتفظ بسجلات معاملات مفصلة",
                "استشر المهنيين القانونيين والضريبيين"
              ]
            },
            {
              category: "مخاطر الاحتيال والأمان",
              risks: [
                "NFTs مقلدة ومسروقة",
                "هجمات التصيد واختراق المحافظ",
                "أسواق مزيفة ومشاريع احتيالية",
                "عمليات سحب البساط والمشاريع المهجورة"
              ],
              mitigation: [
                "تحقق من الأصالة وهوية المبدع",
                "استخدم محافظ وممارسات آمنة",
                "ابحث في المشاريع والفرق بدقة",
                "كن حذراً من العروض الجيدة جداً لدرجة عدم التصديق"
              ]
            }
          ]
        },
        futureOfNFTs: {
          title: "مستقبل NFTs والملكية الرقمية",
          content: "الاتجاهات الناشئة والتطورات المستقبلية في مجال NFT:",
          trends: [
            {
              trend: "NFTs المنفعة أولاً",
              description: "التحول من الفن المضارب إلى الأصول الرقمية الوظيفية",
              implications: "عروض قيمة أكثر استدامة وتطبيقات في العالم الحقيقي",
              timeline: "2024-2026"
            },
            {
              trend: "قابلية التشغيل البيني عبر السلاسل",
              description: "NFTs تعمل عبر شبكات بلوك تشين متعددة",
              implications: "زيادة السيولة وتقليل قفل المنصة",
              timeline: "2024-2025"
            },
            {
              trend: "المحتوى المولد بالذكاء الاصطناعي",
              description: "دمج أدوات الذكاء الاصطناعي في إنشاء وتنسيق NFT",
              implications: "إمكانيات إبداعية جديدة وأسئلة الملكية",
              timeline: "2024-2026"
            },
            {
              trend: "التبني المؤسسي",
              description: "الشركات تستخدم NFTs للولاء والوصول ومشاركة العلامة التجارية",
              implications: "التبني السائد ونماذج أعمال جديدة",
              timeline: "2024-2027"
            },
            {
              trend: "وضوح تنظيمي",
              description: "أطر قانونية أوضح لملكية وتداول NFT",
              implications: "زيادة المشاركة المؤسسية والشرعية",
              timeline: "2025-2028"
            },
            {
              trend: "الحلول البيئية",
              description: "منصات NFT محايدة الكربون وصديقة للبيئة",
              implications: "معالجة مخاوف الاستدامة والامتثال لـ ESG",
              timeline: "2024-2025"
            },
            {
              trend: "تكامل الميتافيرس",
              description: "NFTs كبنية تحتية أساسية للعوالم الافتراضية",
              implications: "اقتصاديات رقمية جديدة وفئات أصول افتراضية",
              timeline: "2025-2030"
            },
            {
              trend: "جسر المادي-الرقمي",
              description: "NFTs تمثل وتتحكم في الأصول المادية",
              implications: "رمزنة أصول العالم الحقيقي والملكية الهجينة",
              timeline: "2026-2030"
            }
          ]
        },
        cta: {
          title: "استكشف الملكية الرقمية",
          desc: "ابدأ رحلتك في NFTs والملكية الرقمية مع ميزات NFT المدمجة في محفظة مخبة والتخزين الآمن.",
          button: "اكتشف NFTs"
        }
      }
    }
  };

  const currentContent = content[locale as 'en' | 'ar'] || content.en;

  return (
    <main className={`relative bg-gradient-to-b from-white to-[#73AED2] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content */}
      <div className="pt-24 pb-16 min-h-screen">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <Link 
              href={`/${locale}/learn`}
              className="text-primary hover:text-primary/80 flex items-center mb-4"
            >
              <span className={`material-symbols-outlined ${isRTL ? 'ml-2 scale-x-[-1]' : 'mr-2'}`}>arrow_back</span>
              {currentContent.backToLearn}
            </Link>
          </nav>

          {/* Article Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-6">
              <span className="material-symbols-outlined text-3xl text-primary">palette</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-blue-950 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-blue-900/70 max-w-2xl mx-auto">
              {currentContent.subtitle}
            </p>
            <div className="flex items-center justify-center gap-4 mt-4 text-sm text-blue-900/60">
              <span>🎨 {locale === 'ar' ? 'رائج' : 'Trending'}</span>
              <span>•</span>
              <span>⏱️ {locale === 'ar' ? '20 دقيقة قراءة' : '20 min read'}</span>
            </div>
          </motion.div>

          {/* Article Content */}
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            {/* Table of Contents */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-bold text-blue-950 mb-4">{currentContent.tableOfContents}</h3>
              <ul className="space-y-2 text-blue-900">
                <li><a href="#what-are-nfts" className="hover:text-primary transition">1. {currentContent.sections.whatAreNFTs.title}</a></li>
                <li><a href="#nft-standards" className="hover:text-primary transition">2. {currentContent.sections.nftStandards.title}</a></li>
                <li><a href="#digital-ownership" className="hover:text-primary transition">3. {currentContent.sections.digitalOwnership.title}</a></li>
                <li><a href="#nft-use-cases" className="hover:text-primary transition">4. {currentContent.sections.nftUseCases.title}</a></li>
                <li><a href="#nft-marketplaces" className="hover:text-primary transition">5. {currentContent.sections.nftMarketplaces.title}</a></li>
                <li><a href="#creating-nfts" className="hover:text-primary transition">6. {currentContent.sections.creatingNFTs.title}</a></li>
                <li><a href="#nft-investing" className="hover:text-primary transition">7. {currentContent.sections.nftInvesting.title}</a></li>
                <li><a href="#nft-risks" className="hover:text-primary transition">8. {currentContent.sections.nftRisks.title}</a></li>
                <li><a href="#future-of-nfts" className="hover:text-primary transition">9. {currentContent.sections.futureOfNFTs.title}</a></li>
              </ul>
            </div>

            {/* What are NFTs Section */}
            <section id="what-are-nfts" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>palette</span>
                {currentContent.sections.whatAreNFTs.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.whatAreNFTs.content[0]}</p>
                <p>{currentContent.sections.whatAreNFTs.content[1]}</p>
                <div className={`bg-purple-50 border-purple-400 p-4 rounded ${isRTL ? 'border-r-4' : 'border-l-4'}`}>
                  <p className="font-medium text-purple-800">
                    {currentContent.sections.whatAreNFTs.content[2]}
                  </p>
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 my-6">
                  {currentContent.sections.whatAreNFTs.characteristics.map((char, index) => (
                    <div key={index} className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl">
                      <h4 className="font-bold text-purple-950 mb-2 text-sm">{char.title}</h4>
                      <p className="text-xs text-purple-900/70">{char.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* NFT Standards Section */}
            <section id="nft-standards" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>code</span>
                {currentContent.sections.nftStandards.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.nftStandards.content}</p>
              <div className="space-y-6">
                {currentContent.sections.nftStandards.standards.map((standard, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <span className="material-symbols-outlined text-primary">token</span>
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-bold text-blue-950">{standard.name}</h4>
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                            {standard.blockchain}
                          </span>
                        </div>
                        <p className="text-blue-900/70 mb-3 text-sm">{standard.description}</p>
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-semibold text-green-700 mb-1 text-sm">{locale === 'ar' ? 'الميزات:' : 'Features:'}</h5>
                            <div className="flex flex-wrap gap-1">
                              {standard.features.map((feature, featIndex) => (
                                <span key={featIndex} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                  {feature}
                                </span>
                              ))}
                            </div>
                          </div>
                          <div>
                            <h5 className="font-semibold text-blue-700 mb-1 text-sm">{locale === 'ar' ? 'حالات الاستخدام:' : 'Use Cases:'}</h5>
                            <div className="flex flex-wrap gap-1">
                              {standard.use_cases.map((useCase, useCaseIndex) => (
                                <span key={useCaseIndex} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                  {useCase}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Digital Ownership Section */}
            <section id="digital-ownership" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>verified_user</span>
                {currentContent.sections.digitalOwnership.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.digitalOwnership.content}</p>
              <div className="space-y-6">
                {currentContent.sections.digitalOwnership.concepts.map((concept, index) => (
                  <div key={index} className="bg-cyan-50 border border-cyan-200 p-6 rounded-xl">
                    <h4 className="font-bold text-cyan-900 mb-3">🔗 {concept.concept}</h4>
                    <p className="text-cyan-800 mb-4">{concept.explanation}</p>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2">{locale === 'ar' ? 'التأثير:' : 'Impact:'}</h5>
                        <p className="text-sm text-blue-600">{concept.impact}</p>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'مثال:' : 'Example:'}</h5>
                        <p className="text-sm text-green-600 bg-green-50 p-3 rounded">{concept.example}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* NFT Use Cases Section */}
            <section id="nft-use-cases" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>apps</span>
                {currentContent.sections.nftUseCases.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.nftUseCases.content}</p>
              <div className="space-y-6">
                {currentContent.sections.nftUseCases.categories.map((category, index) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl">
                    <h4 className="font-bold text-purple-900 mb-3">🎯 {category.category}</h4>
                    <p className="text-purple-800 mb-4">{category.description}</p>
                    <div className="grid lg:grid-cols-3 gap-4">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2 text-sm">{locale === 'ar' ? 'أمثلة:' : 'Examples:'}</h5>
                        <ul className="text-xs text-blue-600 space-y-1">
                          {category.examples.map((example, exampleIndex) => (
                            <li key={exampleIndex}>• {example}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2 text-sm">{locale === 'ar' ? 'الفوائد:' : 'Benefits:'}</h5>
                        <ul className="text-xs text-green-600 space-y-1">
                          {category.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex}>• {benefit}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-orange-700 mb-2 text-sm">{locale === 'ar' ? 'اعتبارات:' : 'Considerations:'}</h5>
                        <ul className="text-xs text-orange-600 space-y-1">
                          {category.considerations.map((consideration, considerationIndex) => (
                            <li key={considerationIndex}>• {consideration}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* NFT Marketplaces Section */}
            <section id="nft-marketplaces" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>storefront</span>
                {currentContent.sections.nftMarketplaces.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.nftMarketplaces.content}</p>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.nftMarketplaces.marketplaces.map((marketplace, index) => (
                  <div key={index} className="bg-indigo-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{marketplace.name}</h4>
                      <span className="text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded">
                        {marketplace.type}
                      </span>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="font-semibold text-blue-700">{locale === 'ar' ? 'البلوك تشين:' : 'Blockchain:'}</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {marketplace.blockchain.map((chain, chainIndex) => (
                            <span key={chainIndex} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                              {chain}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="font-semibold text-green-700">{locale === 'ar' ? 'الميزات:' : 'Features:'}</span>
                        <ul className="text-xs text-green-600 mt-1 space-y-1">
                          {marketplace.features.map((feature, featIndex) => (
                            <li key={featIndex}>• {feature}</li>
                          ))}
                        </ul>
                      </div>
                      <div className="flex justify-between items-center pt-2 border-t border-indigo-200">
                        <span className="text-xs text-gray-600">{marketplace.fees}</span>
                      </div>
                      <p className="text-xs text-indigo-700 italic">{marketplace.specialty}</p>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Creating NFTs Section */}
            <section id="creating-nfts" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>brush</span>
                {currentContent.sections.creatingNFTs.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.creatingNFTs.content}</p>
              <div className="space-y-6">
                {currentContent.sections.creatingNFTs.steps.map((step, index) => (
                  <div key={index} className="bg-yellow-50 p-6 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center font-bold text-sm flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-blue-950 mb-2">{step.step}</h4>
                        <p className="text-blue-900/70 mb-3 text-sm">{step.description}</p>
                        <div className="grid lg:grid-cols-3 gap-4">
                          <div className="lg:col-span-2">
                            <h5 className="font-semibold text-blue-700 mb-1 text-sm">{locale === 'ar' ? 'التفاصيل:' : 'Details:'}</h5>
                            <ul className="text-xs text-blue-600 space-y-1">
                              {step.details.map((detail, detailIndex) => (
                                <li key={detailIndex}>• {detail}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-semibold text-green-700 mb-1 text-sm">{locale === 'ar' ? 'الأدوات:' : 'Tools:'}</h5>
                            <div className="flex flex-wrap gap-1">
                              {step.tools.map((tool, toolIndex) => (
                                <span key={toolIndex} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                  {tool}
                                </span>
                              ))}
                            </div>
                            <div className="mt-2">
                              <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
                                {step.timeframe}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* NFT Investing Section */}
            <section id="nft-investing" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>trending_up</span>
                {currentContent.sections.nftInvesting.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.nftInvesting.content}</p>
              
              {/* Investment Strategies */}
              <div className="space-y-6 mb-8">
                {currentContent.sections.nftInvesting.strategies.map((strategy, index) => (
                  <div key={index} className="bg-green-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{strategy.strategy}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        strategy.risk_level === 'High' || strategy.risk_level === 'عالي' ? 'bg-red-100 text-red-700' :
                        strategy.risk_level === 'Medium-High' || strategy.risk_level === 'متوسط-عالي' ? 'bg-orange-100 text-orange-700' :
                        'bg-yellow-100 text-yellow-700'
                      }`}>
                        {strategy.risk_level}
                      </span>
                    </div>
                    <p className="text-green-900/70 text-sm mb-4">{strategy.description}</p>
                    <div className="grid lg:grid-cols-3 gap-4 text-xs">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-1">{locale === 'ar' ? 'أمثلة:' : 'Examples:'}</h5>
                        <ul className="text-blue-600 space-y-1">
                          {strategy.examples.map((example, exampleIndex) => (
                            <li key={exampleIndex}>• {example}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-1">{locale === 'ar' ? 'المزايا:' : 'Pros:'}</h5>
                        <ul className="text-green-600 space-y-1">
                          {strategy.pros.map((pro, proIndex) => (
                            <li key={proIndex}>• {pro}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-red-700 mb-1">{locale === 'ar' ? 'العيوب:' : 'Cons:'}</h5>
                        <ul className="text-red-600 space-y-1">
                          {strategy.cons.map((con, conIndex) => (
                            <li key={conIndex}>• {con}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Valuation Factors */}
              <div className="bg-blue-50 p-6 rounded-xl">
                <h4 className="font-bold text-blue-950 mb-4">{locale === 'ar' ? 'عوامل التقييم' : 'Valuation Factors'}</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  {currentContent.sections.nftInvesting.valuation_factors.map((factor, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className={`w-3 h-3 rounded-full mt-1 ${
                        factor.impact === 'High' || factor.impact === 'عالي' ? 'bg-red-500' :
                        factor.impact === 'Medium' || factor.impact === 'متوسط' ? 'bg-yellow-500' :
                        'bg-green-500'
                      }`}></div>
                      <div>
                        <h5 className="font-semibold text-blue-950 text-sm">{factor.factor}</h5>
                        <p className="text-blue-900/70 text-xs">{factor.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* NFT Risks Section */}
            <section id="nft-risks" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>warning</span>
                {currentContent.sections.nftRisks.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.nftRisks.content}</p>
              <div className="space-y-6">
                {currentContent.sections.nftRisks.risk_categories.map((riskCategory, index) => (
                  <div key={index} className="bg-orange-50 border border-orange-200 p-6 rounded-xl">
                    <h4 className="font-bold text-orange-900 mb-4">⚠️ {riskCategory.category}</h4>
                    <div className="grid lg:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-semibold text-red-700 mb-2">{locale === 'ar' ? 'المخاطر:' : 'Risks:'}</h5>
                        <ul className="text-sm text-red-600 space-y-1">
                          {riskCategory.risks.map((risk, riskIndex) => (
                            <li key={riskIndex}>• {risk}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'التخفيف:' : 'Mitigation:'}</h5>
                        <ul className="text-sm text-green-600 space-y-1">
                          {riskCategory.mitigation.map((mitigation, mitigationIndex) => (
                            <li key={mitigationIndex}>• {mitigation}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Future of NFTs Section */}
            <section id="future-of-nfts" className="mb-8">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>forward</span>
                {currentContent.sections.futureOfNFTs.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.futureOfNFTs.content}</p>
              <div className="grid md:grid-cols-2 gap-4">
                {currentContent.sections.futureOfNFTs.trends.map((trend, index) => (
                  <div key={index} className="bg-cyan-50 p-4 rounded-xl">
                    <h4 className="font-bold text-blue-950 mb-2 text-sm">🚀 {trend.trend}</h4>
                    <p className="text-cyan-900/70 text-xs mb-2">{trend.description}</p>
                    <div className="text-xs space-y-1">
                      <div className="text-cyan-700 bg-cyan-100 px-2 py-1 rounded">
                        <strong>{locale === 'ar' ? 'التداعيات: ' : 'Implications: '}</strong>{trend.implications}
                      </div>
                      <div className="text-blue-700 bg-blue-100 px-2 py-1 rounded">
                        <strong>{locale === 'ar' ? 'الجدول الزمني: ' : 'Timeline: '}</strong>{trend.timeline}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Call to Action */}
            <div className="text-center pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-blue-950 mb-4">{currentContent.sections.cta.title}</h3>
              <p className="text-blue-900/70 mb-6">{currentContent.sections.cta.desc}</p>
              <Link href={`/${locale}/app`} className="btn-primary">
                {currentContent.sections.cta.button}
              </Link>
            </div>
          </motion.article>
        </div>
      </div>

      <Footer />
    </main>
  );
} 