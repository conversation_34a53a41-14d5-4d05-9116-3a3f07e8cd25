'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function Web3DevelopmentPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Content for both languages
  const content = {
    en: {
      title: "Web3 Development",
      subtitle: "Comprehensive guide to building decentralized applications, smart contracts, and blockchain solutions",
      backToLearn: "Back to Learn",
      tableOfContents: "📋 Table of Contents",
      sections: {
        whatIsWeb3Dev: {
          title: "What is Web3 Development?",
          content: [
            "Web3 development involves building decentralized applications (dApps) and smart contracts on blockchain networks. Unlike traditional web development, Web3 applications run on distributed networks without central authorities.",
            "Web3 developers create applications that interact with blockchains, enabling users to own their data, make trustless transactions, and participate in decentralized economies.",
            "💡 Core Philosophy: Web3 development emphasizes decentralization, user ownership, and trustless interactions through cryptographic proofs."
          ],
          fundamentals: [
            { title: "🔗 Blockchain Integration", desc: "Applications interact directly with blockchain networks through smart contracts and RPC calls" },
            { title: "🔑 Wallet Connectivity", desc: "Users connect their crypto wallets to authenticate and sign transactions" },
            { title: "📜 Smart Contracts", desc: "Self-executing contracts with terms directly written into code" },
            { title: "🌐 Decentralized Storage", desc: "Data stored on IPFS, Arweave, or other distributed storage networks" },
            { title: "⚡ Gas Optimization", desc: "Efficient coding practices to minimize transaction costs" },
            { title: "🔄 Consensus Mechanisms", desc: "Understanding how networks validate and finalize transactions" }
          ]
        },
        blockchainBasics: {
          title: "Blockchain Development Fundamentals",
          content: "Essential blockchain concepts every Web3 developer must understand:",
          concepts: [
            {
              concept: "Distributed Ledger",
              explanation: "Immutable record of transactions distributed across network nodes",
              importance: "Foundation for trustless applications and data integrity",
              implementation: "Each transaction is cryptographically linked to previous ones"
            },
            {
              concept: "Consensus Mechanisms",
              explanation: "Protocols that ensure all nodes agree on the network state",
              importance: "Enables decentralized validation without central authority",
              implementation: "Proof of Work, Proof of Stake, or other consensus algorithms"
            },
            {
              concept: "Cryptographic Hashing",
              explanation: "One-way functions that create unique fingerprints for data",
              importance: "Ensures data integrity and creates immutable links",
              implementation: "SHA-256, Keccak-256, and other hash functions"
            },
            {
              concept: "Digital Signatures",
              explanation: "Cryptographic proofs that verify transaction authenticity",
              importance: "Enables secure, authenticated transactions without passwords",
              implementation: "ECDSA, EdDSA signatures with public-private key pairs"
            }
          ]
        },
        smartContracts: {
          title: "Smart Contract Development",
          content: "Smart contracts are the backbone of Web3 applications, enabling programmable money and automated agreements:",
          languages: [
            {
              name: "Solidity",
              blockchain: ["Ethereum", "BSC", "Polygon"],
              description: "Object-oriented language designed for the Ethereum Virtual Machine",
              features: ["Contract inheritance", "Libraries", "Events", "Modifiers"],
              use_cases: ["DeFi protocols", "NFT contracts", "DAOs", "Token standards"]
            },
            {
              name: "Rust",
              blockchain: ["Solana", "Near", "Polkadot"],
              description: "Systems programming language with memory safety and performance",
              features: ["Zero-cost abstractions", "Memory safety", "Concurrency", "Cross-compilation"],
              use_cases: ["High-performance dApps", "Blockchain infrastructure", "DeFi protocols"]
            },
            {
              name: "Vyper",
              blockchain: ["Ethereum"],
              description: "Python-like language with security and auditability focus",
              features: ["Simplified syntax", "Security-first design", "No inheritance", "Explicit control flow"],
              use_cases: ["Security-critical contracts", "Financial applications", "Simple DeFi protocols"]
            }
          ],
          development_process: [
            { step: "Design", tasks: ["Define requirements", "Create architecture", "Plan security measures"], tools: ["Figma", "Miro", "Documentation"] },
            { step: "Development", tasks: ["Write smart contracts", "Implement business logic", "Add security checks"], tools: ["Remix IDE", "Hardhat", "Foundry"] },
            { step: "Testing", tasks: ["Unit testing", "Integration testing", "Security audits"], tools: ["Mocha", "Chai", "Waffle"] },
            { step: "Deployment", tasks: ["Deploy to testnet", "Verify contracts", "Deploy to mainnet"], tools: ["Hardhat Deploy", "Truffle", "Etherscan"] }
          ]
        },
        developmentStack: {
          title: "Web3 Development Stack",
          content: "Essential tools and frameworks for building Web3 applications:",
          categories: [
            {
              category: "Frontend Frameworks",
              tools: [
                { name: "React", description: "Most popular library for building Web3 UIs", pros: ["Large ecosystem", "Component reusability"], cons: ["Learning curve", "Bundle size"] },
                { name: "Next.js", description: "Full-stack React framework with SSR support", pros: ["SEO-friendly", "API routes"], cons: ["Complexity", "Opinionated"] },
                { name: "Vue.js", description: "Progressive framework with excellent DX", pros: ["Easy learning", "Performance"], cons: ["Smaller ecosystem", "Less Web3 tooling"] }
              ]
            },
            {
              category: "Web3 Libraries",
              tools: [
                { name: "ethers.js", description: "Complete Ethereum library for JavaScript", pros: ["TypeScript support", "Modular design"], cons: ["Bundle size", "Learning curve"] },
                { name: "web3.js", description: "Original Ethereum JavaScript library", pros: ["Battle-tested", "Large community"], cons: ["Larger bundle", "Complex API"] },
                { name: "viem", description: "Modern TypeScript interface for Ethereum", pros: ["Type safety", "Performance"], cons: ["Newer ecosystem", "Limited examples"] }
              ]
            },
            {
              category: "Development Environments",
              tools: [
                { name: "Hardhat", description: "Ethereum development environment", pros: ["Plugin ecosystem", "Local blockchain"], cons: ["Ethereum-focused", "Configuration complexity"] },
                { name: "Foundry", description: "Fast, portable, and modular toolkit", pros: ["Rust-based speed", "Gas optimization"], cons: ["Rust knowledge needed", "Newer ecosystem"] },
                { name: "Truffle", description: "Development framework for Ethereum", pros: ["Comprehensive suite", "Migration system"], cons: ["Slower compilation", "Complex setup"] }
              ]
            }
          ]
        },
        dappArchitecture: {
          title: "dApp Architecture Patterns",
          content: "Common architectural patterns for building scalable Web3 applications:",
          patterns: [
            {
              pattern: "Frontend + Smart Contract",
              description: "Simple architecture with frontend directly interacting with blockchain",
              components: ["React/Vue frontend", "Smart contracts", "Wallet connection"],
              pros: ["Simple to implement", "Fully decentralized", "No backend needed"],
              cons: ["Limited functionality", "Poor UX", "High gas costs"],
              best_for: "Simple dApps, prototypes, blockchain-native applications"
            },
            {
              pattern: "Hybrid Architecture",
              description: "Combines blockchain for critical operations with traditional backend for performance",
              components: ["Frontend", "Backend API", "Database", "Smart contracts", "Blockchain indexer"],
              pros: ["Better UX", "Cost-effective", "Scalable"],
              cons: ["Centralization points", "Complexity", "Trust assumptions"],
              best_for: "Production dApps, complex applications, user-facing products"
            },
            {
              pattern: "Multi-Chain Architecture",
              description: "Applications that operate across multiple blockchain networks",
              components: ["Cross-chain bridges", "Multi-chain contracts", "Chain abstraction layer"],
              pros: ["Access to multiple ecosystems", "Risk distribution", "Better liquidity"],
              cons: ["High complexity", "Security challenges", "Fragmented state"],
              best_for: "DeFi protocols, large-scale applications, infrastructure projects"
            }
          ]
        },
        webThreeTech: {
          title: "Web3 Technologies & Protocols",
          content: "Key technologies that power the Web3 ecosystem:",
          technologies: [
            {
              category: "Layer 1 Blockchains",
              description: "Base layer blockchain networks",
              examples: [
                { name: "Ethereum", focus: "Smart contracts", language: "Solidity", consensus: "Proof of Stake" },
                { name: "Solana", focus: "High performance", language: "Rust", consensus: "Proof of History" },
                { name: "Polkadot", focus: "Interoperability", language: "Rust", consensus: "Nominated PoS" }
              ]
            },
            {
              category: "Layer 2 Solutions",
              description: "Scaling solutions built on top of Layer 1",
              examples: [
                { name: "Polygon", focus: "Ethereum scaling", language: "Solidity", consensus: "Proof of Stake" },
                { name: "Arbitrum", focus: "Optimistic rollups", language: "Solidity", consensus: "Fraud proofs" },
                { name: "Optimism", focus: "Optimistic rollups", language: "Solidity", consensus: "Fraud proofs" }
              ]
            },
            {
              category: "Storage Networks",
              description: "Decentralized data storage solutions",
              examples: [
                { name: "IPFS", focus: "Content addressing", language: "Go/JS", consensus: "Content routing" },
                { name: "Arweave", focus: "Permanent storage", language: "Erlang", consensus: "Proof of Access" },
                { name: "Filecoin", focus: "Storage marketplace", language: "Go", consensus: "Proof of Spacetime" }
              ]
            }
          ]
        },
        securityBestPractices: {
          title: "Security Best Practices",
          content: "Critical security considerations for Web3 development:",
          practices: [
            {
              area: "Smart Contract Security",
              risks: ["Reentrancy attacks", "Integer overflow/underflow", "Access control issues", "Front-running"],
              solutions: ["Use ReentrancyGuard", "Use SafeMath libraries", "Implement proper modifiers", "Use commit-reveal schemes"],
              tools: ["Slither", "MythX", "Securify", "Manticore"]
            },
            {
              area: "Frontend Security",
              risks: ["Phishing attacks", "Man-in-the-middle", "Malicious DApps", "Wallet drainage"],
              solutions: ["Verify contract addresses", "Use HTTPS", "Validate all inputs", "Show transaction details"],
              tools: ["Web3 security libraries", "Address verification", "Transaction simulation"]
            },
            {
              area: "Operational Security",
              risks: ["Private key exposure", "Deployment errors", "Governance attacks", "Oracle manipulation"],
              solutions: ["Use hardware wallets", "Multi-signature deployments", "Timelocks", "Multiple oracle sources"],
              tools: ["Gnosis Safe", "OpenZeppelin Defender", "Chainlink oracles"]
            }
          ]
        },
        learningPath: {
          title: "Web3 Developer Learning Path",
          content: "Structured roadmap for becoming a Web3 developer:",
          phases: [
            {
              phase: "Foundation (1-2 months)",
              skills: ["Blockchain basics", "Cryptocurrency fundamentals", "Git/GitHub", "Command line"],
              resources: ["Mastering Bitcoin", "Ethereum Whitepaper", "Crypto basics courses"],
              projects: ["Set up wallets", "Make transactions", "Explore block explorers"]
            },
            {
              phase: "Programming Basics (2-3 months)",
              skills: ["JavaScript/TypeScript", "React fundamentals", "Node.js", "Package managers"],
              resources: ["freeCodeCamp", "React documentation", "MDN Web Docs"],
              projects: ["Todo app", "Weather app", "Portfolio website"]
            },
            {
              phase: "Web3 Fundamentals (2-3 months)",
              skills: ["ethers.js/web3.js", "Metamask integration", "IPFS basics", "Gas optimization"],
              resources: ["CryptoZombies", "Buildspace", "Alchemy University"],
              projects: ["Wallet connector", "NFT gallery", "Token balance checker"]
            },
            {
              phase: "Smart Contract Development (3-4 months)",
              skills: ["Solidity programming", "Hardhat/Foundry", "Testing", "Deployment"],
              resources: ["Solidity docs", "OpenZeppelin", "Smart Contract Programmer"],
              projects: ["ERC-20 token", "NFT collection", "Simple DEX"]
            },
            {
              phase: "Advanced Web3 (4-6 months)",
              skills: ["DeFi protocols", "Security auditing", "Layer 2", "Cross-chain"],
              resources: ["DeFiPulse", "L2Beat", "Security courses"],
              projects: ["Yield farming dApp", "Multi-chain bridge", "DAO governance"]
            }
          ]
        },
        jobMarket: {
          title: "Web3 Career Opportunities",
          content: "Career paths and opportunities in the Web3 space:",
          roles: [
            {
              role: "Smart Contract Developer",
              description: "Develop and audit smart contracts",
              skills: ["Solidity", "Security", "Gas optimization", "Testing"],
              salary_range: "$80k - $200k+",
              demand: "Very High"
            },
            {
              role: "dApp Frontend Developer",
              description: "Build user interfaces for Web3 applications",
              skills: ["React", "Web3.js", "UX/UI", "Wallet integration"],
              salary_range: "$70k - $150k+",
              demand: "High"
            },
            {
              role: "Blockchain Infrastructure Engineer",
              description: "Build and maintain blockchain infrastructure",
              skills: ["System design", "DevOps", "Rust/Go", "Networking"],
              salary_range: "$100k - $250k+",
              demand: "High"
            },
            {
              role: "DeFi Protocol Developer",
              description: "Build decentralized finance applications",
              skills: ["Advanced Solidity", "Economics", "Mathematics", "Security"],
              salary_range: "$120k - $300k+",
              demand: "Very High"
            },
            {
              role: "Web3 Security Auditor",
              description: "Audit smart contracts and protocols for vulnerabilities",
              skills: ["Security expertise", "Multiple languages", "Formal verification"],
              salary_range: "$100k - $400k+",
              demand: "Extremely High"
            }
          ]
        },
        futureOfWeb3Dev: {
          title: "Future of Web3 Development",
          content: "Emerging trends and future directions in Web3 development:",
          trends: [
            {
              trend: "AI + Blockchain Integration",
              description: "Combining AI capabilities with blockchain transparency",
              impact: "New types of applications and automated protocols",
              timeline: "2024-2026"
            },
            {
              trend: "Zero-Knowledge Applications",
              description: "Privacy-preserving applications using ZK proofs",
              impact: "Enhanced privacy while maintaining transparency",
              timeline: "2024-2025"
            },
            {
              trend: "Modular Blockchain Architecture",
              description: "Specialized chains for execution, settlement, and data availability",
              impact: "More scalable and efficient blockchain systems",
              timeline: "2024-2027"
            },
            {
              trend: "Intent-Based Architectures",
              description: "Users specify outcomes rather than transactions",
              impact: "Simplified UX and automated execution",
              timeline: "2025-2027"
            },
            {
              trend: "Cross-Chain Native Applications",
              description: "Applications built for multi-chain from the ground up",
              impact: "Seamless interoperability and unified liquidity",
              timeline: "2025-2028"
            }
          ]
        },
        cta: {
          title: "Start Your Web3 Development Journey",
          desc: "Begin building the decentralized future with comprehensive tools and resources available through Mokhba's developer ecosystem.",
          button: "Explore Development Tools"
        }
      }
    },
    ar: {
      title: "تطوير Web3",
      subtitle: "دليل شامل لبناء التطبيقات اللامركزية والعقود الذكية وحلول البلوك تشين",
      backToLearn: "العودة إلى التعلم",
      tableOfContents: "📋 فهرس المحتويات",
      sections: {
        whatIsWeb3Dev: {
          title: "ما هو تطوير Web3؟",
          content: [
            "يشمل تطوير Web3 بناء التطبيقات اللامركزية (dApps) والعقود الذكية على شبكات البلوك تشين. على عكس تطوير الويب التقليدي، تعمل تطبيقات Web3 على شبكات موزعة دون سلطات مركزية.",
            "يقوم مطورو Web3 بإنشاء تطبيقات تتفاعل مع البلوك تشين، مما يمكن المستخدمين من امتلاك بياناتهم وإجراء معاملات بدون ثقة والمشاركة في الاقتصادات اللامركزية.",
            "💡 الفلسفة الأساسية: يؤكد تطوير Web3 على اللامركزية وملكية المستخدم والتفاعلات بدون ثقة من خلال الإثباتات التشفيرية."
          ],
          fundamentals: [
            { title: "🔗 تكامل البلوك تشين", desc: "التطبيقات تتفاعل مباشرة مع شبكات البلوك تشين من خلال العقود الذكية واستدعاءات RPC" },
            { title: "🔑 اتصال المحفظة", desc: "المستخدمون يربطون محافظ العملات المشفرة للمصادقة وتوقيع المعاملات" },
            { title: "📜 العقود الذكية", desc: "عقود تنفذ نفسها بنفسها مع الشروط المكتوبة مباشرة في الكود" },
            { title: "🌐 التخزين اللامركزي", desc: "البيانات مخزنة على IPFS أو Arweave أو شبكات التخزين الموزعة الأخرى" },
            { title: "⚡ تحسين الغاز", desc: "ممارسات الترميز الفعالة لتقليل تكاليف المعاملات" },
            { title: "🔄 آليات الإجماع", desc: "فهم كيفية تحقق الشبكات من المعاملات وإنهائها" }
          ]
        },
        blockchainBasics: {
          title: "أساسيات تطوير البلوك تشين",
          content: "المفاهيم الأساسية للبلوك تشين التي يجب على كل مطور Web3 فهمها:",
          concepts: [
            {
              concept: "دفتر الأستاذ الموزع",
              explanation: "سجل غير قابل للتغيير للمعاملات موزع عبر عقد الشبكة",
              importance: "أساس للتطبيقات بدون ثقة وسلامة البيانات",
              implementation: "كل معاملة مربوطة تشفيرياً بالمعاملات السابقة"
            },
            {
              concept: "آليات الإجماع",
              explanation: "بروتوكولات تضمن موافقة جميع العقد على حالة الشبكة",
              importance: "تمكن التحقق اللامركزي دون سلطة مركزية",
              implementation: "إثبات العمل، إثبات الحصة، أو خوارزميات إجماع أخرى"
            },
            {
              concept: "التشفير بالتهيش",
              explanation: "وظائف أحادية الاتجاه تنشئ بصمات فريدة للبيانات",
              importance: "تضمن سلامة البيانات وتنشئ روابط غير قابلة للتغيير",
              implementation: "SHA-256، Keccak-256، ووظائف التهيش الأخرى"
            },
            {
              concept: "التوقيعات الرقمية",
              explanation: "إثباتات تشفيرية تتحقق من صحة المعاملة",
              importance: "تمكن المعاملات الآمنة والمصادق عليها دون كلمات مرور",
              implementation: "توقيعات ECDSA، EdDSA مع أزواج المفاتيح العامة-الخاصة"
            }
          ]
        },
        smartContracts: {
          title: "تطوير العقود الذكية",
          content: "العقود الذكية هي العمود الفقري لتطبيقات Web3، مما يمكن المال القابل للبرمجة والاتفاقيات الآلية:",
          languages: [
            {
              name: "Solidity",
              blockchain: ["إيثريوم", "BSC", "بوليغون"],
              description: "لغة موجهة للكائنات مصممة لآلة إيثريوم الافتراضية",
              features: ["وراثة العقود", "المكتبات", "الأحداث", "المعدلات"],
              use_cases: ["بروتوكولات DeFi", "عقود NFT", "DAOs", "معايير الرموز"]
            },
            {
              name: "Rust",
              blockchain: ["سولانا", "Near", "Polkadot"],
              description: "لغة برمجة أنظمة مع أمان الذاكرة والأداء",
              features: ["تجريدات بدون تكلفة", "أمان الذاكرة", "التزامن", "التجميع المتقاطع"],
              use_cases: ["dApps عالية الأداء", "بنية البلوك تشين التحتية", "بروتوكولات DeFi"]
            },
            {
              name: "Vyper",
              blockchain: ["إيثريوم"],
              description: "لغة شبيهة بـ Python مع التركيز على الأمان وقابلية المراجعة",
              features: ["بناء جملة مبسط", "تصميم يركز على الأمان", "لا وراثة", "تدفق تحكم صريح"],
              use_cases: ["عقود حرجة الأمان", "تطبيقات مالية", "بروتوكولات DeFi بسيطة"]
            }
          ],
          development_process: [
            { step: "التصميم", tasks: ["تحديد المتطلبات", "إنشاء الهندسة المعمارية", "تخطيط تدابير الأمان"], tools: ["Figma", "Miro", "التوثيق"] },
            { step: "التطوير", tasks: ["كتابة العقود الذكية", "تنفيذ منطق الأعمال", "إضافة فحوصات الأمان"], tools: ["Remix IDE", "Hardhat", "Foundry"] },
            { step: "الاختبار", tasks: ["اختبار الوحدة", "اختبار التكامل", "عمليات مراجعة الأمان"], tools: ["Mocha", "Chai", "Waffle"] },
            { step: "النشر", tasks: ["النشر على testnet", "التحقق من العقود", "النشر على mainnet"], tools: ["Hardhat Deploy", "Truffle", "Etherscan"] }
          ]
        },
        developmentStack: {
          title: "مجموعة تطوير Web3",
          content: "الأدوات والأطر الأساسية لبناء تطبيقات Web3:",
          categories: [
            {
              category: "أطر العمل الأمامية",
              tools: [
                { name: "React", description: "أشهر مكتبة لبناء واجهات Web3", pros: ["نظام بيئي كبير", "إعادة استخدام المكونات"], cons: ["منحنى التعلم", "حجم الحزمة"] },
                { name: "Next.js", description: "إطار React كامل مع دعم SSR", pros: ["صديق لـ SEO", "مسارات API"], cons: ["تعقيد", "رأي قوي"] },
                { name: "Vue.js", description: "إطار تدريجي مع DX ممتاز", pros: ["تعلم سهل", "أداء"], cons: ["نظام بيئي أصغر", "أدوات Web3 أقل"] }
              ]
            },
            {
              category: "مكتبات Web3",
              tools: [
                { name: "ethers.js", description: "مكتبة إيثريوم كاملة لـ JavaScript", pros: ["دعم TypeScript", "تصميم معياري"], cons: ["حجم الحزمة", "منحنى التعلم"] },
                { name: "web3.js", description: "مكتبة JavaScript الأصلية لإيثريوم", pros: ["مجربة في المعركة", "مجتمع كبير"], cons: ["حزمة أكبر", "API معقد"] },
                { name: "viem", description: "واجهة TypeScript حديثة لإيثريوم", pros: ["أمان النوع", "أداء"], cons: ["نظام بيئي أحدث", "أمثلة محدودة"] }
              ]
            },
            {
              category: "بيئات التطوير",
              tools: [
                { name: "Hardhat", description: "بيئة تطوير إيثريوم", pros: ["نظام المكونات الإضافية", "بلوك تشين محلي"], cons: ["مركز على إيثريوم", "تعقيد التكوين"] },
                { name: "Foundry", description: "مجموعة أدوات سريعة ومحمولة ومعيارية", pros: ["سرعة مبنية على Rust", "تحسين الغاز"], cons: ["مطلوب معرفة Rust", "نظام بيئي أحدث"] },
                { name: "Truffle", description: "إطار تطوير لإيثريوم", pros: ["مجموعة شاملة", "نظام الهجرة"], cons: ["تجميع أبطأ", "إعداد معقد"] }
              ]
            }
          ]
        },
        dappArchitecture: {
          title: "أنماط هندسة dApp",
          content: "الأنماط المعمارية الشائعة لبناء تطبيقات Web3 قابلة للتوسع:",
          patterns: [
            {
              pattern: "الواجهة الأمامية + العقد الذكي",
              description: "هندسة بسيطة مع تفاعل الواجهة الأمامية مباشرة مع البلوك تشين",
              components: ["واجهة React/Vue الأمامية", "العقود الذكية", "اتصال المحفظة"],
              pros: ["بسيط للتنفيذ", "لامركزي بالكامل", "لا حاجة لخادم خلفي"],
              cons: ["وظائف محدودة", "UX ضعيف", "تكاليف غاز عالية"],
              best_for: "dApps بسيطة، نماذج أولية، تطبيقات أصلية للبلوك تشين"
            },
            {
              pattern: "الهندسة المختلطة",
              description: "يجمع البلوك تشين للعمليات الحرجة مع الخادم الخلفي التقليدي للأداء",
              components: ["الواجهة الأمامية", "API الخادم الخلفي", "قاعدة البيانات", "العقود الذكية", "مفهرس البلوك تشين"],
              pros: ["UX أفضل", "فعال من حيث التكلفة", "قابل للتوسع"],
              cons: ["نقاط مركزية", "تعقيد", "افتراضات الثقة"],
              best_for: "dApps الإنتاج، التطبيقات المعقدة، المنتجات المواجهة للمستخدم"
            },
            {
              pattern: "الهندسة متعددة السلاسل",
              description: "التطبيقات التي تعمل عبر شبكات بلوك تشين متعددة",
              components: ["جسور عبر السلاسل", "عقود متعددة السلاسل", "طبقة تجريد السلسلة"],
              pros: ["الوصول لنظم متعددة", "توزيع المخاطر", "سيولة أفضل"],
              cons: ["تعقيد عالي", "تحديات أمنية", "حالة مجزأة"],
              best_for: "بروتوكولات DeFi، التطبيقات واسعة النطاق، مشاريع البنية التحتية"
            }
          ]
        },
        webThreeTech: {
          title: "تقنيات وبروتوكولات Web3",
          content: "التقنيات الأساسية التي تشغل نظام Web3:",
          technologies: [
            {
              category: "شبكات الطبقة الأولى",
              description: "شبكات البلوك تشين للطبقة الأساسية",
              examples: [
                { name: "إيثريوم", focus: "العقود الذكية", language: "Solidity", consensus: "إثبات الحصة" },
                { name: "سولانا", focus: "أداء عالي", language: "Rust", consensus: "إثبات التاريخ" },
                { name: "Polkadot", focus: "قابلية التشغيل البيني", language: "Rust", consensus: "PoS المرشح" }
              ]
            },
            {
              category: "حلول الطبقة الثانية",
              description: "حلول التوسيع المبنية على الطبقة الأولى",
              examples: [
                { name: "بوليغون", focus: "توسيع إيثريوم", language: "Solidity", consensus: "إثبات الحصة" },
                { name: "Arbitrum", focus: "rollups متفائلة", language: "Solidity", consensus: "إثباتات الاحتيال" },
                { name: "Optimism", focus: "rollups متفائلة", language: "Solidity", consensus: "إثباتات الاحتيال" }
              ]
            },
            {
              category: "شبكات التخزين",
              description: "حلول تخزين البيانات اللامركزية",
              examples: [
                { name: "IPFS", focus: "عنونة المحتوى", language: "Go/JS", consensus: "توجيه المحتوى" },
                { name: "Arweave", focus: "تخزين دائم", language: "Erlang", consensus: "إثبات الوصول" },
                { name: "Filecoin", focus: "سوق التخزين", language: "Go", consensus: "إثبات المكان والزمان" }
              ]
            }
          ]
        },
        securityBestPractices: {
          title: "أفضل ممارسات الأمان",
          content: "اعتبارات الأمان الحرجة لتطوير Web3:",
          practices: [
            {
              area: "أمان العقود الذكية",
              risks: ["هجمات إعادة الدخول", "فيض/نقص الأعداد الصحيحة", "مشاكل التحكم في الوصول", "الجري الأمامي"],
              solutions: ["استخدم ReentrancyGuard", "استخدم مكتبات SafeMath", "نفذ معدلات مناسبة", "استخدم مخططات الالتزام-الكشف"],
              tools: ["Slither", "MythX", "Securify", "Manticore"]
            },
            {
              area: "أمان الواجهة الأمامية",
              risks: ["هجمات التصيد", "رجل في الوسط", "dApps ضارة", "تفريغ المحفظة"],
              solutions: ["تحقق من عناوين العقود", "استخدم HTTPS", "تحقق من جميع المدخلات", "أظهر تفاصيل المعاملة"],
              tools: ["مكتبات أمان Web3", "التحقق من العنوان", "محاكاة المعاملة"]
            },
            {
              area: "الأمان التشغيلي",
              risks: ["تعرض المفتاح الخاص", "أخطاء النشر", "هجمات الحوكمة", "تلاعب أوراكل"],
              solutions: ["استخدم محافظ الأجهزة", "نشر متعدد التوقيع", "أقفال زمنية", "مصادر أوراكل متعددة"],
              tools: ["Gnosis Safe", "OpenZeppelin Defender", "أوراكل Chainlink"]
            }
          ]
        },
        learningPath: {
          title: "مسار تعلم مطور Web3",
          content: "خارطة طريق منظمة لتصبح مطور Web3:",
          phases: [
            {
              phase: "الأساس (1-2 شهر)",
              skills: ["أساسيات البلوك تشين", "أساسيات العملة المشفرة", "Git/GitHub", "سطر الأوامر"],
              resources: ["إتقان البيتكوين", "ورقة إيثريوم البيضاء", "دورات أساسيات العملة المشفرة"],
              projects: ["إعداد المحافظ", "إجراء المعاملات", "استكشاف مستكشفات الكتل"]
            },
            {
              phase: "أساسيات البرمجة (2-3 أشهر)",
              skills: ["JavaScript/TypeScript", "أساسيات React", "Node.js", "مديرو الحزم"],
              resources: ["freeCodeCamp", "وثائق React", "MDN Web Docs"],
              projects: ["تطبيق مهام", "تطبيق طقس", "موقع محفظة أعمال"]
            },
            {
              phase: "أساسيات Web3 (2-3 أشهر)",
              skills: ["ethers.js/web3.js", "تكامل Metamask", "أساسيات IPFS", "تحسين الغاز"],
              resources: ["CryptoZombies", "Buildspace", "جامعة Alchemy"],
              projects: ["موصل محفظة", "معرض NFT", "فاحص رصيد الرمز"]
            },
            {
              phase: "تطوير العقود الذكية (3-4 أشهر)",
              skills: ["برمجة Solidity", "Hardhat/Foundry", "الاختبار", "النشر"],
              resources: ["وثائق Solidity", "OpenZeppelin", "مبرمج العقود الذكية"],
              projects: ["رمز ERC-20", "مجموعة NFT", "DEX بسيط"]
            },
            {
              phase: "Web3 متقدم (4-6 أشهر)",
              skills: ["بروتوكولات DeFi", "مراجعة الأمان", "الطبقة 2", "عبر السلاسل"],
              resources: ["DeFiPulse", "L2Beat", "دورات الأمان"],
              projects: ["dApp زراعة العائد", "جسر متعدد السلاسل", "حوكمة DAO"]
            }
          ]
        },
        jobMarket: {
          title: "فرص مهنة Web3",
          content: "المسارات المهنية والفرص في مجال Web3:",
          roles: [
            {
              role: "مطور العقود الذكية",
              description: "تطوير ومراجعة العقود الذكية",
              skills: ["Solidity", "الأمان", "تحسين الغاز", "الاختبار"],
              salary_range: "$80k - $200k+",
              demand: "عالي جداً"
            },
            {
              role: "مطور واجهة dApp الأمامية",
              description: "بناء واجهات المستخدم لتطبيقات Web3",
              skills: ["React", "Web3.js", "UX/UI", "تكامل المحفظة"],
              salary_range: "$70k - $150k+",
              demand: "عالي"
            },
            {
              role: "مهندس بنية البلوك تشين التحتية",
              description: "بناء وصيانة بنية البلوك تشين التحتية",
              skills: ["تصميم النظم", "DevOps", "Rust/Go", "الشبكات"],
              salary_range: "$100k - $250k+",
              demand: "عالي"
            },
            {
              role: "مطور بروتوكول DeFi",
              description: "بناء تطبيقات التمويل اللامركزي",
              skills: ["Solidity متقدم", "الاقتصاد", "الرياضيات", "الأمان"],
              salary_range: "$120k - $300k+",
              demand: "عالي جداً"
            },
            {
              role: "مراجع أمان Web3",
              description: "مراجعة العقود الذكية والبروتوكولات للثغرات",
              skills: ["خبرة أمنية", "لغات متعددة", "التحقق الرسمي"],
              salary_range: "$100k - $400k+",
              demand: "عالي للغاية"
            }
          ]
        },
        futureOfWeb3Dev: {
          title: "مستقبل تطوير Web3",
          content: "الاتجاهات الناشئة والاتجاهات المستقبلية في تطوير Web3:",
          trends: [
            {
              trend: "تكامل الذكاء الاصطناعي + البلوك تشين",
              description: "دمج قدرات الذكاء الاصطناعي مع شفافية البلوك تشين",
              impact: "أنواع جديدة من التطبيقات والبروتوكولات الآلية",
              timeline: "2024-2026"
            },
            {
              trend: "تطبيقات المعرفة الصفرية",
              description: "تطبيقات تحافظ على الخصوصية باستخدام إثباتات ZK",
              impact: "خصوصية محسنة مع الحفاظ على الشفافية",
              timeline: "2024-2025"
            },
            {
              trend: "هندسة البلوك تشين المعيارية",
              description: "سلاسل متخصصة للتنفيذ والتسوية وتوفر البيانات",
              impact: "أنظمة بلوك تشين أكثر قابلية للتوسع وكفاءة",
              timeline: "2024-2027"
            },
            {
              trend: "هندسة قائمة على النوايا",
              description: "المستخدمون يحددون النتائج بدلاً من المعاملات",
              impact: "UX مبسط وتنفيذ آلي",
              timeline: "2025-2027"
            },
            {
              trend: "تطبيقات أصلية عبر السلاسل",
              description: "تطبيقات مبنية لمتعددة السلاسل من الأساس",
              impact: "قابلية تشغيل بيني سلسة وسيولة موحدة",
              timeline: "2025-2028"
            }
          ]
        },
        cta: {
          title: "ابدأ رحلة تطوير Web3",
          desc: "ابدأ في بناء المستقبل اللامركزي مع الأدوات والموارد الشاملة المتاحة من خلال نظام مطوري مخبة.",
          button: "استكشف أدوات التطوير"
        }
      }
    }
  };

  const currentContent = content[locale as 'en' | 'ar'] || content.en;

  return (
    <main className={`relative bg-gradient-to-b from-white to-[#73AED2] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content */}
      <div className="pt-24 pb-16 min-h-screen">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <Link 
              href={`/${locale}/learn`}
              className="text-primary hover:text-primary/80 flex items-center mb-4"
            >
              <span className={`material-symbols-outlined ${isRTL ? 'ml-2 scale-x-[-1]' : 'mr-2'}`}>arrow_back</span>
              {currentContent.backToLearn}
            </Link>
          </nav>

          {/* Article Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-6">
              <span className="material-symbols-outlined text-3xl text-primary">code</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-blue-950 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-blue-900/70 max-w-2xl mx-auto">
              {currentContent.subtitle}
            </p>
            <div className="flex items-center justify-center gap-4 mt-4 text-sm text-blue-900/60">
              <span>💻 {locale === 'ar' ? 'تقني' : 'Technical'}</span>
              <span>•</span>
              <span>⏱️ {locale === 'ar' ? '25 دقيقة قراءة' : '25 min read'}</span>
            </div>
          </motion.div>

          {/* Article Content */}
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            {/* Table of Contents */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-bold text-blue-950 mb-4">{currentContent.tableOfContents}</h3>
              <ul className="space-y-2 text-blue-900 grid md:grid-cols-2 gap-2">
                <li><a href="#what-is-web3-dev" className="hover:text-primary transition">1. {currentContent.sections.whatIsWeb3Dev.title}</a></li>
                <li><a href="#blockchain-basics" className="hover:text-primary transition">2. {currentContent.sections.blockchainBasics.title}</a></li>
                <li><a href="#smart-contracts" className="hover:text-primary transition">3. {currentContent.sections.smartContracts.title}</a></li>
                <li><a href="#development-stack" className="hover:text-primary transition">4. {currentContent.sections.developmentStack.title}</a></li>
                <li><a href="#dapp-architecture" className="hover:text-primary transition">5. {currentContent.sections.dappArchitecture.title}</a></li>
                <li><a href="#web3-tech" className="hover:text-primary transition">6. {currentContent.sections.webThreeTech.title}</a></li>
                <li><a href="#security-practices" className="hover:text-primary transition">7. {currentContent.sections.securityBestPractices.title}</a></li>
                <li><a href="#learning-path" className="hover:text-primary transition">8. {currentContent.sections.learningPath.title}</a></li>
                <li><a href="#job-market" className="hover:text-primary transition">9. {currentContent.sections.jobMarket.title}</a></li>
                <li><a href="#future-web3-dev" className="hover:text-primary transition">10. {currentContent.sections.futureOfWeb3Dev.title}</a></li>
              </ul>
            </div>

            {/* What is Web3 Dev Section */}
            <section id="what-is-web3-dev" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>code</span>
                {currentContent.sections.whatIsWeb3Dev.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.whatIsWeb3Dev.content[0]}</p>
                <p>{currentContent.sections.whatIsWeb3Dev.content[1]}</p>
                <div className={`bg-indigo-50 border-indigo-400 p-4 rounded ${isRTL ? 'border-r-4' : 'border-l-4'}`}>
                  <p className="font-medium text-indigo-800">
                    {currentContent.sections.whatIsWeb3Dev.content[2]}
                  </p>
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 my-6">
                  {currentContent.sections.whatIsWeb3Dev.fundamentals.map((fundamental, index) => (
                    <div key={index} className="bg-gradient-to-br from-indigo-50 to-purple-50 p-4 rounded-xl">
                      <h4 className="font-bold text-indigo-950 mb-2 text-sm">{fundamental.title}</h4>
                      <p className="text-xs text-indigo-900/70">{fundamental.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Blockchain Basics Section */}
            <section id="blockchain-basics" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>link</span>
                {currentContent.sections.blockchainBasics.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.blockchainBasics.content}</p>
              <div className="space-y-6">
                {currentContent.sections.blockchainBasics.concepts.map((concept, index) => (
                  <div key={index} className="bg-blue-50 border border-blue-200 p-6 rounded-xl">
                    <h4 className="font-bold text-blue-900 mb-3">🔗 {concept.concept}</h4>
                    <p className="text-blue-800 mb-4 text-sm">{concept.explanation}</p>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'الأهمية:' : 'Importance:'}</h5>
                        <p className="text-green-600">{concept.importance}</p>
                      </div>
                      <div>
                        <h5 className="font-semibold text-purple-700 mb-2">{locale === 'ar' ? 'التنفيذ:' : 'Implementation:'}</h5>
                        <p className="text-purple-600">{concept.implementation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Smart Contracts Section */}
            <section id="smart-contracts" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>description</span>
                {currentContent.sections.smartContracts.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.smartContracts.content}</p>
              
              {/* Languages */}
              <div className="mb-8">
                <h4 className="font-bold text-blue-950 mb-4">{locale === 'ar' ? 'لغات البرمجة' : 'Programming Languages'}</h4>
                <div className="space-y-4">
                  {currentContent.sections.smartContracts.languages.map((language, index) => (
                    <div key={index} className="bg-gray-50 p-4 rounded-xl">
                      <div className="flex justify-between items-start mb-2">
                        <h5 className="font-bold text-blue-950">{language.name}</h5>
                        <div className="flex gap-1">
                          {language.blockchain.map((chain, chainIndex) => (
                            <span key={chainIndex} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                              {chain}
                            </span>
                          ))}
                        </div>
                      </div>
                      <p className="text-gray-700 text-sm mb-3">{language.description}</p>
                      <div className="grid md:grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="font-semibold text-green-700">{locale === 'ar' ? 'الميزات:' : 'Features:'}</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {language.features.map((feature, featIndex) => (
                              <span key={featIndex} className="bg-green-100 text-green-700 px-2 py-1 rounded">
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div>
                          <span className="font-semibold text-purple-700">{locale === 'ar' ? 'حالات الاستخدام:' : 'Use Cases:'}</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {language.use_cases.map((useCase, useCaseIndex) => (
                              <span key={useCaseIndex} className="bg-purple-100 text-purple-700 px-2 py-1 rounded">
                                {useCase}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Development Process */}
              <div>
                <h4 className="font-bold text-blue-950 mb-4">{locale === 'ar' ? 'عملية التطوير' : 'Development Process'}</h4>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {currentContent.sections.smartContracts.development_process.map((phase, index) => (
                    <div key={index} className="bg-yellow-50 p-4 rounded-xl">
                      <h5 className="font-bold text-yellow-900 mb-2">{phase.step}</h5>
                      <ul className="text-xs text-yellow-800 space-y-1 mb-3">
                        {phase.tasks.map((task, taskIndex) => (
                          <li key={taskIndex}>• {task}</li>
                        ))}
                      </ul>
                      <div className="flex flex-wrap gap-1">
                        {phase.tools.map((tool, toolIndex) => (
                          <span key={toolIndex} className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
                            {tool}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Development Stack Section */}
            <section id="development-stack" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>layers</span>
                {currentContent.sections.developmentStack.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.developmentStack.content}</p>
              <div className="space-y-8">
                {currentContent.sections.developmentStack.categories.map((category, index) => (
                  <div key={index}>
                    <h4 className="font-bold text-blue-950 mb-4">{category.category}</h4>
                    <div className="grid md:grid-cols-3 gap-4">
                      {category.tools.map((tool, toolIndex) => (
                        <div key={toolIndex} className="bg-green-50 p-4 rounded-xl">
                          <h5 className="font-bold text-green-900 mb-2">{tool.name}</h5>
                          <p className="text-green-800 text-sm mb-3">{tool.description}</p>
                          <div className="space-y-2 text-xs">
                            <div>
                              <span className="font-semibold text-green-700">{locale === 'ar' ? 'المزايا:' : 'Pros:'}</span>
                              <ul className="text-green-600 ml-2">
                                {tool.pros.map((pro, proIndex) => (
                                  <li key={proIndex}>• {pro}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <span className="font-semibold text-red-700">{locale === 'ar' ? 'العيوب:' : 'Cons:'}</span>
                              <ul className="text-red-600 ml-2">
                                {tool.cons.map((con, conIndex) => (
                                  <li key={conIndex}>• {con}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* dApp Architecture Section */}
            <section id="dapp-architecture" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>architecture</span>
                {currentContent.sections.dappArchitecture.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.dappArchitecture.content}</p>
              <div className="space-y-6">
                {currentContent.sections.dappArchitecture.patterns.map((pattern, index) => (
                  <div key={index} className="bg-purple-50 p-6 rounded-xl">
                    <h4 className="font-bold text-purple-900 mb-3">🏗️ {pattern.pattern}</h4>
                    <p className="text-purple-800 mb-4 text-sm">{pattern.description}</p>
                    <div className="grid lg:grid-cols-4 gap-4 text-xs">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2">{locale === 'ar' ? 'المكونات:' : 'Components:'}</h5>
                        <ul className="text-blue-600 space-y-1">
                          {pattern.components.map((component, compIndex) => (
                            <li key={compIndex}>• {component}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'المزايا:' : 'Pros:'}</h5>
                        <ul className="text-green-600 space-y-1">
                          {pattern.pros.map((pro, proIndex) => (
                            <li key={proIndex}>• {pro}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-red-700 mb-2">{locale === 'ar' ? 'العيوب:' : 'Cons:'}</h5>
                        <ul className="text-red-600 space-y-1">
                          {pattern.cons.map((con, conIndex) => (
                            <li key={conIndex}>• {con}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-purple-700 mb-2">{locale === 'ar' ? 'الأفضل لـ:' : 'Best For:'}</h5>
                        <p className="text-purple-600">{pattern.best_for}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Web3 Technologies Section */}
            <section id="web3-tech" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>hub</span>
                {currentContent.sections.webThreeTech.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.webThreeTech.content}</p>
              <div className="space-y-6">
                {currentContent.sections.webThreeTech.technologies.map((tech, index) => (
                  <div key={index} className="bg-cyan-50 p-6 rounded-xl">
                    <h4 className="font-bold text-cyan-900 mb-4">🌐 {tech.category}</h4>
                    <p className="text-cyan-800 mb-4 text-sm">{tech.description}</p>
                    <div className="grid md:grid-cols-3 gap-4">
                      {tech.examples.map((example, exampleIndex) => (
                        <div key={exampleIndex} className="bg-white p-4 rounded-lg">
                          <h5 className="font-bold text-blue-950 mb-2">{example.name}</h5>
                          <div className="space-y-1 text-xs">
                            <div><strong>{locale === 'ar' ? 'التركيز:' : 'Focus:'}</strong> {example.focus}</div>
                            <div><strong>{locale === 'ar' ? 'اللغة:' : 'Language:'}</strong> {example.language}</div>
                            <div><strong>{locale === 'ar' ? 'الإجماع:' : 'Consensus:'}</strong> {example.consensus}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Security Best Practices Section */}
            <section id="security-practices" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>security</span>
                {currentContent.sections.securityBestPractices.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.securityBestPractices.content}</p>
              <div className="space-y-6">
                {currentContent.sections.securityBestPractices.practices.map((practice, index) => (
                  <div key={index} className="bg-orange-50 border border-orange-200 p-6 rounded-xl">
                    <h4 className="font-bold text-orange-900 mb-4">🔒 {practice.area}</h4>
                    <div className="grid lg:grid-cols-3 gap-4 text-sm">
                      <div>
                        <h5 className="font-semibold text-red-700 mb-2">{locale === 'ar' ? 'المخاطر:' : 'Risks:'}</h5>
                        <ul className="text-red-600 space-y-1">
                          {practice.risks.map((risk, riskIndex) => (
                            <li key={riskIndex}>• {risk}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'الحلول:' : 'Solutions:'}</h5>
                        <ul className="text-green-600 space-y-1">
                          {practice.solutions.map((solution, solutionIndex) => (
                            <li key={solutionIndex}>• {solution}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2">{locale === 'ar' ? 'الأدوات:' : 'Tools:'}</h5>
                        <div className="flex flex-wrap gap-1">
                          {practice.tools.map((tool, toolIndex) => (
                            <span key={toolIndex} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                              {tool}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Learning Path Section */}
            <section id="learning-path" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>school</span>
                {currentContent.sections.learningPath.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.learningPath.content}</p>
              <div className="space-y-6">
                {currentContent.sections.learningPath.phases.map((phase, index) => (
                  <div key={index} className="bg-indigo-50 p-6 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center font-bold text-sm flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-blue-950 mb-3">{phase.phase}</h4>
                        <div className="grid lg:grid-cols-3 gap-4 text-sm">
                          <div>
                            <h5 className="font-semibold text-blue-700 mb-2">{locale === 'ar' ? 'المهارات:' : 'Skills:'}</h5>
                            <ul className="text-blue-600 space-y-1">
                              {phase.skills.map((skill, skillIndex) => (
                                <li key={skillIndex}>• {skill}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'الموارد:' : 'Resources:'}</h5>
                            <ul className="text-green-600 space-y-1">
                              {phase.resources.map((resource, resourceIndex) => (
                                <li key={resourceIndex}>• {resource}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-semibold text-purple-700 mb-2">{locale === 'ar' ? 'المشاريع:' : 'Projects:'}</h5>
                            <ul className="text-purple-600 space-y-1">
                              {phase.projects.map((project, projectIndex) => (
                                <li key={projectIndex}>• {project}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Job Market Section */}
            <section id="job-market" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>work</span>
                {currentContent.sections.jobMarket.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.jobMarket.content}</p>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.jobMarket.roles.map((role, index) => (
                  <div key={index} className="bg-green-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{role.role}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        role.demand === 'Extremely High' || role.demand === 'عالي للغاية' ? 'bg-red-100 text-red-700' :
                        role.demand === 'Very High' || role.demand === 'عالي جداً' ? 'bg-orange-100 text-orange-700' :
                        'bg-yellow-100 text-yellow-700'
                      }`}>
                        {role.demand}
                      </span>
                    </div>
                    <p className="text-green-900/70 text-sm mb-4">{role.description}</p>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-semibold text-blue-700">{locale === 'ar' ? 'المهارات:' : 'Skills:'}</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {role.skills.map((skill, skillIndex) => (
                            <span key={skillIndex} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="flex justify-between items-center pt-2 border-t border-green-200">
                        <span className="font-semibold text-green-700">{locale === 'ar' ? 'الراتب:' : 'Salary:'}</span>
                        <span className="text-green-600 font-bold">{role.salary_range}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Future of Web3 Dev Section */}
            <section id="future-web3-dev" className="mb-8">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>rocket_launch</span>
                {currentContent.sections.futureOfWeb3Dev.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.futureOfWeb3Dev.content}</p>
              <div className="grid md:grid-cols-2 gap-4">
                {currentContent.sections.futureOfWeb3Dev.trends.map((trend, index) => (
                  <div key={index} className="bg-cyan-50 p-4 rounded-xl">
                    <h4 className="font-bold text-blue-950 mb-2 text-sm">🚀 {trend.trend}</h4>
                    <p className="text-cyan-900/70 text-xs mb-2">{trend.description}</p>
                    <div className="text-xs space-y-1">
                      <div className="text-cyan-700 bg-cyan-100 px-2 py-1 rounded">
                        <strong>{locale === 'ar' ? 'التأثير: ' : 'Impact: '}</strong>{trend.impact}
                      </div>
                      <div className="text-blue-700 bg-blue-100 px-2 py-1 rounded">
                        <strong>{locale === 'ar' ? 'الجدول الزمني: ' : 'Timeline: '}</strong>{trend.timeline}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Call to Action */}
            <div className="text-center pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-blue-950 mb-4">{currentContent.sections.cta.title}</h3>
              <p className="text-blue-900/70 mb-6">{currentContent.sections.cta.desc}</p>
              <Link href={`/${locale}/app`} className="btn-primary">
                {currentContent.sections.cta.button}
              </Link>
            </div>
          </motion.article>
        </div>
      </div>

      <Footer />
    </main>
  );
} 