'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function SecurityPracticesPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Content for both languages
  const content = {
    en: {
      title: "Security Best Practices",
      subtitle: "Essential security measures to protect your cryptocurrency investments and digital assets",
      backToLearn: "Back to Learn",
      tableOfContents: "📋 Table of Contents",
      sections: {
        walletSecurity: {
          title: "Wallet Security Fundamentals",
          content: [
            "Your wallet is your digital vault. Securing it properly is the most critical step in protecting your cryptocurrency investments.",
            "Whether you use hardware wallets, software wallets, or mobile wallets, following security best practices is essential for safeguarding your assets.",
            "🔐 Golden Rule: Never share your private keys, seed phrases, or passwords with anyone. Legitimate services will never ask for these."
          ],
          practices: [
            { title: "🔑 Private Key Management", desc: "Generate strong private keys offline and store them securely. Never store them digitally or in cloud services." },
            { title: "📝 Seed Phrase Backup", desc: "Write down your 12-24 word seed phrase on paper or metal. Store copies in multiple secure locations." },
            { title: "🔒 Strong Passwords", desc: "Use unique, complex passwords for each wallet and exchange account. Consider using a password manager." },
            { title: "🔄 Regular Updates", desc: "Keep your wallet software updated to the latest version to protect against known vulnerabilities." }
          ]
        },
        exchangeSecurity: {
          title: "Exchange & Platform Security",
          content: "Centralized exchanges are convenient but present unique security challenges. Follow these practices to minimize risks:",
          guidelines: [
            {
              title: "Two-Factor Authentication (2FA)",
              desc: "Enable 2FA using authenticator apps like Google Authenticator or Authy. Avoid SMS-based 2FA when possible.",
              priority: "Critical"
            },
            {
              title: "Withdrawal Whitelisting",
              desc: "Only allow withdrawals to pre-approved addresses. This prevents unauthorized withdrawals even if your account is compromised.",
              priority: "High"
            },
            {
              title: "Regular Security Audits",
              desc: "Review your account activity regularly. Check login history and transaction records for any suspicious activity.",
              priority: "Medium"
            },
            {
              title: "Cold Storage Transfer",
              desc: "Don't keep large amounts on exchanges. Transfer to hardware wallets or cold storage for long-term holding.",
              priority: "Critical"
            }
          ]
        },
        phishingScams: {
          title: "Phishing & Scam Protection",
          content: "Cybercriminals use sophisticated techniques to steal your cryptocurrency. Learn to recognize and avoid these threats:",
          threats: [
            {
              type: "Phishing Websites",
              description: "Fake websites that look identical to legitimate exchanges or wallets",
              redFlags: ["Slight URL differences", "HTTP instead of HTTPS", "Poor spelling/grammar"],
              protection: "Always bookmark official sites and check URLs carefully"
            },
            {
              type: "Social Engineering",
              description: "Scammers impersonating support staff or influencers to gain your trust",
              redFlags: ["Unsolicited messages", "Urgency tactics", "Requests for private keys"],
              protection: "Never share sensitive information through social media or messages"
            },
            {
              type: "Fake Mobile Apps",
              description: "Malicious apps that steal wallet information or private keys",
              redFlags: ["Unofficial app stores", "Poor reviews", "Excessive permissions"],
              protection: "Only download apps from official stores and verify developer authenticity"
            },
            {
              type: "Investment Scams",
              description: "Promises of guaranteed returns or 'get rich quick' schemes",
              redFlags: ["Unrealistic returns", "Pressure to invest quickly", "No clear business model"],
              protection: "Research thoroughly and remember: if it sounds too good to be true, it probably is"
            }
          ]
        },
        hardwareSecurity: {
          title: "Hardware & Device Security",
          content: "Your devices are potential entry points for attackers. Secure them properly to protect your crypto assets.",
          measures: [
            {
              icon: "💻",
              title: "Secure Computing Environment",
              desc: "Use dedicated devices for crypto transactions. Keep operating systems updated and run antivirus software.",
              tips: ["Dedicated crypto computer", "Regular OS updates", "Antivirus protection", "Avoid public WiFi"]
            },
            {
              icon: "📱",
              title: "Mobile Device Security",
              desc: "Secure your mobile devices with strong PINs, biometric locks, and app-specific passwords.",
              tips: ["Strong screen locks", "App permissions review", "Official app stores only", "Regular security updates"]
            },
            {
              icon: "🔌",
              title: "Hardware Wallets",
              desc: "Use reputable hardware wallets for storing large amounts. Buy directly from manufacturers.",
              tips: ["Buy from official sources", "Verify firmware authenticity", "Use secure PIN", "Store recovery seed safely"]
            },
            {
              icon: "🌐",
              title: "Network Security",
              desc: "Use secure networks and consider VPN for additional privacy protection.",
              tips: ["Avoid public WiFi", "Use VPN services", "Secure home router", "Monitor network traffic"]
            }
          ]
        },
        emergencyPrep: {
          title: "Emergency Preparedness",
          content: "Prepare for various scenarios to ensure you can always access your funds when needed.",
          scenarios: [
            {
              scenario: "Device Loss/Theft",
              preparation: "Secure backups of all wallet data and recovery phrases in multiple locations",
              response: "Immediately transfer funds to new wallet using backup recovery phrase"
            },
            {
              scenario: "Death/Incapacitation",
              preparation: "Create secure inheritance plan with trusted family members or legal services",
              response: "Beneficiaries follow predetermined access procedures"
            },
            {
              scenario: "Exchange Hack/Shutdown",
              preparation: "Don't store large amounts on exchanges; have multiple exchange accounts",
              response: "Quickly withdraw funds to personal wallets if possible"
            },
            {
              scenario: "Government Restrictions",
              preparation: "Understand local regulations; consider decentralized options",
              response: "Follow legal requirements while protecting asset access"
            }
          ]
        },
        advancedSecurity: {
          title: "Advanced Security Techniques",
          content: "For users with significant holdings, these advanced techniques provide additional layers of security:",
          techniques: [
            {
              name: "Multi-Signature Wallets",
              description: "Require multiple signatures to authorize transactions",
              useCase: "Large holdings, business accounts, shared custody",
              complexity: "High"
            },
            {
              name: "Cold Storage Solutions",
              description: "Completely offline storage methods for maximum security",
              useCase: "Long-term storage, inheritance planning",
              complexity: "Medium"
            },
            {
              name: "Decoy Wallets",
              description: "Small amounts in easily accessible wallets to mislead attackers",
              useCase: "Physical security threats, travel situations",
              complexity: "Medium"
            },
            {
              name: "Time-Locked Transactions",
              description: "Transactions that can only be executed after specific time periods",
              useCase: "Inheritance, forced savings, security delays",
              complexity: "High"
            }
          ]
        },
        securityChecklist: {
          title: "Security Checklist",
          items: [
            "✅ Enable 2FA on all crypto-related accounts",
            "✅ Use hardware wallet for large amounts",
            "✅ Backup seed phrases securely (offline, multiple locations)",
            "✅ Use unique, strong passwords for each service",
            "✅ Regularly update all software and firmware",
            "✅ Verify all URLs and app authenticity before use",
            "✅ Never share private keys or seed phrases",
            "✅ Use secure networks for crypto transactions",
            "✅ Monitor accounts regularly for suspicious activity",
            "✅ Have emergency access plan in place"
          ]
        },
        cta: {
          title: "Secure Your Crypto Journey",
          desc: "Implement these security practices today and protect your investments with Mokhba Wallet's built-in security features.",
          button: "Secure Your Wallet"
        }
      }
    },
    ar: {
      title: "أفضل ممارسات الأمان",
      subtitle: "التدابير الأمنية الأساسية لحماية استثماراتك في العملات المشفرة والأصول الرقمية",
      backToLearn: "العودة إلى التعلم",
      tableOfContents: "📋 فهرس المحتويات",
      sections: {
        walletSecurity: {
          title: "أساسيات أمان المحفظة",
          content: [
            "محفظتك هي خزينتك الرقمية. تأمينها بشكل صحيح هو الخطوة الأكثر أهمية في حماية استثماراتك في العملات المشفرة.",
            "سواء كنت تستخدم محافظ الأجهزة أو محافظ البرامج أو محافظ الهاتف المحمول، فإن اتباع أفضل ممارسات الأمان أمر ضروري لحماية أصولك.",
            "🔐 القاعدة الذهبية: لا تشارك أبداً مفاتيحك الخاصة أو عبارات البذرة أو كلمات المرور مع أي شخص. الخدمات الشرعية لن تطلب هذه المعلومات أبداً."
          ],
          practices: [
            { title: "🔑 إدارة المفاتيح الخاصة", desc: "أنشئ مفاتيح خاصة قوية دون اتصال بالإنترنت واحتفظ بها بأمان. لا تخزنها رقمياً أو في خدمات التخزين السحابي." },
            { title: "📝 نسخ احتياطي لعبارة البذرة", desc: "اكتب عبارة البذرة المكونة من 12-24 كلمة على ورق أو معدن. احتفظ بنسخ في مواقع آمنة متعددة." },
            { title: "🔒 كلمات مرور قوية", desc: "استخدم كلمات مرور فريدة ومعقدة لكل محفظة وحساب صرافة. فكر في استخدام مدير كلمات المرور." },
            { title: "🔄 التحديثات المنتظمة", desc: "حافظ على تحديث برنامج محفظتك إلى أحدث إصدار للحماية من الثغرات المعروفة." }
          ]
        },
        exchangeSecurity: {
          title: "أمان البورصات والمنصات",
          content: "البورصات المركزية مريحة ولكنها تطرح تحديات أمنية فريدة. اتبع هذه الممارسات لتقليل المخاطر:",
          guidelines: [
            {
              title: "المصادقة الثنائية (2FA)",
              desc: "فعل 2FA باستخدام تطبيقات المصادقة مثل Google Authenticator أو Authy. تجنب 2FA القائم على SMS عندما يكون ذلك ممكناً.",
              priority: "حرج"
            },
            {
              title: "القائمة البيضاء للسحب",
              desc: "اسمح بالسحب فقط إلى عناوين معتمدة مسبقاً. هذا يمنع السحوبات غير المصرح بها حتى لو تم اختراق حسابك.",
              priority: "عالي"
            },
            {
              title: "مراجعات الأمان المنتظمة",
              desc: "راجع نشاط حسابك بانتظام. تحقق من سجل تسجيل الدخول وسجلات المعاملات لأي نشاط مشبوه.",
              priority: "متوسط"
            },
            {
              title: "نقل التخزين البارد",
              desc: "لا تحتفظ بمبالغ كبيرة في البورصات. انقل إلى محافظ الأجهزة أو التخزين البارد للاحتفاظ طويل المدى.",
              priority: "حرج"
            }
          ]
        },
        phishingScams: {
          title: "الحماية من التصيد والاحتيال",
          content: "يستخدم مجرمو الإنترنت تقنيات متطورة لسرقة عملاتك المشفرة. تعلم التعرف على هذه التهديدات وتجنبها:",
          threats: [
            {
              type: "مواقع التصيد",
              description: "مواقع مزيفة تبدو مطابقة للبورصات أو المحافظ الشرعية",
              redFlags: ["اختلافات طفيفة في URL", "HTTP بدلاً من HTTPS", "إملاء/نحو ضعيف"],
              protection: "ضع دائماً إشارات مرجعية للمواقع الرسمية وتحقق من عناوين URL بعناية"
            },
            {
              type: "الهندسة الاجتماعية",
              description: "محتالون يتنكرون في هيئة موظفي دعم أو مؤثرين لكسب ثقتك",
              redFlags: ["رسائل غير مرغوب فيها", "تكتيكات الإلحاح", "طلبات للمفاتيح الخاصة"],
              protection: "لا تشارك أبداً معلومات حساسة عبر وسائل التواصل الاجتماعي أو الرسائل"
            },
            {
              type: "تطبيقات الهاتف المحمول المزيفة",
              description: "تطبيقات ضارة تسرق معلومات المحفظة أو المفاتيح الخاصة",
              redFlags: ["متاجر تطبيقات غير رسمية", "تقييمات ضعيفة", "أذونات مفرطة"],
              protection: "حمل التطبيقات فقط من المتاجر الرسمية وتحقق من صحة المطور"
            },
            {
              type: "احتيال الاستثمار",
              description: "وعود بعوائد مضمونة أو مخططات 'الثراء السريع'",
              redFlags: ["عوائد غير واقعية", "ضغط للاستثمار بسرعة", "نموذج عمل غير واضح"],
              protection: "ابحث بدقة وتذكر: إذا بدا جيداً جداً ليكون حقيقياً، فربما ليس كذلك"
            }
          ]
        },
        hardwareSecurity: {
          title: "أمان الأجهزة والمعدات",
          content: "أجهزتك هي نقاط دخول محتملة للمهاجمين. أمنها بشكل صحيح لحماية أصول العملات المشفرة الخاصة بك.",
          measures: [
            {
              icon: "💻",
              title: "بيئة حاسوبية آمنة",
              desc: "استخدم أجهزة مخصصة لمعاملات العملات المشفرة. حافظ على تحديث أنظمة التشغيل وشغل برامج مكافحة الفيروسات.",
              tips: ["حاسوب مخصص للعملات المشفرة", "تحديثات OS منتظمة", "حماية مكافحة الفيروسات", "تجنب WiFi العام"]
            },
            {
              icon: "📱",
              title: "أمان الأجهزة المحمولة",
              desc: "أمن أجهزتك المحمولة بـ PINs قوية وأقفال بيومترية وكلمات مرور خاصة بالتطبيق.",
              tips: ["أقفال شاشة قوية", "مراجعة أذونات التطبيق", "متاجر التطبيقات الرسمية فقط", "تحديثات أمنية منتظمة"]
            },
            {
              icon: "🔌",
              title: "محافظ الأجهزة",
              desc: "استخدم محافظ أجهزة موثوقة لتخزين مبالغ كبيرة. اشتر مباشرة من الشركات المصنعة.",
              tips: ["الشراء من مصادر رسمية", "التحقق من صحة البرامج الثابتة", "استخدام PIN آمن", "تخزين بذرة الاسترداد بأمان"]
            },
            {
              icon: "🌐",
              title: "أمان الشبكة",
              desc: "استخدم شبكات آمنة وفكر في VPN للحماية الإضافية للخصوصية.",
              tips: ["تجنب WiFi العام", "استخدام خدمات VPN", "جهاز توجيه منزلي آمن", "مراقبة حركة مرور الشبكة"]
            }
          ]
        },
        emergencyPrep: {
          title: "الاستعداد للطوارئ",
          content: "استعد لسيناريوهات مختلفة لضمان قدرتك دائماً على الوصول إلى أموالك عند الحاجة.",
          scenarios: [
            {
              scenario: "فقدان/سرقة الجهاز",
              preparation: "نسخ احتياطية آمنة لجميع بيانات المحفظة وعبارات الاسترداد في مواقع متعددة",
              response: "نقل الأموال فوراً إلى محفظة جديدة باستخدام عبارة الاسترداد الاحتياطية"
            },
            {
              scenario: "الوفاة/العجز",
              preparation: "إنشاء خطة وراثة آمنة مع أفراد الأسرة الموثوقين أو الخدمات القانونية",
              response: "المستفيدون يتبعون إجراءات الوصول المحددة مسبقاً"
            },
            {
              scenario: "اختراق/إغلاق البورصة",
              preparation: "لا تخزن مبالغ كبيرة في البورصات؛ امتلك حسابات بورصة متعددة",
              response: "سحب الأموال بسرعة إلى محافظ شخصية إذا أمكن"
            },
            {
              scenario: "القيود الحكومية",
              preparation: "فهم اللوائح المحلية؛ فكر في الخيارات اللامركزية",
              response: "اتباع المتطلبات القانونية مع حماية الوصول إلى الأصول"
            }
          ]
        },
        advancedSecurity: {
          title: "تقنيات الأمان المتقدمة",
          content: "للمستخدمين الذين لديهم ممتلكات كبيرة، توفر هذه التقنيات المتقدمة طبقات إضافية من الأمان:",
          techniques: [
            {
              name: "المحافظ متعددة التوقيع",
              description: "تتطلب توقيعات متعددة لتفويض المعاملات",
              useCase: "الممتلكات الكبيرة، حسابات الأعمال، الحضانة المشتركة",
              complexity: "عالي"
            },
            {
              name: "حلول التخزين البارد",
              description: "طرق تخزين غير متصلة بالإنترنت تماماً للأمان الأقصى",
              useCase: "التخزين طويل المدى، تخطيط الوراثة",
              complexity: "متوسط"
            },
            {
              name: "المحافظ الخداعية",
              description: "مبالغ صغيرة في محافظ يسهل الوصول إليها لتضليل المهاجمين",
              useCase: "تهديدات الأمان المادي، حالات السفر",
              complexity: "متوسط"
            },
            {
              name: "المعاملات مقفلة زمنياً",
              description: "معاملات لا يمكن تنفيذها إلا بعد فترات زمنية محددة",
              useCase: "الوراثة، الادخار القسري، تأخيرات الأمان",
              complexity: "عالي"
            }
          ]
        },
        securityChecklist: {
          title: "قائمة مراجعة الأمان",
          items: [
            "✅ فعل 2FA على جميع الحسابات المتعلقة بالعملات المشفرة",
            "✅ استخدم محفظة أجهزة للمبالغ الكبيرة",
            "✅ انسخ عبارات البذرة احتياطياً بأمان (دون اتصال، مواقع متعددة)",
            "✅ استخدم كلمات مرور فريدة وقوية لكل خدمة",
            "✅ حدث جميع البرامج والبرامج الثابتة بانتظام",
            "✅ تحقق من جميع URLs وصحة التطبيق قبل الاستخدام",
            "✅ لا تشارك أبداً المفاتيح الخاصة أو عبارات البذرة",
            "✅ استخدم شبكات آمنة لمعاملات العملات المشفرة",
            "✅ راقب الحسابات بانتظام للأنشطة المشبوهة",
            "✅ امتلك خطة وصول للطوارئ"
          ]
        },
        cta: {
          title: "أمن رحلة العملات المشفرة الخاصة بك",
          desc: "طبق هذه الممارسات الأمنية اليوم واحم استثماراتك بميزات الأمان المدمجة في محفظة مخبة.",
          button: "أمن محفظتك"
        }
      }
    }
  };

  const currentContent = content[locale as 'en' | 'ar'] || content.en;

  return (
    <main className={`relative bg-gradient-to-b from-white to-[#73AED2] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content */}
      <div className="pt-24 pb-16 min-h-screen">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <Link 
              href={`/${locale}/learn`}
              className="text-primary hover:text-primary/80 flex items-center mb-4"
            >
              <span className={`material-symbols-outlined ${isRTL ? 'ml-2 scale-x-[-1]' : 'mr-2'}`}>arrow_back</span>
              {currentContent.backToLearn}
            </Link>
          </nav>

          {/* Article Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-6">
              <span className="material-symbols-outlined text-3xl text-primary">security</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-blue-950 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-blue-900/70 max-w-2xl mx-auto">
              {currentContent.subtitle}
            </p>
            <div className="flex items-center justify-center gap-4 mt-4 text-sm text-blue-900/60">
              <span>🛡️ {locale === 'ar' ? 'ضروري' : 'Essential'}</span>
              <span>•</span>
              <span>⏱️ {locale === 'ar' ? '12 دقيقة قراءة' : '12 min read'}</span>
            </div>
          </motion.div>

          {/* Article Content */}
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            {/* Table of Contents */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-bold text-blue-950 mb-4">{currentContent.tableOfContents}</h3>
              <ul className="space-y-2 text-blue-900">
                <li><a href="#wallet-security" className="hover:text-primary transition">1. {currentContent.sections.walletSecurity.title}</a></li>
                <li><a href="#exchange-security" className="hover:text-primary transition">2. {currentContent.sections.exchangeSecurity.title}</a></li>
                <li><a href="#phishing-scams" className="hover:text-primary transition">3. {currentContent.sections.phishingScams.title}</a></li>
                <li><a href="#hardware-security" className="hover:text-primary transition">4. {currentContent.sections.hardwareSecurity.title}</a></li>
                <li><a href="#emergency-prep" className="hover:text-primary transition">5. {currentContent.sections.emergencyPrep.title}</a></li>
                <li><a href="#advanced-security" className="hover:text-primary transition">6. {currentContent.sections.advancedSecurity.title}</a></li>
                <li><a href="#security-checklist" className="hover:text-primary transition">7. {currentContent.sections.securityChecklist.title}</a></li>
              </ul>
            </div>

            {/* Wallet Security Section */}
            <section id="wallet-security" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>wallet</span>
                {currentContent.sections.walletSecurity.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.walletSecurity.content[0]}</p>
                <p>{currentContent.sections.walletSecurity.content[1]}</p>
                <div className={`bg-red-50 border-red-400 p-4 rounded ${isRTL ? 'border-r-4' : 'border-l-4'}`}>
                  <p className="font-medium text-red-800">
                    {currentContent.sections.walletSecurity.content[2]}
                  </p>
                </div>
                <div className="grid md:grid-cols-2 gap-6 my-6">
                  {currentContent.sections.walletSecurity.practices.map((practice, index) => (
                    <div key={index} className="bg-blue-50 p-6 rounded-xl">
                      <h4 className="font-bold text-blue-950 mb-3">{practice.title}</h4>
                      <p className="text-sm">{practice.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Exchange Security Section */}
            <section id="exchange-security" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>currency_exchange</span>
                {currentContent.sections.exchangeSecurity.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.exchangeSecurity.content}</p>
              <div className="space-y-4">
                {currentContent.sections.exchangeSecurity.guidelines.map((guideline, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{guideline.title}</h4>
                      <span className={`text-xs px-3 py-1 rounded-full ${
                        guideline.priority === 'Critical' || guideline.priority === 'حرج' ? 'bg-red-100 text-red-700' :
                        guideline.priority === 'High' || guideline.priority === 'عالي' ? 'bg-orange-100 text-orange-700' :
                        'bg-yellow-100 text-yellow-700'
                      }`}>
                        {guideline.priority}
                      </span>
                    </div>
                    <p className="text-blue-900/70 text-sm">{guideline.desc}</p>
                  </div>
                ))}
              </div>
            </section>

            {/* Phishing & Scams Section */}
            <section id="phishing-scams" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>phishing</span>
                {currentContent.sections.phishingScams.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.phishingScams.content}</p>
              <div className="space-y-6">
                {currentContent.sections.phishingScams.threats.map((threat, index) => (
                  <div key={index} className="bg-orange-50 border border-orange-200 p-6 rounded-xl">
                    <h4 className="font-bold text-orange-900 mb-3">⚠️ {threat.type}</h4>
                    <p className="text-orange-800 mb-4">{threat.description}</p>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-semibold text-red-700 mb-2">{locale === 'ar' ? 'علامات حمراء:' : 'Red Flags:'}</h5>
                        <ul className="text-sm text-red-600 space-y-1">
                          {threat.redFlags.map((flag, flagIndex) => (
                            <li key={flagIndex}>• {flag}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'الحماية:' : 'Protection:'}</h5>
                        <p className="text-sm text-green-600">{threat.protection}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Hardware Security Section */}
            <section id="hardware-security" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>devices</span>
                {currentContent.sections.hardwareSecurity.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.hardwareSecurity.content}</p>
              <div className="space-y-6">
                {currentContent.sections.hardwareSecurity.measures.map((measure, index) => (
                  <div key={index} className="bg-purple-50 p-6 rounded-xl">
                    <div className="flex items-start gap-4">
                      <span className="text-3xl">{measure.icon}</span>
                      <div className="flex-1">
                        <h4 className="font-bold text-blue-950 mb-3">{measure.title}</h4>
                        <p className="text-purple-900/70 mb-4">{measure.desc}</p>
                        <div className="flex flex-wrap gap-2">
                          {measure.tips.map((tip, tipIndex) => (
                            <span key={tipIndex} className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                              {tip}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Emergency Preparedness Section */}
            <section id="emergency-prep" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>emergency</span>
                {currentContent.sections.emergencyPrep.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.emergencyPrep.content}</p>
              <div className="space-y-4">
                {currentContent.sections.emergencyPrep.scenarios.map((scenario, index) => (
                  <div key={index} className="bg-yellow-50 border border-yellow-200 p-6 rounded-xl">
                    <h4 className="font-bold text-yellow-900 mb-3">🚨 {scenario.scenario}</h4>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2">{locale === 'ar' ? 'التحضير:' : 'Preparation:'}</h5>
                        <p className="text-yellow-800">{scenario.preparation}</p>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'الاستجابة:' : 'Response:'}</h5>
                        <p className="text-yellow-800">{scenario.response}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Advanced Security Section */}
            <section id="advanced-security" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>enhanced_encryption</span>
                {currentContent.sections.advancedSecurity.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.advancedSecurity.content}</p>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.advancedSecurity.techniques.map((technique, index) => (
                  <div key={index} className="bg-indigo-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{technique.name}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        technique.complexity === 'High' || technique.complexity === 'عالي' ? 'bg-red-100 text-red-700' :
                        'bg-orange-100 text-orange-700'
                      }`}>
                        {technique.complexity}
                      </span>
                    </div>
                    <p className="text-indigo-900/70 text-sm mb-3">{technique.description}</p>
                    <div className="text-xs text-indigo-600 bg-indigo-100 px-2 py-1 rounded">
                      {locale === 'ar' ? 'حالة الاستخدام: ' : 'Use Case: '}{technique.useCase}
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Security Checklist Section */}
            <section id="security-checklist" className="mb-8">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>checklist</span>
                {currentContent.sections.securityChecklist.title}
              </h2>
              <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                <ul className="space-y-3">
                  {currentContent.sections.securityChecklist.items.map((item, index) => (
                    <li key={index} className="text-green-800 flex items-start gap-2">
                      <span className="text-green-600 mt-1">{item.split(' ')[0]}</span>
                      <span>{item.substring(item.indexOf(' ') + 1)}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </section>

            {/* Call to Action */}
            <div className="text-center pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-blue-950 mb-4">{currentContent.sections.cta.title}</h3>
              <p className="text-blue-900/70 mb-6">{currentContent.sections.cta.desc}</p>
              <Link href={`/${locale}/app`} className="btn-primary">
                {currentContent.sections.cta.button}
              </Link>
            </div>
          </motion.article>
        </div>
      </div>

      <Footer />
    </main>
  );
} 