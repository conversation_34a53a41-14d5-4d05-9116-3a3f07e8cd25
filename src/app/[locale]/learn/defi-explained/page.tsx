'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function DefiExplainedPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Content for both languages
  const content = {
    en: {
      title: "DeFi Explained",
      subtitle: "Decentralized Finance fundamentals, protocols, risks, and opportunities in the new financial ecosystem",
      backToLearn: "Back to Learn",
      tableOfContents: "📋 Table of Contents",
      sections: {
        whatIsDefi: {
          title: "What is DeFi?",
          content: [
            "Decentralized Finance (DeFi) represents a paradigm shift from traditional, centralized financial systems to peer-to-peer finance enabled by decentralized technologies built on Ethereum and other programmable blockchains.",
            "Unlike traditional finance, DeFi eliminates intermediaries like banks, brokers, and exchanges by using smart contracts to automate financial services.",
            "💡 Key Principle: DeFi operates on the principle of 'Don't trust, verify' - every transaction and rule is transparent and verifiable on the blockchain."
          ],
          characteristics: [
            { title: "🌐 Permissionless", desc: "Anyone with an internet connection can access DeFi services without approval from gatekeepers" },
            { title: "🔓 Non-custodial", desc: "Users maintain control of their assets without relying on third-party custodians" },
            { title: "🔍 Transparent", desc: "All transactions and smart contract code are publicly verifiable on the blockchain" },
            { title: "🌍 Global", desc: "Available 24/7 worldwide without geographical restrictions or traditional banking hours" },
            { title: "⚡ Programmable", desc: "Smart contracts enable complex financial instruments and automated execution" },
            { title: "🔄 Composable", desc: "DeFi protocols can be combined like 'money legos' to create new financial products" }
          ]
        },
        coreProtocols: {
          title: "Core DeFi Protocols",
          content: "DeFi ecosystem consists of various protocol types, each serving specific financial functions:",
          categories: [
            {
              name: "Decentralized Exchanges (DEXs)",
              description: "Platforms for trading cryptocurrencies without intermediaries",
              examples: ["Uniswap", "SushiSwap", "1inch", "Curve"],
              features: ["Automated Market Makers (AMM)", "Liquidity pools", "No KYC required", "24/7 trading"],
              icon: "swap_horiz"
            },
            {
              name: "Lending & Borrowing",
              description: "Protocols that enable users to lend assets for yield or borrow against collateral",
              examples: ["Aave", "Compound", "MakerDAO", "Venus"],
              features: ["Overcollateralized loans", "Flash loans", "Interest rate models", "Liquidation mechanisms"],
              icon: "account_balance"
            },
            {
              name: "Stablecoins",
              description: "Cryptocurrencies designed to maintain stable value relative to reference assets",
              examples: ["DAI", "USDC", "USDT", "FRAX"],
              features: ["Price stability", "Collateral backing", "Algorithmic mechanisms", "Cross-border transfers"],
              icon: "payments"
            },
            {
              name: "Yield Farming",
              description: "Strategies to earn rewards by providing liquidity or staking tokens",
              examples: ["Yearn Finance", "Convex", "Beefy", "AutoFarm"],
              features: ["Liquidity mining", "Reward tokens", "Auto-compounding", "Strategy optimization"],
              icon: "agriculture"
            },
            {
              name: "Derivatives",
              description: "Financial contracts whose value derives from underlying assets",
              examples: ["Synthetix", "dYdX", "GMX", "Perpetual Protocol"],
              features: ["Synthetic assets", "Leveraged trading", "Options", "Futures contracts"],
              icon: "trending_up"
            },
            {
              name: "Insurance",
              description: "Protocols providing coverage against smart contract risks and other DeFi risks",
              examples: ["Nexus Mutual", "Cover Protocol", "Unslashed", "Risk Harbor"],
              features: ["Smart contract coverage", "Mutual risk sharing", "Claim assessment", "Premium calculations"],
              icon: "security"
            }
          ]
        },
        howDefiWorks: {
          title: "How DeFi Works",
          content: "Understanding the fundamental mechanisms that power decentralized finance:",
          mechanisms: [
            {
              concept: "Smart Contracts",
              explanation: "Self-executing contracts with terms directly written into code",
              benefits: ["Automatic execution", "Reduced counterparty risk", "Transparent rules", "No intermediaries"],
              example: "A lending smart contract automatically liquidates collateral when loan-to-value ratio exceeds threshold"
            },
            {
              concept: "Liquidity Pools",
              explanation: "Pools of tokens locked in smart contracts that provide liquidity for trading",
              benefits: ["24/7 liquidity", "Permissionless market making", "Earning fees", "Price discovery"],
              example: "Users deposit ETH and USDC to create a trading pair, earning fees from each trade"
            },
            {
              concept: "Automated Market Makers (AMM)",
              explanation: "Mathematical formulas that automatically price assets based on supply and demand",
              benefits: ["No order books needed", "Continuous liquidity", "Algorithmic pricing", "Reduced slippage"],
              example: "Uniswap's x*y=k formula ensures that product of token reserves remains constant"
            },
            {
              concept: "Governance Tokens",
              explanation: "Tokens that give holders voting rights on protocol decisions and upgrades",
              benefits: ["Decentralized governance", "Community ownership", "Protocol evolution", "Aligned incentives"],
              example: "UNI token holders vote on Uniswap protocol upgrades and fee distributions"
            }
          ]
        },
        defiOpportunities: {
          title: "DeFi Opportunities",
          content: "Various ways to participate and earn in the DeFi ecosystem:",
          strategies: [
            {
              strategy: "Liquidity Provision",
              description: "Provide tokens to liquidity pools and earn trading fees",
              riskLevel: "Medium",
              potentialAPY: "5-50%",
              requirements: "Token pairs, gas fees",
              considerations: "Impermanent loss risk, smart contract risk"
            },
            {
              strategy: "Yield Farming",
              description: "Stake LP tokens or other assets to earn additional reward tokens",
              riskLevel: "High",
              potentialAPY: "10-1000%+",
              requirements: "Liquidity provision, active management",
              considerations: "High volatility, complex strategies, contract risks"
            },
            {
              strategy: "Lending",
              description: "Lend cryptocurrencies to earn interest from borrowers",
              riskLevel: "Low-Medium",
              potentialAPY: "1-15%",
              requirements: "Cryptocurrency holdings",
              considerations: "Platform risk, interest rate fluctuations"
            },
            {
              strategy: "Borrowing",
              description: "Borrow against crypto collateral for leverage or liquidity",
              riskLevel: "High",
              potentialAPY: "Variable",
              requirements: "Collateral, liquidation awareness",
              considerations: "Liquidation risk, interest costs, market volatility"
            },
            {
              strategy: "Arbitrage",
              description: "Profit from price differences across different platforms",
              riskLevel: "Medium-High",
              potentialAPY: "Variable",
              requirements: "Capital, speed, technical knowledge",
              considerations: "MEV competition, gas costs, execution risk"
            },
            {
              strategy: "Governance Participation",
              description: "Hold governance tokens and participate in protocol decisions",
              riskLevel: "Medium",
              potentialAPY: "Variable",
              requirements: "Governance tokens, time commitment",
              considerations: "Token price volatility, governance complexity"
            }
          ]
        },
        defiRisks: {
          title: "DeFi Risks & Challenges",
          content: "Understanding and managing risks is crucial for safe DeFi participation:",
          riskCategories: [
            {
              category: "Smart Contract Risk",
              description: "Bugs, vulnerabilities, or exploits in smart contract code",
              examples: ["Code bugs leading to fund loss", "Flash loan attacks", "Reentrancy exploits"],
              mitigation: ["Use audited protocols", "Start with small amounts", "Diversify across platforms", "Stay updated on security news"]
            },
            {
              category: "Impermanent Loss",
              description: "Loss compared to holding assets when providing liquidity",
              examples: ["Price divergence in LP pairs", "Volatile asset pairs", "Large price movements"],
              mitigation: ["Choose stable pairs", "Understand IL calculators", "Consider IL protection protocols", "Factor in trading fees"]
            },
            {
              category: "Liquidation Risk",
              description: "Forced sale of collateral when loan-to-value ratio exceeds threshold",
              examples: ["Market crashes", "Price volatility", "Oracle manipulation"],
              mitigation: ["Conservative LTV ratios", "Monitor positions", "Set up alerts", "Keep emergency funds"]
            },
            {
              category: "Regulatory Risk",
              description: "Changing regulations affecting DeFi protocols and users",
              examples: ["Protocol shutdowns", "Geographic restrictions", "Tax implications"],
              mitigation: ["Stay informed on regulations", "Use compliant platforms", "Maintain transaction records", "Consult legal advice"]
            },
            {
              category: "Market Risk",
              description: "Cryptocurrency price volatility affecting portfolio value",
              examples: ["Bear markets", "Token depegging", "Correlation risks"],
              mitigation: ["Diversification", "Risk management", "Position sizing", "Hedging strategies"]
            },
            {
              category: "Technical Risk",
              description: "Blockchain network issues, gas fees, and user errors",
              examples: ["Network congestion", "High gas fees", "Transaction failures", "User mistakes"],
              mitigation: ["Understand gas mechanics", "Double-check transactions", "Use testnets first", "Keep backups"]
            }
          ]
        },
        gettingStarted: {
          title: "Getting Started with DeFi",
          content: "Step-by-step guide to safely enter the DeFi ecosystem:",
          steps: [
            {
              step: "1. Education & Preparation",
              description: "Learn DeFi fundamentals before investing",
              actions: ["Read whitepapers", "Understand key concepts", "Learn about risks", "Follow reputable sources"],
              timeFrame: "1-2 weeks"
            },
            {
              step: "2. Wallet Setup",
              description: "Set up a non-custodial wallet for DeFi interactions",
              actions: ["Choose wallet (MetaMask, Trust Wallet)", "Secure seed phrase", "Enable security features", "Practice transactions"],
              timeFrame: "1-2 days"
            },
            {
              step: "3. Start Small",
              description: "Begin with small amounts to gain experience",
              actions: ["Buy major cryptocurrencies", "Try simple lending", "Provide liquidity to stable pairs", "Track performance"],
              timeFrame: "1-2 weeks"
            },
            {
              step: "4. Expand Gradually",
              description: "Slowly increase exposure as knowledge grows",
              actions: ["Try different protocols", "Explore yield farming", "Learn advanced strategies", "Join communities"],
              timeFrame: "Ongoing"
            },
            {
              step: "5. Risk Management",
              description: "Implement proper risk management practices",
              actions: ["Diversify protocols", "Set position limits", "Monitor regularly", "Prepare exit strategies"],
              timeFrame: "Ongoing"
            }
          ]
        },
        defiEcosystem: {
          title: "DeFi Ecosystem Overview",
          content: "The interconnected network of protocols, tokens, and participants:",
          layers: [
            {
              layer: "Layer 1 - Blockchains",
              description: "Base layer blockchains that support DeFi applications",
              components: ["Ethereum", "Binance Smart Chain", "Polygon", "Avalanche", "Solana"],
              function: "Provide security, decentralization, and programmability"
            },
            {
              layer: "Layer 2 - Protocols",
              description: "Smart contract protocols that provide financial services",
              components: ["DEXs", "Lending protocols", "Yield farms", "Insurance", "Derivatives"],
              function: "Execute financial logic and manage user funds"
            },
            {
              layer: "Layer 3 - Applications",
              description: "User-facing applications and interfaces",
              components: ["Web interfaces", "Mobile apps", "Portfolio trackers", "Analytics tools"],
              function: "Provide user experience and accessibility"
            },
            {
              layer: "Layer 4 - Aggregators",
              description: "Services that optimize and aggregate DeFi protocols",
              components: ["1inch", "Yearn Finance", "Paraswap", "DeFi Pulse"],
              function: "Find best rates and automate strategies"
            }
          ]
        },
        futureOfDefi: {
          title: "Future of DeFi",
          content: "Emerging trends and developments shaping DeFi's evolution:",
          trends: [
            {
              trend: "Cross-Chain DeFi",
              description: "Protocols operating across multiple blockchain networks",
              impact: "Increased liquidity and reduced fragmentation"
            },
            {
              trend: "Institutional Adoption",
              description: "Traditional financial institutions integrating DeFi protocols",
              impact: "Increased legitimacy and capital inflow"
            },
            {
              trend: "Regulatory Clarity",
              description: "Clearer regulations providing framework for DeFi operations",
              impact: "Reduced uncertainty and mainstream adoption"
            },
            {
              trend: "Improved UX",
              description: "Better user interfaces and simplified interactions",
              impact: "Wider accessibility and user adoption"
            },
            {
              trend: "Layer 2 Solutions",
              description: "Scaling solutions reducing transaction costs and increasing speed",
              impact: "Enhanced usability and broader participation"
            },
            {
              trend: "Real-World Assets",
              description: "Tokenization of traditional assets like real estate and bonds",
              impact: "Bridge between traditional and decentralized finance"
            }
          ]
        },
        cta: {
          title: "Start Your DeFi Journey",
          desc: "Begin exploring decentralized finance with Mokhba Wallet's integrated DeFi features and built-in security.",
          button: "Explore DeFi"
        }
      }
    },
    ar: {
      title: "شرح التمويل اللامركزي (DeFi)",
      subtitle: "أساسيات التمويل اللامركزي، البروتوكولات، المخاطر، والفرص في النظام المالي الجديد",
      backToLearn: "العودة إلى التعلم",
      tableOfContents: "📋 فهرس المحتويات",
      sections: {
        whatIsDefi: {
          title: "ما هو التمويل اللامركزي (DeFi)؟",
          content: [
            "التمويل اللامركزي (DeFi) يمثل تحولاً جذرياً من الأنظمة المالية التقليدية المركزية إلى التمويل من نظير إلى نظير المدعوم بالتقنيات اللامركزية المبنية على إيثريوم وبلوك تشين أخرى قابلة للبرمجة.",
            "على عكس التمويل التقليدي، يلغي DeFi الوسطاء مثل البنوك والوسطاء والبورصات باستخدام العقود الذكية لأتمتة الخدمات المالية.",
            "💡 المبدأ الأساسي: يعمل DeFi على مبدأ 'لا تثق، تحقق' - كل معاملة وقاعدة شفافة وقابلة للتحقق على البلوك تشين."
          ],
          characteristics: [
            { title: "🌐 بدون إذن", desc: "يمكن لأي شخص لديه اتصال بالإنترنت الوصول إلى خدمات DeFi دون موافقة من حراس البوابات" },
            { title: "🔓 غير احتجازي", desc: "يحتفظ المستخدمون بالسيطرة على أصولهم دون الاعتماد على أمناء طرف ثالث" },
            { title: "🔍 شفاف", desc: "جميع المعاملات وكود العقود الذكية قابلة للتحقق علناً على البلوك تشين" },
            { title: "🌍 عالمي", desc: "متاح على مدار الساعة طوال أيام الأسبوع في جميع أنحاء العالم دون قيود جغرافية أو ساعات مصرفية تقليدية" },
            { title: "⚡ قابل للبرمجة", desc: "تمكن العقود الذكية من إنشاء أدوات مالية معقدة وتنفيذ آلي" },
            { title: "🔄 قابل للتركيب", desc: "يمكن دمج بروتوكولات DeFi مثل 'ليغو المال' لإنشاء منتجات مالية جديدة" }
          ]
        },
        coreProtocols: {
          title: "بروتوكولات DeFi الأساسية",
          content: "يتكون نظام DeFi من أنواع مختلفة من البروتوكولات، كل منها يخدم وظائف مالية محددة:",
          categories: [
            {
              name: "البورصات اللامركزية (DEXs)",
              description: "منصات لتداول العملات المشفرة دون وسطاء",
              examples: ["Uniswap", "SushiSwap", "1inch", "Curve"],
              features: ["صناع السوق الآليين (AMM)", "مجمعات السيولة", "لا حاجة لـ KYC", "تداول على مدار الساعة"],
              icon: "swap_horiz"
            },
            {
              name: "الإقراض والاقتراض",
              description: "بروتوكولات تمكن المستخدمين من إقراض الأصول للحصول على عائد أو الاقتراض مقابل ضمانات",
              examples: ["Aave", "Compound", "MakerDAO", "Venus"],
              features: ["قروض مفرطة الضمان", "قروض فلاش", "نماذج أسعار الفائدة", "آليات التصفية"],
              icon: "account_balance"
            },
            {
              name: "العملات المستقرة",
              description: "عملات مشفرة مصممة للحفاظ على قيمة مستقرة نسبة إلى الأصول المرجعية",
              examples: ["DAI", "USDC", "USDT", "FRAX"],
              features: ["استقرار السعر", "دعم الضمانات", "آليات خوارزمية", "تحويلات عبر الحدود"],
              icon: "payments"
            },
            {
              name: "زراعة العائد",
              description: "استراتيجيات لكسب المكافآت من خلال توفير السيولة أو رهن الرموز",
              examples: ["Yearn Finance", "Convex", "Beefy", "AutoFarm"],
              features: ["تعدين السيولة", "رموز المكافآت", "التركيب التلقائي", "تحسين الاستراتيجية"],
              icon: "agriculture"
            },
            {
              name: "المشتقات",
              description: "عقود مالية تستمد قيمتها من الأصول الأساسية",
              examples: ["Synthetix", "dYdX", "GMX", "Perpetual Protocol"],
              features: ["أصول اصطناعية", "تداول بالرافعة المالية", "خيارات", "عقود آجلة"],
              icon: "trending_up"
            },
            {
              name: "التأمين",
              description: "بروتوكولات توفر تغطية ضد مخاطر العقود الذكية ومخاطر DeFi الأخرى",
              examples: ["Nexus Mutual", "Cover Protocol", "Unslashed", "Risk Harbor"],
              features: ["تغطية العقود الذكية", "مشاركة المخاطر المتبادلة", "تقييم المطالبات", "حسابات الأقساط"],
              icon: "security"
            }
          ]
        },
        howDefiWorks: {
          title: "كيف يعمل DeFi",
          content: "فهم الآليات الأساسية التي تشغل التمويل اللامركزي:",
          mechanisms: [
            {
              concept: "العقود الذكية",
              explanation: "عقود تنفذ نفسها بنفسها مع الشروط المكتوبة مباشرة في الكود",
              benefits: ["تنفيذ تلقائي", "تقليل مخاطر الطرف المقابل", "قواعد شفافة", "لا وسطاء"],
              example: "عقد إقراض ذكي يصفي الضمانات تلقائياً عندما تتجاوز نسبة القرض إلى القيمة العتبة"
            },
            {
              concept: "مجمعات السيولة",
              explanation: "مجمعات من الرموز مقفلة في عقود ذكية توفر السيولة للتداول",
              benefits: ["سيولة على مدار الساعة", "صنع السوق بدون إذن", "كسب الرسوم", "اكتشاف الأسعار"],
              example: "يودع المستخدمون ETH و USDC لإنشاء زوج تداول، ويكسبون رسوماً من كل تداول"
            },
            {
              concept: "صناع السوق الآليين (AMM)",
              explanation: "صيغ رياضية تسعر الأصول تلقائياً بناءً على العرض والطلب",
              benefits: ["لا حاجة لدفاتر الطلبات", "سيولة مستمرة", "تسعير خوارزمي", "تقليل الانزلاق"],
              example: "صيغة x*y=k في Uniswap تضمن أن منتج احتياطيات الرموز يبقى ثابتاً"
            },
            {
              concept: "رموز الحوكمة",
              explanation: "رموز تمنح حامليها حقوق التصويت على قرارات البروتوكول والترقيات",
              benefits: ["حوكمة لامركزية", "ملكية المجتمع", "تطور البروتوكول", "حوافز متوافقة"],
              example: "حاملو رمز UNI يصوتون على ترقيات بروتوكول Uniswap وتوزيعات الرسوم"
            }
          ]
        },
        defiOpportunities: {
          title: "فرص DeFi",
          content: "طرق مختلفة للمشاركة والكسب في نظام DeFi:",
          strategies: [
            {
              strategy: "توفير السيولة",
              description: "توفير الرموز لمجمعات السيولة وكسب رسوم التداول",
              riskLevel: "متوسط",
              potentialAPY: "5-50%",
              requirements: "أزواج الرموز، رسوم الغاز",
              considerations: "مخاطر الخسارة الوهمية، مخاطر العقود الذكية"
            },
            {
              strategy: "زراعة العائد",
              description: "رهن رموز LP أو أصول أخرى لكسب رموز مكافآت إضافية",
              riskLevel: "عالي",
              potentialAPY: "10-1000%+",
              requirements: "توفير السيولة، إدارة نشطة",
              considerations: "تقلبات عالية، استراتيجيات معقدة، مخاطر العقود"
            },
            {
              strategy: "الإقراض",
              description: "إقراض العملات المشفرة لكسب فوائد من المقترضين",
              riskLevel: "منخفض-متوسط",
              potentialAPY: "1-15%",
              requirements: "حيازات العملات المشفرة",
              considerations: "مخاطر المنصة، تقلبات أسعار الفائدة"
            },
            {
              strategy: "الاقتراض",
              description: "الاقتراض مقابل ضمانات العملات المشفرة للرافعة المالية أو السيولة",
              riskLevel: "عالي",
              potentialAPY: "متغير",
              requirements: "ضمانات، وعي بالتصفية",
              considerations: "مخاطر التصفية، تكاليف الفوائد، تقلبات السوق"
            },
            {
              strategy: "المراجحة",
              description: "الربح من فروق الأسعار عبر منصات مختلفة",
              riskLevel: "متوسط-عالي",
              potentialAPY: "متغير",
              requirements: "رأس المال، السرعة، المعرفة التقنية",
              considerations: "منافسة MEV، تكاليف الغاز، مخاطر التنفيذ"
            },
            {
              strategy: "المشاركة في الحوكمة",
              description: "حمل رموز الحوكمة والمشاركة في قرارات البروتوكول",
              riskLevel: "متوسط",
              potentialAPY: "متغير",
              requirements: "رموز الحوكمة، التزام بالوقت",
              considerations: "تقلبات أسعار الرموز، تعقيد الحوكمة"
            }
          ]
        },
        defiRisks: {
          title: "مخاطر وتحديات DeFi",
          content: "فهم وإدارة المخاطر أمر بالغ الأهمية للمشاركة الآمنة في DeFi:",
          riskCategories: [
            {
              category: "مخاطر العقود الذكية",
              description: "أخطاء أو ثغرات أو استغلال في كود العقود الذكية",
              examples: ["أخطاء الكود المؤدية لفقدان الأموال", "هجمات القروض الفلاشية", "استغلال إعادة الدخول"],
              mitigation: ["استخدم بروتوكولات مدققة", "ابدأ بمبالغ صغيرة", "نوع عبر المنصات", "ابق محدثاً بأخبار الأمان"]
            },
            {
              category: "الخسارة الوهمية",
              description: "الخسارة مقارنة بحمل الأصول عند توفير السيولة",
              examples: ["تباعد الأسعار في أزواج LP", "أزواج الأصول المتقلبة", "تحركات أسعار كبيرة"],
              mitigation: ["اختر أزواج مستقرة", "افهم حاسبات IL", "فكر في بروتوكولات حماية IL", "احسب رسوم التداول"]
            },
            {
              category: "مخاطر التصفية",
              description: "البيع القسري للضمانات عندما تتجاوز نسبة القرض إلى القيمة العتبة",
              examples: ["انهيارات السوق", "تقلبات الأسعار", "تلاعب الأوراكل"],
              mitigation: ["نسب LTV محافظة", "راقب المراكز", "ضع تنبيهات", "احتفظ بأموال طوارئ"]
            },
            {
              category: "المخاطر التنظيمية",
              description: "تغيير اللوائح التي تؤثر على بروتوكولات DeFi والمستخدمين",
              examples: ["إغلاق البروتوكولات", "قيود جغرافية", "تداعيات ضريبية"],
              mitigation: ["ابق على علم باللوائح", "استخدم منصات متوافقة", "احتفظ بسجلات المعاملات", "استشر النصائح القانونية"]
            },
            {
              category: "مخاطر السوق",
              description: "تقلبات أسعار العملات المشفرة التي تؤثر على قيمة المحفظة",
              examples: ["أسواق هابطة", "خروج الرموز عن ربطها", "مخاطر الارتباط"],
              mitigation: ["التنويع", "إدارة المخاطر", "تحجيم المراكز", "استراتيجيات التحوط"]
            },
            {
              category: "المخاطر التقنية",
              description: "مشاكل شبكة البلوك تشين، رسوم الغاز، وأخطاء المستخدم",
              examples: ["ازدحام الشبكة", "رسوم غاز عالية", "فشل المعاملات", "أخطاء المستخدم"],
              mitigation: ["افهم آليات الغاز", "تحقق من المعاملات مرتين", "استخدم شبكات الاختبار أولاً", "احتفظ بنسخ احتياطية"]
            }
          ]
        },
        gettingStarted: {
          title: "البدء مع DeFi",
          content: "دليل خطوة بخطوة للدخول بأمان إلى نظام DeFi:",
          steps: [
            {
              step: "1. التعليم والإعداد",
              description: "تعلم أساسيات DeFi قبل الاستثمار",
              actions: ["اقرأ الأوراق البيضاء", "افهم المفاهيم الأساسية", "تعلم عن المخاطر", "اتبع مصادر موثوقة"],
              timeFrame: "1-2 أسابيع"
            },
            {
              step: "2. إعداد المحفظة",
              description: "أعد محفظة غير احتجازية للتفاعل مع DeFi",
              actions: ["اختر محفظة (MetaMask، Trust Wallet)", "أمن عبارة البذرة", "فعل ميزات الأمان", "تدرب على المعاملات"],
              timeFrame: "1-2 أيام"
            },
            {
              step: "3. ابدأ صغيراً",
              description: "ابدأ بمبالغ صغيرة لاكتساب الخبرة",
              actions: ["اشتر عملات مشفرة رئيسية", "جرب إقراض بسيط", "وفر سيولة لأزواج مستقرة", "تتبع الأداء"],
              timeFrame: "1-2 أسابيع"
            },
            {
              step: "4. توسع تدريجياً",
              description: "زد التعرض ببطء مع نمو المعرفة",
              actions: ["جرب بروتوكولات مختلفة", "استكشف زراعة العائد", "تعلم استراتيجيات متقدمة", "انضم للمجتمعات"],
              timeFrame: "مستمر"
            },
            {
              step: "5. إدارة المخاطر",
              description: "طبق ممارسات إدارة المخاطر المناسبة",
              actions: ["نوع البروتوكولات", "ضع حدود للمراكز", "راقب بانتظام", "حضر استراتيجيات الخروج"],
              timeFrame: "مستمر"
            }
          ]
        },
        defiEcosystem: {
          title: "نظرة عامة على نظام DeFi",
          content: "الشبكة المترابطة من البروتوكولات والرموز والمشاركين:",
          layers: [
            {
              layer: "الطبقة 1 - البلوك تشين",
              description: "بلوك تشين الطبقة الأساسية التي تدعم تطبيقات DeFi",
              components: ["إيثريوم", "بينانس سمارت تشين", "بوليغون", "أفالانتش", "سولانا"],
              function: "توفر الأمان واللامركزية وقابلية البرمجة"
            },
            {
              layer: "الطبقة 2 - البروتوكولات",
              description: "بروتوكولات العقود الذكية التي توفر الخدمات المالية",
              components: ["DEXs", "بروتوكولات الإقراض", "مزارع العائد", "التأمين", "المشتقات"],
              function: "تنفذ المنطق المالي وتدير أموال المستخدمين"
            },
            {
              layer: "الطبقة 3 - التطبيقات",
              description: "التطبيقات والواجهات المواجهة للمستخدم",
              components: ["واجهات الويب", "تطبيقات الهاتف المحمول", "متتبعات المحافظ", "أدوات التحليل"],
              function: "توفر تجربة المستخدم وإمكانية الوصول"
            },
            {
              layer: "الطبقة 4 - المجمعون",
              description: "خدمات تحسن وتجمع بروتوكولات DeFi",
              components: ["1inch", "Yearn Finance", "Paraswap", "DeFi Pulse"],
              function: "تجد أفضل الأسعار وتؤتمت الاستراتيجيات"
            }
          ]
        },
        futureOfDefi: {
          title: "مستقبل DeFi",
          content: "الاتجاهات والتطورات الناشئة التي تشكل تطور DeFi:",
          trends: [
            {
              trend: "DeFi عبر السلاسل",
              description: "بروتوكولات تعمل عبر شبكات بلوك تشين متعددة",
              impact: "زيادة السيولة وتقليل التجزئة"
            },
            {
              trend: "التبني المؤسسي",
              description: "المؤسسات المالية التقليدية تدمج بروتوكولات DeFi",
              impact: "زيادة الشرعية وتدفق رؤوس الأموال"
            },
            {
              trend: "وضوح تنظيمي",
              description: "لوائح أوضح توفر إطار عمل لعمليات DeFi",
              impact: "تقليل عدم اليقين والتبني السائد"
            },
            {
              trend: "تحسين UX",
              description: "واجهات مستخدم أفضل وتفاعلات مبسطة",
              impact: "إمكانية وصول أوسع وتبني المستخدمين"
            },
            {
              trend: "حلول الطبقة 2",
              description: "حلول التوسيع تقلل تكاليف المعاملات وتزيد السرعة",
              impact: "سهولة استخدام محسنة ومشاركة أوسع"
            },
            {
              trend: "أصول العالم الحقيقي",
              description: "رمزنة الأصول التقليدية مثل العقارات والسندات",
              impact: "جسر بين التمويل التقليدي واللامركزي"
            }
          ]
        },
        cta: {
          title: "ابدأ رحلة DeFi الخاصة بك",
          desc: "ابدأ في استكشاف التمويل اللامركزي مع ميزات DeFi المدمجة في محفظة مخبة والأمان المدمج.",
          button: "استكشف DeFi"
        }
      }
    }
  };

  const currentContent = content[locale as 'en' | 'ar'] || content.en;

  return (
    <main className={`relative bg-gradient-to-b from-white to-[#73AED2] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content */}
      <div className="pt-24 pb-16 min-h-screen">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <Link 
              href={`/${locale}/learn`}
              className="text-primary hover:text-primary/80 flex items-center mb-4"
            >
              <span className={`material-symbols-outlined ${isRTL ? 'ml-2 scale-x-[-1]' : 'mr-2'}`}>arrow_back</span>
              {currentContent.backToLearn}
            </Link>
          </nav>

          {/* Article Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-6">
              <span className="material-symbols-outlined text-3xl text-primary">account_balance</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-blue-950 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-blue-900/70 max-w-2xl mx-auto">
              {currentContent.subtitle}
            </p>
            <div className="flex items-center justify-center gap-4 mt-4 text-sm text-blue-900/60">
              <span>🏦 {locale === 'ar' ? 'متوسط' : 'Intermediate'}</span>
              <span>•</span>
              <span>⏱️ {locale === 'ar' ? '18 دقيقة قراءة' : '18 min read'}</span>
            </div>
          </motion.div>

          {/* Article Content */}
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            {/* Table of Contents */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-bold text-blue-950 mb-4">{currentContent.tableOfContents}</h3>
              <ul className="space-y-2 text-blue-900">
                <li><a href="#what-is-defi" className="hover:text-primary transition">1. {currentContent.sections.whatIsDefi.title}</a></li>
                <li><a href="#core-protocols" className="hover:text-primary transition">2. {currentContent.sections.coreProtocols.title}</a></li>
                <li><a href="#how-defi-works" className="hover:text-primary transition">3. {currentContent.sections.howDefiWorks.title}</a></li>
                <li><a href="#defi-opportunities" className="hover:text-primary transition">4. {currentContent.sections.defiOpportunities.title}</a></li>
                <li><a href="#defi-risks" className="hover:text-primary transition">5. {currentContent.sections.defiRisks.title}</a></li>
                <li><a href="#getting-started" className="hover:text-primary transition">6. {currentContent.sections.gettingStarted.title}</a></li>
                <li><a href="#defi-ecosystem" className="hover:text-primary transition">7. {currentContent.sections.defiEcosystem.title}</a></li>
                <li><a href="#future-of-defi" className="hover:text-primary transition">8. {currentContent.sections.futureOfDefi.title}</a></li>
              </ul>
            </div>

            {/* What is DeFi Section */}
            <section id="what-is-defi" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>account_balance</span>
                {currentContent.sections.whatIsDefi.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.whatIsDefi.content[0]}</p>
                <p>{currentContent.sections.whatIsDefi.content[1]}</p>
                <div className={`bg-blue-50 border-blue-400 p-4 rounded ${isRTL ? 'border-r-4' : 'border-l-4'}`}>
                  <p className="font-medium text-blue-800">
                    {currentContent.sections.whatIsDefi.content[2]}
                  </p>
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 my-6">
                  {currentContent.sections.whatIsDefi.characteristics.map((char, index) => (
                    <div key={index} className="bg-gradient-to-br from-blue-50 to-purple-50 p-4 rounded-xl">
                      <h4 className="font-bold text-blue-950 mb-2 text-sm">{char.title}</h4>
                      <p className="text-xs text-blue-900/70">{char.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Core Protocols Section */}
            <section id="core-protocols" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>hub</span>
                {currentContent.sections.coreProtocols.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.coreProtocols.content}</p>
              <div className="space-y-6">
                {currentContent.sections.coreProtocols.categories.map((category, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <span className="material-symbols-outlined text-primary">{category.icon}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-blue-950 mb-2">{category.name}</h4>
                        <p className="text-blue-900/70 mb-3 text-sm">{category.description}</p>
                        <div className="grid md:grid-cols-2 gap-4 mb-3">
                          <div>
                            <h5 className="font-semibold text-blue-700 mb-1 text-sm">{locale === 'ar' ? 'أمثلة:' : 'Examples:'}</h5>
                            <div className="flex flex-wrap gap-1">
                              {category.examples.map((example, exIndex) => (
                                <span key={exIndex} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                  {example}
                                </span>
                              ))}
                            </div>
                          </div>
                          <div>
                            <h5 className="font-semibold text-green-700 mb-1 text-sm">{locale === 'ar' ? 'الميزات:' : 'Features:'}</h5>
                            <div className="flex flex-wrap gap-1">
                              {category.features.map((feature, featIndex) => (
                                <span key={featIndex} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                  {feature}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* How DeFi Works Section */}
            <section id="how-defi-works" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>settings</span>
                {currentContent.sections.howDefiWorks.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.howDefiWorks.content}</p>
              <div className="space-y-6">
                {currentContent.sections.howDefiWorks.mechanisms.map((mechanism, index) => (
                  <div key={index} className="bg-purple-50 border border-purple-200 p-6 rounded-xl">
                    <h4 className="font-bold text-purple-900 mb-3">⚙️ {mechanism.concept}</h4>
                    <p className="text-purple-800 mb-4">{mechanism.explanation}</p>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2">{locale === 'ar' ? 'الفوائد:' : 'Benefits:'}</h5>
                        <ul className="text-sm text-blue-600 space-y-1">
                          {mechanism.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex}>• {benefit}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'مثال:' : 'Example:'}</h5>
                        <p className="text-sm text-green-600 bg-green-50 p-3 rounded">{mechanism.example}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* DeFi Opportunities Section */}
            <section id="defi-opportunities" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>trending_up</span>
                {currentContent.sections.defiOpportunities.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.defiOpportunities.content}</p>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.defiOpportunities.strategies.map((strategy, index) => (
                  <div key={index} className="bg-green-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{strategy.strategy}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        strategy.riskLevel === 'High' || strategy.riskLevel === 'عالي' ? 'bg-red-100 text-red-700' :
                        strategy.riskLevel === 'Medium' || strategy.riskLevel === 'متوسط' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-green-100 text-green-700'
                      }`}>
                        {strategy.riskLevel}
                      </span>
                    </div>
                    <p className="text-green-900/70 text-sm mb-4">{strategy.description}</p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span className="font-medium">{locale === 'ar' ? 'العائد المتوقع:' : 'Potential APY:'}</span>
                        <span className="text-green-600 font-bold">{strategy.potentialAPY}</span>
                      </div>
                      <div>
                        <span className="font-medium">{locale === 'ar' ? 'المتطلبات:' : 'Requirements:'}</span>
                        <span className="text-gray-600 ml-1">{strategy.requirements}</span>
                      </div>
                      <div>
                        <span className="font-medium">{locale === 'ar' ? 'اعتبارات:' : 'Considerations:'}</span>
                        <span className="text-gray-600 ml-1">{strategy.considerations}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* DeFi Risks Section */}
            <section id="defi-risks" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>warning</span>
                {currentContent.sections.defiRisks.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.defiRisks.content}</p>
              <div className="space-y-6">
                {currentContent.sections.defiRisks.riskCategories.map((risk, index) => (
                  <div key={index} className="bg-orange-50 border border-orange-200 p-6 rounded-xl">
                    <h4 className="font-bold text-orange-900 mb-3">⚠️ {risk.category}</h4>
                    <p className="text-orange-800 mb-4">{risk.description}</p>
                    <div className="grid lg:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-semibold text-red-700 mb-2">{locale === 'ar' ? 'أمثلة:' : 'Examples:'}</h5>
                        <ul className="text-sm text-red-600 space-y-1">
                          {risk.examples.map((example, exampleIndex) => (
                            <li key={exampleIndex}>• {example}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2">{locale === 'ar' ? 'التخفيف:' : 'Mitigation:'}</h5>
                        <ul className="text-sm text-green-600 space-y-1">
                          {risk.mitigation.map((mitigation, mitigationIndex) => (
                            <li key={mitigationIndex}>• {mitigation}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Getting Started Section */}
            <section id="getting-started" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>rocket_launch</span>
                {currentContent.sections.gettingStarted.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.gettingStarted.content}</p>
              <div className="space-y-4">
                {currentContent.sections.gettingStarted.steps.map((step, index) => (
                  <div key={index} className="bg-blue-50 p-6 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center font-bold text-sm flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-blue-950 mb-2">{step.step}</h4>
                        <p className="text-blue-900/70 mb-3 text-sm">{step.description}</p>
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-semibold text-blue-700 mb-1 text-sm">{locale === 'ar' ? 'الإجراءات:' : 'Actions:'}</h5>
                            <ul className="text-xs text-blue-600 space-y-1">
                              {step.actions.map((action, actionIndex) => (
                                <li key={actionIndex}>• {action}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-semibold text-green-700 mb-1 text-sm">{locale === 'ar' ? 'الإطار الزمني:' : 'Time Frame:'}</h5>
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">{step.timeFrame}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* DeFi Ecosystem Section */}
            <section id="defi-ecosystem" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>layers</span>
                {currentContent.sections.defiEcosystem.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.defiEcosystem.content}</p>
              <div className="space-y-4">
                {currentContent.sections.defiEcosystem.layers.map((layer, index) => (
                  <div key={index} className="bg-indigo-50 p-6 rounded-xl">
                    <h4 className="font-bold text-indigo-900 mb-3">🏗️ {layer.layer}</h4>
                    <p className="text-indigo-800 mb-4 text-sm">{layer.description}</p>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-semibold text-blue-700 mb-2 text-sm">{locale === 'ar' ? 'المكونات:' : 'Components:'}</h5>
                        <div className="flex flex-wrap gap-1">
                          {layer.components.map((component, compIndex) => (
                            <span key={compIndex} className="text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded">
                              {component}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h5 className="font-semibold text-green-700 mb-2 text-sm">{locale === 'ar' ? 'الوظيفة:' : 'Function:'}</h5>
                        <p className="text-sm text-green-600">{layer.function}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Future of DeFi Section */}
            <section id="future-of-defi" className="mb-8">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>forward</span>
                {currentContent.sections.futureOfDefi.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.futureOfDefi.content}</p>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.futureOfDefi.trends.map((trend, index) => (
                  <div key={index} className="bg-cyan-50 p-6 rounded-xl">
                    <h4 className="font-bold text-blue-950 mb-3">🚀 {trend.trend}</h4>
                    <p className="text-cyan-900/70 text-sm mb-3">{trend.description}</p>
                    <div className="text-xs text-cyan-700 bg-cyan-100 px-3 py-2 rounded">
                      <strong>{locale === 'ar' ? 'التأثير: ' : 'Impact: '}</strong>{trend.impact}
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Call to Action */}
            <div className="text-center pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-blue-950 mb-4">{currentContent.sections.cta.title}</h3>
              <p className="text-blue-900/70 mb-6">{currentContent.sections.cta.desc}</p>
              <Link href={`/${locale}/app`} className="btn-primary">
                {currentContent.sections.cta.button}
              </Link>
            </div>
          </motion.article>
        </div>
      </div>

      <Footer />
    </main>
  );
} 