'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';

export default function LearnPage() {
  const { t, isRTL } = useLanguage();
  
  // Learning resources data - now using translations
  const learningResources = [
    {
      title: t('learn.resources.cryptoBasics'),
      description: t('learn.resources.cryptoBasicsDesc'),
      icon: 'school',
      tag: t('learn.tags.beginner'),
    },
    {
      title: t('learn.resources.advancedTrading'),
      description: t('learn.resources.advancedTradingDesc'),
      icon: 'trending_up',
      tag: t('learn.tags.advanced'),
    },
    {
      title: t('learn.resources.securityPractices'),
      description: t('learn.resources.securityPracticesDesc'),
      icon: 'security',
      tag: t('learn.tags.essential'),
    },
    {
      title: t('learn.resources.defiExplained'),
      description: t('learn.resources.defiExplainedDesc'),
      icon: 'account_balance',
      tag: t('learn.tags.intermediate'),
    },
    {
      title: t('learn.resources.nftOwnership'),
      description: t('learn.resources.nftOwnershipDesc'),
      icon: 'palette',
      tag: t('learn.tags.trending'),
    },
    {
      title: t('learn.resources.web3Development'),
      description: t('learn.resources.web3DevelopmentDesc'),
      icon: 'code',
      tag: t('learn.tags.technical'),
    },
  ];
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('learn.title')}
          subtitle={t('learn.subtitle')}
          icon="school"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            {learningResources.map((resource, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all hover:-translate-y-1"
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="material-symbols-outlined text-2xl text-primary">
                      {resource.icon}
                    </span>
                  </div>
                  <span className="text-xs font-medium px-3 py-1 rounded-full bg-accent/10 text-accent">
                    {resource.tag}
                  </span>
                </div>
                <h3 className={`text-xl font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
                  {resource.title}
                </h3>
                <p className={`text-blue-900/70 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
                  {resource.description}
                </p>
                <Link 
                  href="#" 
                  className={`text-primary font-medium hover:text-primary/80 transition flex items-center ${isRTL ? 'font-tajawal' : ''}`}
                >
                  {t('learn.learnMore')}
                  <span className={`material-symbols-outlined text-sm ${isRTL ? 'mr-1 scale-x-[-1]' : 'ml-1'}`}>
                    arrow_forward
                  </span>
                </Link>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="mt-16 text-center"
          >
            <h2 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('learn.readyToDive')}
            </h2>
            <p className={`text-blue-900/70 mb-6 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
              {t('learn.docsDescription')}
            </p>
            <Link href="/docs" className="btn-primary">
              {t('learn.exploreDocs')}
            </Link>
          </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
