'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function AdvancedTradingPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Content for both languages
  const content = {
    en: {
      title: "Advanced Trading Strategies",
      subtitle: "Master sophisticated trading techniques and risk management in cryptocurrency markets",
      backToLearn: "Back to Learn",
      tableOfContents: "📋 Table of Contents",
      sections: {
        technicalAnalysis: {
          title: "Technical Analysis Mastery",
          content: [
            "Technical analysis is the cornerstone of advanced trading. It involves studying price charts, patterns, and indicators to predict future price movements.",
            "Advanced traders use multiple timeframes, combine various indicators, and understand market psychology to make informed decisions.",
            "💡 Pro Tip: Never rely on a single indicator. Always use confluence - multiple signals pointing in the same direction."
          ],
          tools: [
            { title: "📈 Candlestick Patterns", desc: "Doji, Hammer, Shooting Star, and complex patterns like Head & Shoulders" },
            { title: "📊 Technical Indicators", desc: "RSI, MACD, Bollinger Bands, Fibonacci retracements, and custom indicators" },
            { title: "📉 Volume Analysis", desc: "Volume profile, OBV (On-Balance Volume), and volume-weighted indicators" },
            { title: "🎯 Support & Resistance", desc: "Dynamic levels, psychological levels, and institutional order blocks" }
          ]
        },
        tradingStrategies: {
          title: "Advanced Trading Strategies",
          strategies: [
            {
              name: "Swing Trading",
              desc: "Hold positions for days to weeks, capturing medium-term price movements",
              risk: "Medium",
              timeframe: "4H - Daily"
            },
            {
              name: "Scalping",
              desc: "Quick trades lasting minutes to hours, profiting from small price movements",
              risk: "High",
              timeframe: "1m - 15m"
            },
            {
              name: "Position Trading",
              desc: "Long-term strategy holding for weeks to months based on fundamental analysis",
              risk: "Low-Medium",
              timeframe: "Weekly - Monthly"
            },
            {
              name: "Arbitrage",
              desc: "Exploiting price differences across different exchanges or trading pairs",
              risk: "Low",
              timeframe: "Seconds - Minutes"
            }
          ]
        },
        riskManagement: {
          title: "Risk Management & Psychology",
          principles: [
            {
              icon: "🛡️",
              title: "Position Sizing",
              desc: "Never risk more than 1-2% of your portfolio on a single trade. Use the Kelly Criterion for optimal sizing."
            },
            {
              icon: "🎯",
              title: "Stop Losses",
              desc: "Always set stop losses before entering trades. Use technical levels, not arbitrary percentages."
            },
            {
              icon: "💰",
              title: "Risk-Reward Ratio",
              desc: "Aim for at least 1:2 risk-reward ratio. If you risk $100, target $200+ profit."
            },
            {
              icon: "🧠",
              title: "Trading Psychology",
              desc: "Control emotions like fear and greed. Keep a trading journal to track performance and emotions."
            },
            {
              icon: "📊",
              title: "Portfolio Diversification",
              desc: "Don't put all eggs in one basket. Spread risk across different assets and strategies."
            }
          ]
        },
        derivativesDefi: {
          title: "Derivatives & DeFi Trading",
          content: "Advanced traders leverage derivatives and DeFi protocols for enhanced strategies and yield generation.",
          instruments: [
            {
              name: "Futures Contracts",
              desc: "Trade with leverage up to 100x. High risk but potential for amplified returns.",
              use: "Hedging, speculation, arbitrage"
            },
            {
              name: "Options Trading",
              desc: "Buy the right to purchase/sell at specific prices. Complex but powerful for risk management.",
              use: "Income generation, hedging, speculation"
            },
            {
              name: "Perpetual Swaps",
              desc: "Futures without expiry dates. Most popular derivative in crypto trading.",
              use: "Leveraged trading, short selling"
            },
            {
              name: "DeFi Yield Farming",
              desc: "Provide liquidity to earn fees and tokens. Research smart contract risks.",
              use: "Passive income, portfolio growth"
            }
          ]
        },
        marketAnalysis: {
          title: "Market Analysis & News Trading",
          content: "Successful traders combine technical analysis with fundamental analysis and market sentiment.",
          factors: [
            "📰 News Events: Regulations, partnerships, major announcements",
            "💹 Market Sentiment: Fear & Greed Index, social media trends",
            "🏛️ Macroeconomic Factors: Interest rates, inflation, global events",
            "🐋 Whale Movements: Large transactions and institutional activity",
            "📈 On-chain Metrics: Network activity, transaction volumes, developer activity"
          ]
        },
        tradingTools: {
          title: "Professional Trading Tools",
          categories: [
            {
              category: "Charting Platforms",
              tools: ["TradingView", "Coinigy", "3Commas", "Cryptowatch"]
            },
            {
              category: "Trading Bots",
              tools: ["Grid Trading", "DCA Bots", "Arbitrage Bots", "Custom Algorithms"]
            },
            {
              category: "Portfolio Management",
              tools: ["CoinTracker", "Blockfolio", "Delta", "Custom Spreadsheets"]
            },
            {
              category: "Research Tools",
              tools: ["Glassnode", "IntoTheBlock", "Santiment", "CryptoQuant"]
            }
          ]
        },
        warnings: {
          title: "Advanced Trading Warnings",
          warning: "⚠️ Critical Risk Warnings",
          risks: [
            "Leverage can amplify losses as much as gains - never trade with money you can't afford to lose",
            "Advanced strategies require significant time, knowledge, and emotional control",
            "Market manipulation and whale movements can invalidate technical analysis",
            "Always start with small amounts and gradually increase as you gain experience",
            "Consider tax implications of frequent trading in your jurisdiction",
            "Beware of pump and dump schemes, especially in low-cap altcoins"
          ]
        },
        cta: {
          title: "Ready for Advanced Trading?",
          desc: "Start practicing with small amounts and use Mokhba Wallet's advanced features to enhance your trading journey.",
          button: "Start Advanced Trading"
        }
      }
    },
    ar: {
      title: "استراتيجيات التداول المتقدمة",
      subtitle: "إتقان التقنيات المتطورة وإدارة المخاطر في أسواق العملات المشفرة",
      backToLearn: "العودة إلى التعلم",
      tableOfContents: "📋 فهرس المحتويات",
      sections: {
        technicalAnalysis: {
          title: "إتقان التحليل الفني",
          content: [
            "التحليل الفني هو حجر الزاوية في التداول المتقدم. يتضمن دراسة الرسوم البيانية للأسعار والأنماط والمؤشرات للتنبؤ بحركات الأسعار المستقبلية.",
            "يستخدم المتداولون المتقدمون إطارات زمنية متعددة، ويجمعون بين مؤشرات مختلفة، ويفهمون علم نفس السوق لاتخاذ قرارات مدروسة.",
            "💡 نصيحة محترف: لا تعتمد أبداً على مؤشر واحد. استخدم دائماً التقارب - إشارات متعددة تشير في نفس الاتجاه."
          ],
          tools: [
            { title: "📈 أنماط الشموع", desc: "دوجي، المطرقة، النجم الساقط، والأنماط المعقدة مثل الرأس والكتفين" },
            { title: "📊 المؤشرات الفنية", desc: "RSI، MACD، بولينجر باندز، تصحيحات فيبوناتشي، والمؤشرات المخصصة" },
            { title: "📉 تحليل الحجم", desc: "ملف تعريف الحجم، OBV، والمؤشرات المرجحة بالحجم" },
            { title: "🎯 الدعم والمقاومة", desc: "المستويات الديناميكية، المستويات النفسية، وكتل الطلبات المؤسسية" }
          ]
        },
        tradingStrategies: {
          title: "استراتيجيات التداول المتقدمة",
          strategies: [
            {
              name: "التداول المتأرجح",
              desc: "الاحتفاظ بالصفقات لأيام إلى أسابيع، والاستفادة من حركات الأسعار متوسطة المدى",
              risk: "متوسط",
              timeframe: "4 ساعات - يومي"
            },
            {
              name: "المضاربة السريعة",
              desc: "صفقات سريعة تدوم دقائق إلى ساعات، والربح من حركات الأسعار الصغيرة",
              risk: "عالي",
              timeframe: "دقيقة - 15 دقيقة"
            },
            {
              name: "التداول طويل المدى",
              desc: "استراتيجية طويلة المدى للاحتفاظ لأسابيع إلى شهور بناءً على التحليل الأساسي",
              risk: "منخفض-متوسط",
              timeframe: "أسبوعي - شهري"
            },
            {
              name: "المراجحة",
              desc: "استغلال الفروق السعرية عبر البورصات المختلفة أو أزواج التداول",
              risk: "منخفض",
              timeframe: "ثوانٍ - دقائق"
            }
          ]
        },
        riskManagement: {
          title: "إدارة المخاطر وعلم النفس",
          principles: [
            {
              icon: "🛡️",
              title: "حجم الصفقة",
              desc: "لا تخاطر أبداً بأكثر من 1-2% من محفظتك في صفقة واحدة. استخدم معيار كيلي للحجم الأمثل."
            },
            {
              icon: "🎯",
              title: "وقف الخسارة",
              desc: "ضع دائماً وقف الخسارة قبل دخول الصفقات. استخدم المستويات الفنية، وليس النسب المئوية التعسفية."
            },
            {
              icon: "💰",
              title: "نسبة المخاطرة إلى العائد",
              desc: "اهدف إلى نسبة مخاطرة إلى عائد 1:2 على الأقل. إذا خاطرت بـ 100 دولار، استهدف ربح 200 دولار أو أكثر."
            },
            {
              icon: "🧠",
              title: "علم نفس التداول",
              desc: "تحكم في المشاعر مثل الخوف والجشع. احتفظ بمذكرة تداول لتتبع الأداء والمشاعر."
            },
            {
              icon: "📊",
              title: "تنويع المحفظة",
              desc: "لا تضع كل البيض في سلة واحدة. وزع المخاطر عبر أصول واستراتيجيات مختلفة."
            }
          ]
        },
        derivativesDefi: {
          title: "المشتقات وتداول DeFi",
          content: "يستفيد المتداولون المتقدمون من المشتقات وبروتوكولات DeFi للاستراتيجيات المحسنة وتوليد العوائد.",
          instruments: [
            {
              name: "عقود المستقبليات",
              desc: "التداول بالرافعة المالية حتى 100x. مخاطرة عالية ولكن إمكانية عوائد مضخمة.",
              use: "التحوط، المضاربة، المراجحة"
            },
            {
              name: "تداول الخيارات",
              desc: "شراء الحق في الشراء/البيع بأسعار محددة. معقد ولكن قوي لإدارة المخاطر.",
              use: "توليد الدخل، التحوط، المضاربة"
            },
            {
              name: "المقايضات الدائمة",
              desc: "المستقبليات بدون تواريخ انتهاء. أشهر مشتق في تداول العملات المشفرة.",
              use: "التداول بالرافعة المالية، البيع على المكشوف"
            },
            {
              name: "زراعة العوائد DeFi",
              desc: "توفير السيولة لكسب الرسوم والرموز. ابحث في مخاطر العقود الذكية.",
              use: "الدخل السلبي، نمو المحفظة"
            }
          ]
        },
        marketAnalysis: {
          title: "تحليل السوق وتداول الأخبار",
          content: "يجمع المتداولون الناجحون بين التحليل الفني والتحليل الأساسي ومعنويات السوق.",
          factors: [
            "📰 الأحداث الإخبارية: اللوائح، الشراكات، الإعلانات الكبرى",
            "💹 معنويات السوق: مؤشر الخوف والجشع، اتجاهات وسائل التواصل الاجتماعي",
            "🏛️ العوامل الاقتصادية الكلية: أسعار الفائدة، التضخم، الأحداث العالمية",
            "🐋 حركات الحيتان: المعاملات الكبيرة والنشاط المؤسسي",
            "📈 مقاييس على السلسلة: نشاط الشبكة، أحجام المعاملات، نشاط المطورين"
          ]
        },
        tradingTools: {
          title: "أدوات التداول المهنية",
          categories: [
            {
              category: "منصات الرسوم البيانية",
              tools: ["TradingView", "Coinigy", "3Commas", "Cryptowatch"]
            },
            {
              category: "روبوتات التداول",
              tools: ["التداول الشبكي", "روبوتات DCA", "روبوتات المراجحة", "خوارزميات مخصصة"]
            },
            {
              category: "إدارة المحفظة",
              tools: ["CoinTracker", "Blockfolio", "Delta", "جداول بيانات مخصصة"]
            },
            {
              category: "أدوات البحث",
              tools: ["Glassnode", "IntoTheBlock", "Santiment", "CryptoQuant"]
            }
          ]
        },
        warnings: {
          title: "تحذيرات التداول المتقدم",
          warning: "⚠️ تحذيرات مخاطر حرجة",
          risks: [
            "الرافعة المالية يمكن أن تضخم الخسائر بقدر ما تضخم المكاسب - لا تتداول أبداً بأموال لا يمكنك تحمل خسارتها",
            "الاستراتيجيات المتقدمة تتطلب وقتاً ومعرفة وتحكماً عاطفياً كبيراً",
            "التلاعب بالسوق وحركات الحيتان يمكن أن تبطل التحليل الفني",
            "ابدأ دائماً بمبالغ صغيرة وزد تدريجياً مع اكتساب الخبرة",
            "فكر في الآثار الضريبية للتداول المتكرر في ولايتك القضائية",
            "احذر من مخططات الضخ والتفريغ، خاصة في العملات البديلة منخفضة السقف"
          ]
        },
        cta: {
          title: "هل أنت مستعد للتداول المتقدم؟",
          desc: "ابدأ بالممارسة بمبالغ صغيرة واستخدم ميزات محفظة مخبة المتقدمة لتعزيز رحلة التداول الخاصة بك.",
          button: "ابدأ التداول المتقدم"
        }
      }
    }
  };

  const currentContent = content[locale as 'en' | 'ar'] || content.en;

  return (
    <main className={`relative bg-gradient-to-b from-white to-[#73AED2] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content */}
      <div className="pt-24 pb-16 min-h-screen">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <Link 
              href={`/${locale}/learn`}
              className="text-primary hover:text-primary/80 flex items-center mb-4"
            >
              <span className={`material-symbols-outlined ${isRTL ? 'ml-2 scale-x-[-1]' : 'mr-2'}`}>arrow_back</span>
              {currentContent.backToLearn}
            </Link>
          </nav>

          {/* Article Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-6">
              <span className="material-symbols-outlined text-3xl text-primary">trending_up</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-blue-950 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-blue-900/70 max-w-2xl mx-auto">
              {currentContent.subtitle}
            </p>
            <div className="flex items-center justify-center gap-4 mt-4 text-sm text-blue-900/60">
              <span>🎯 {locale === 'ar' ? 'متقدم' : 'Advanced'}</span>
              <span>•</span>
              <span>⏱️ {locale === 'ar' ? '15 دقيقة قراءة' : '15 min read'}</span>
            </div>
          </motion.div>

          {/* Article Content */}
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            {/* Table of Contents */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-bold text-blue-950 mb-4">{currentContent.tableOfContents}</h3>
              <ul className="space-y-2 text-blue-900">
                <li><a href="#technical-analysis" className="hover:text-primary transition">1. {currentContent.sections.technicalAnalysis.title}</a></li>
                <li><a href="#trading-strategies" className="hover:text-primary transition">2. {currentContent.sections.tradingStrategies.title}</a></li>
                <li><a href="#risk-management" className="hover:text-primary transition">3. {currentContent.sections.riskManagement.title}</a></li>
                <li><a href="#derivatives-defi" className="hover:text-primary transition">4. {currentContent.sections.derivativesDefi.title}</a></li>
                <li><a href="#market-analysis" className="hover:text-primary transition">5. {currentContent.sections.marketAnalysis.title}</a></li>
                <li><a href="#trading-tools" className="hover:text-primary transition">6. {currentContent.sections.tradingTools.title}</a></li>
                <li><a href="#warnings" className="hover:text-primary transition">7. {currentContent.sections.warnings.title}</a></li>
              </ul>
            </div>

            {/* Technical Analysis Section */}
            <section id="technical-analysis" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>analytics</span>
                {currentContent.sections.technicalAnalysis.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.technicalAnalysis.content[0]}</p>
                <p>{currentContent.sections.technicalAnalysis.content[1]}</p>
                <div className={`bg-green-50 border-green-400 p-4 rounded ${isRTL ? 'border-r-4' : 'border-l-4'}`}>
                  <p className="font-medium text-green-800">
                    {currentContent.sections.technicalAnalysis.content[2]}
                  </p>
                </div>
                <div className="grid md:grid-cols-2 gap-6 my-6">
                  {currentContent.sections.technicalAnalysis.tools.map((tool, index) => (
                    <div key={index} className="bg-blue-50 p-6 rounded-xl">
                      <h4 className="font-bold text-blue-950 mb-3">{tool.title}</h4>
                      <p className="text-sm">{tool.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Trading Strategies Section */}
            <section id="trading-strategies" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>strategy</span>
                {currentContent.sections.tradingStrategies.title}
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.tradingStrategies.strategies.map((strategy, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-xl">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-blue-950">{strategy.name}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        strategy.risk === 'High' || strategy.risk === 'عالي' ? 'bg-red-100 text-red-700' :
                        strategy.risk === 'Medium' || strategy.risk === 'متوسط' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-green-100 text-green-700'
                      }`}>
                        {strategy.risk}
                      </span>
                    </div>
                    <p className="text-blue-900/70 text-sm mb-3">{strategy.desc}</p>
                    <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                      {strategy.timeframe}
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Risk Management Section */}
            <section id="risk-management" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>shield</span>
                {currentContent.sections.riskManagement.title}
              </h2>
              <div className="space-y-6">
                {currentContent.sections.riskManagement.principles.map((principle, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
                    <span className="text-2xl">{principle.icon}</span>
                    <div>
                      <h4 className="font-bold text-blue-950 mb-2">{principle.title}</h4>
                      <p className="text-blue-900/70">{principle.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Derivatives & DeFi Section */}
            <section id="derivatives-defi" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>auto_graph</span>
                {currentContent.sections.derivativesDefi.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.derivativesDefi.content}</p>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.derivativesDefi.instruments.map((instrument, index) => (
                  <div key={index} className="bg-purple-50 p-6 rounded-xl">
                    <h4 className="font-bold text-blue-950 mb-3">{instrument.name}</h4>
                    <p className="text-purple-900/70 text-sm mb-3">{instrument.desc}</p>
                    <div className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">
                      {locale === 'ar' ? 'الاستخدام: ' : 'Use: '}{instrument.use}
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Market Analysis Section */}
            <section id="market-analysis" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>monitoring</span>
                {currentContent.sections.marketAnalysis.title}
              </h2>
              <p className="text-blue-900/80 mb-6">{currentContent.sections.marketAnalysis.content}</p>
              <ul className="space-y-3">
                {currentContent.sections.marketAnalysis.factors.map((factor, index) => (
                  <li key={index} className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                    <span className="text-sm text-orange-900/80">{factor}</span>
                  </li>
                ))}
              </ul>
            </section>

            {/* Trading Tools Section */}
            <section id="trading-tools" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>build</span>
                {currentContent.sections.tradingTools.title}
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.tradingTools.categories.map((category, index) => (
                  <div key={index} className="bg-green-50 p-6 rounded-xl">
                    <h4 className="font-bold text-blue-950 mb-3">{category.category}</h4>
                    <ul className="space-y-2">
                      {category.tools.map((tool, toolIndex) => (
                        <li key={toolIndex} className="text-green-900/70 text-sm">• {tool}</li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </section>

            {/* Warnings Section */}
            <section id="warnings" className="mb-8">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>warning</span>
                {currentContent.sections.warnings.title}
              </h2>
              <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                <h4 className="font-bold text-red-800 mb-4">{currentContent.sections.warnings.warning}</h4>
                <ul className="space-y-3 text-red-700">
                  {currentContent.sections.warnings.risks.map((risk, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>{risk}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </section>

            {/* Call to Action */}
            <div className="text-center pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-blue-950 mb-4">{currentContent.sections.cta.title}</h3>
              <p className="text-blue-900/70 mb-6">{currentContent.sections.cta.desc}</p>
              <Link href={`/${locale}/app`} className="btn-primary">
                {currentContent.sections.cta.button}
              </Link>
            </div>
          </motion.article>
        </div>
      </div>

      <Footer />
    </main>
  );
} 