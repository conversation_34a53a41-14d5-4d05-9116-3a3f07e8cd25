'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function CryptoBasicsPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Arabic content
  const content = {
    en: {
      title: "Cryptocurrency Basics",
      subtitle: "Everything you need to know to get started in the world of digital currencies",
      backToLearn: "Back to Learn",
      tableOfContents: "📋 Table of Contents",
      sections: {
        whatIsCrypto: {
          title: "What is Cryptocurrency?",
          content: [
            "Cryptocurrency is a digital or virtual form of money that uses cryptography for security. Unlike traditional currencies issued by governments (like USD or EUR), cryptocurrencies operate on decentralized networks based on blockchain technology.",
            "Think of it as digital cash that exists only online, but with some unique properties that make it different from digital bank transfers or online payments.",
            "💡 Simple Analogy: If traditional money is like sending a physical letter through the postal service, cryptocurrency is like sending an encrypted email directly to someone without needing a postal service."
          ]
        },
        howItWorks: {
          title: "How Does it Work?",
          content: "Cryptocurrencies work on a technology called blockchain - a distributed ledger that records all transactions across a network of computers.",
          concepts: [
            { title: "🔗 Blockchain", desc: "A chain of blocks containing transaction data, secured by cryptography and distributed across multiple computers." },
            { title: "🔐 Cryptography", desc: "Mathematical algorithms that secure transactions and control the creation of new units." },
            { title: "🌐 Decentralization", desc: "No single authority controls the network - it's maintained by participants worldwide." },
            { title: "⛏️ Mining/Validation", desc: "Process of verifying transactions and adding them to the blockchain." }
          ]
        },
        keyFeatures: {
          title: "Key Features",
          features: [
            { icon: "🌍", title: "Global & Borderless", desc: "Send money anywhere in the world, 24/7, without bank restrictions or high international fees." },
            { icon: "🔒", title: "Secure & Transparent", desc: "All transactions are recorded on a public ledger that's virtually impossible to hack or manipulate." },
            { icon: "⚡", title: "Fast & Efficient", desc: "Transactions can be completed in minutes rather than days, especially for international transfers." },
            { icon: "💰", title: "Lower Fees", desc: "Often cheaper than traditional banking, especially for large amounts or international transfers." },
            { icon: "🏦", title: "No Central Authority", desc: "Not controlled by any government or bank - you have full control over your money." }
          ]
        },
        popularCryptos: {
          title: "Popular Cryptocurrencies",
          cryptos: [
            { name: "Bitcoin (BTC)", desc: "The first and most well-known cryptocurrency, often called 'digital gold'.", icon: "₿" },
            { name: "Ethereum (ETH)", desc: "Platform for smart contracts and decentralized applications (DApps).", icon: "Ξ" },
            { name: "Tether (USDT)", desc: "A stablecoin pegged to the US Dollar, maintaining stable value.", icon: "₮" },
            { name: "BNB", desc: "Binance's native token used on the world's largest crypto exchange.", icon: "🔶" }
          ]
        },
        gettingStarted: {
          title: "Getting Started",
          steps: [
            { title: "Choose a Wallet", desc: "Get a digital wallet to store your cryptocurrencies. Mokhba Wallet is a great choice for beginners!" },
            { title: "Buy Your First Crypto", desc: "Start small with a reputable exchange. Bitcoin or Ethereum are good first choices." },
            { title: "Secure Your Investment", desc: "Write down your recovery phrase and keep it safe. Never share your private keys!" },
            { title: "Keep Learning", desc: "The crypto space evolves rapidly. Stay informed and only invest what you can afford to lose." }
          ]
        },
        safetyTips: {
          title: "Safety Tips",
          warning: "🚨 Important Security Reminders",
          tips: [
            "Never share your private keys or recovery phrase with anyone",
            "Only use reputable exchanges and wallets",
            "Be wary of \"get rich quick\" schemes and scams",
            "Start with small amounts while learning",
            "Keep your software updated",
            "Use two-factor authentication (2FA) whenever possible"
          ]
        },
        cta: {
          title: "Ready to Start Your Crypto Journey?",
          desc: "Join thousands of users who trust Mokhba Wallet for their cryptocurrency needs.",
          button: "Open Mokhba Wallet"
        }
      }
    },
    ar: {
      title: "أساسيات العملات المشفرة",
      subtitle: "كل ما تحتاج لمعرفته للبدء في عالم العملات الرقمية",
      backToLearn: "العودة إلى التعلم",
      tableOfContents: "📋 فهرس المحتويات",
      sections: {
        whatIsCrypto: {
          title: "ما هي العملة المشفرة؟",
          content: [
            "العملة المشفرة هي شكل رقمي أو افتراضي من المال يستخدم التشفير للأمان. على عكس العملات التقليدية الصادرة عن الحكومات (مثل الدولار الأمريكي أو اليورو)، تعمل العملات المشفرة على شبكات لامركزية تعتمد على تقنية البلوك تشين.",
            "فكر فيها كنقد رقمي موجود فقط عبر الإنترنت، ولكن بخصائص فريدة تجعلها مختلفة عن التحويلات المصرفية الرقمية أو المدفوعات عبر الإنترنت.",
            "💡 تشبيه بسيط: إذا كانت الأموال التقليدية مثل إرسال رسالة مادية عبر البريد، فإن العملة المشفرة مثل إرسال بريد إلكتروني مشفر مباشرة إلى شخص ما دون الحاجة إلى خدمة بريدية."
          ]
        },
        howItWorks: {
          title: "كيف تعمل؟",
          content: "تعمل العملات المشفرة على تقنية تسمى البلوك تشين - دفتر حسابات موزع يسجل جميع المعاملات عبر شبكة من أجهزة الكمبيوتر.",
          concepts: [
            { title: "🔗 البلوك تشين", desc: "سلسلة من الكتل التي تحتوي على بيانات المعاملات، محمية بالتشفير وموزعة عبر أجهزة كمبيوتر متعددة." },
            { title: "🔐 التشفير", desc: "خوارزميات رياضية تؤمن المعاملات وتتحكم في إنشاء وحدات جديدة." },
            { title: "🌐 اللامركزية", desc: "لا توجد سلطة واحدة تتحكم في الشبكة - يتم صيانتها من قبل المشاركين في جميع أنحاء العالم." },
            { title: "⛏️ التعدين/التحقق", desc: "عملية التحقق من المعاملات وإضافتها إلى البلوك تشين." }
          ]
        },
        keyFeatures: {
          title: "الميزات الرئيسية",
          features: [
            { icon: "🌍", title: "عالمية وبلا حدود", desc: "أرسل الأموال إلى أي مكان في العالم، 24/7، بدون قيود بنكية أو رسوم دولية عالية." },
            { icon: "🔒", title: "آمنة وشفافة", desc: "يتم تسجيل جميع المعاملات في دفتر حسابات عام يكاد يكون من المستحيل اختراقه أو التلاعب به." },
            { icon: "⚡", title: "سريعة وفعالة", desc: "يمكن إتمام المعاملات في دقائق بدلاً من أيام، خاصة للتحويلات الدولية." },
            { icon: "💰", title: "رسوم أقل", desc: "غالباً ما تكون أرخص من البنوك التقليدية، خاصة للمبالغ الكبيرة أو التحويلات الدولية." },
            { icon: "🏦", title: "لا سلطة مركزية", desc: "غير مسيطر عليها من قبل أي حكومة أو بنك - لديك السيطرة الكاملة على أموالك." }
          ]
        },
        popularCryptos: {
          title: "العملات المشفرة الشائعة",
          cryptos: [
            { name: "البيتكوين (BTC)", desc: "أول وأشهر عملة مشفرة، غالباً ما تسمى 'الذهب الرقمي'.", icon: "₿" },
            { name: "الإيثيريوم (ETH)", desc: "منصة للعقود الذكية والتطبيقات اللامركزية (DApps).", icon: "Ξ" },
            { name: "التيثر (USDT)", desc: "عملة مستقرة مربوطة بالدولار الأمريكي، تحافظ على قيمة ثابتة.", icon: "₮" },
            { name: "BNB", desc: "رمز بايننس الأصلي المستخدم في أكبر بورصة عملات مشفرة في العالم.", icon: "🔶" }
          ]
        },
        gettingStarted: {
          title: "البدء",
          steps: [
            { title: "اختر محفظة", desc: "احصل على محفظة رقمية لتخزين عملاتك المشفرة. محفظة مخبة هي خيار رائع للمبتدئين!" },
            { title: "اشتر أول عملة مشفرة", desc: "ابدأ بمبلغ صغير مع بورصة موثوقة. البيتكوين أو الإيثيريوم خيارات جيدة للبداية." },
            { title: "أمن استثمارك", desc: "اكتب عبارة الاسترداد واحتفظ بها آمنة. لا تشارك مفاتيحك الخاصة أبداً!" },
            { title: "استمر في التعلم", desc: "مجال العملات المشفرة يتطور بسرعة. ابق على اطلاع واستثمر فقط ما يمكنك تحمل خسارته." }
          ]
        },
        safetyTips: {
          title: "نصائح الأمان",
          warning: "🚨 تذكيرات أمنية مهمة",
          tips: [
            "لا تشارك مفاتيحك الخاصة أو عبارة الاسترداد مع أي شخص",
            "استخدم فقط البورصات والمحافظ الموثوقة",
            "احذر من مخططات 'الثراء السريع' والاحتيال",
            "ابدأ بمبالغ صغيرة أثناء التعلم",
            "حدث برامجك باستمرار",
            "استخدم المصادقة الثنائية (2FA) كلما أمكن ذلك"
          ]
        },
        cta: {
          title: "هل أنت مستعد لبدء رحلتك في العملات المشفرة؟",
          desc: "انضم إلى آلاف المستخدمين الذين يثقون في محفظة مخبة لاحتياجاتهم من العملات المشفرة.",
          button: "افتح محفظة مخبة"
        }
      }
    }
  };

  const currentContent = content[locale as 'en' | 'ar'] || content.en;

  return (
    <main className={`relative bg-gradient-to-b from-white to-[#73AED2] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content */}
      <div className="pt-24 pb-16 min-h-screen">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <Link 
              href={`/${locale}/learn`}
              className="text-primary hover:text-primary/80 flex items-center mb-4"
            >
              <span className={`material-symbols-outlined ${isRTL ? 'ml-2 scale-x-[-1]' : 'mr-2'}`}>arrow_back</span>
              {currentContent.backToLearn}
            </Link>
          </nav>

          {/* Article Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-6">
              <span className="material-symbols-outlined text-3xl text-primary">school</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-blue-950 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-blue-900/70 max-w-2xl mx-auto">
              {currentContent.subtitle}
            </p>
            <div className="flex items-center justify-center gap-4 mt-4 text-sm text-blue-900/60">
              <span>📚 {locale === 'ar' ? 'دليل المبتدئين' : 'Beginner Guide'}</span>
              <span>•</span>
              <span>⏱️ {locale === 'ar' ? '10 دقائق قراءة' : '10 min read'}</span>
            </div>
          </motion.div>

          {/* Article Content */}
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            {/* Table of Contents */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-bold text-blue-950 mb-4">{currentContent.tableOfContents}</h3>
              <ul className="space-y-2 text-blue-900">
                <li><a href="#what-is-crypto" className="hover:text-primary transition">1. {currentContent.sections.whatIsCrypto.title}</a></li>
                <li><a href="#how-it-works" className="hover:text-primary transition">2. {currentContent.sections.howItWorks.title}</a></li>
                <li><a href="#key-features" className="hover:text-primary transition">3. {currentContent.sections.keyFeatures.title}</a></li>
                <li><a href="#popular-cryptos" className="hover:text-primary transition">4. {currentContent.sections.popularCryptos.title}</a></li>
                <li><a href="#getting-started" className="hover:text-primary transition">5. {currentContent.sections.gettingStarted.title}</a></li>
                <li><a href="#safety-tips" className="hover:text-primary transition">6. {currentContent.sections.safetyTips.title}</a></li>
              </ul>
            </div>

            {/* Article Sections */}
            <section id="what-is-crypto" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>help</span>
                {currentContent.sections.whatIsCrypto.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.whatIsCrypto.content[0]}</p>
                <p>{currentContent.sections.whatIsCrypto.content[1]}</p>
                <div className={`bg-yellow-50 border-yellow-400 p-4 rounded ${isRTL ? 'border-r-4' : 'border-l-4'}`}>
                  <p className="font-medium text-yellow-800">
                    {currentContent.sections.whatIsCrypto.content[2]}
                  </p>
                </div>
              </div>
            </section>

            <section id="how-it-works" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>settings</span>
                {currentContent.sections.howItWorks.title}
              </h2>
              <div className="prose prose-lg max-w-none text-blue-900/80 space-y-4">
                <p>{currentContent.sections.howItWorks.content}</p>
                <div className="grid md:grid-cols-2 gap-6 my-6">
                  {currentContent.sections.howItWorks.concepts.map((concept, index) => (
                    <div key={index} className="bg-blue-50 p-6 rounded-xl">
                      <h4 className="font-bold text-blue-950 mb-3">{concept.title}</h4>
                      <p className="text-sm">{concept.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            <section id="key-features" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>star</span>
                {currentContent.sections.keyFeatures.title}
              </h2>
              <div className="space-y-6">
                {currentContent.sections.keyFeatures.features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
                    <span className="text-2xl">{feature.icon}</span>
                    <div>
                      <h4 className="font-bold text-blue-950 mb-2">{feature.title}</h4>
                      <p className="text-blue-900/70">{feature.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            <section id="popular-cryptos" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>trending_up</span>
                {currentContent.sections.popularCryptos.title}
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {currentContent.sections.popularCryptos.cryptos.map((crypto, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-xl">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-2xl bg-orange-100 w-10 h-10 rounded-full flex items-center justify-center">
                        {crypto.icon}
                      </span>
                      <h4 className="font-bold text-blue-950">{crypto.name}</h4>
                    </div>
                    <p className="text-blue-900/70 text-sm">{crypto.desc}</p>
                  </div>
                ))}
              </div>
            </section>

            <section id="getting-started" className="mb-12">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>rocket_launch</span>
                {currentContent.sections.gettingStarted.title}
              </h2>
              <div className="space-y-4">
                {currentContent.sections.gettingStarted.steps.map((step, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold">{index + 1}</div>
                    <div>
                      <h4 className="font-bold text-blue-950 mb-2">{step.title}</h4>
                      <p className="text-blue-900/70">{step.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            <section id="safety-tips" className="mb-8">
              <h2 className="text-2xl font-bold text-blue-950 mb-6 flex items-center">
                <span className={`material-symbols-outlined text-primary ${isRTL ? 'ml-3' : 'mr-3'}`}>shield</span>
                {currentContent.sections.safetyTips.title}
              </h2>
              <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                <h4 className="font-bold text-red-800 mb-4">{currentContent.sections.safetyTips.warning}</h4>
                <ul className="space-y-2 text-red-700">
                  {currentContent.sections.safetyTips.tips.map((tip, index) => (
                    <li key={index}>• {tip}</li>
                  ))}
                </ul>
              </div>
            </section>

            {/* Call to Action */}
            <div className="text-center pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-blue-950 mb-4">{currentContent.sections.cta.title}</h3>
              <p className="text-blue-900/70 mb-6">{currentContent.sections.cta.desc}</p>
              <Link href={`/${locale}/app`} className="btn-primary">
                {currentContent.sections.cta.button}
              </Link>
            </div>
          </motion.article>
        </div>
      </div>

      <Footer />
    </main>
  );
} 