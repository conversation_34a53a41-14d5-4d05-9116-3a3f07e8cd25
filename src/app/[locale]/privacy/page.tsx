'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';

// Privacy policy sections
const getPrivacySections = (t: any) => [
  {
    title: t('privacy.dataCollection.title'),
    content: t('privacy.dataCollection.content'),
  },
  {
    title: t('privacy.dataUse.title'),
    content: t('privacy.dataUse.content'),
  },
  {
    title: t('privacy.dataSharing.title'),
    content: t('privacy.dataSharing.content'),
  },
  {
    title: t('privacy.security.title'),
    content: t('privacy.security.content'),
  },
  {
    title: t('privacy.rights.title'),
    content: t('privacy.rights.content'),
  },
  {
    title: t('privacy.cookies.title'),
    content: t('privacy.cookies.content'),
  },
  {
    title: t('privacy.overview.title'),
    content: t('privacy.overview.content'),
  },
  {
    title: t('privacy.nonCustodial.title'),
    content: t('privacy.nonCustodial.content'),
  },
  {
    title: t('privacy.changes.title'),
    content: t('privacy.changes.content'),
  },
  {
    title: t('privacy.contact.title'),
    content: t('privacy.contact.content'),
  },
];

export default function PrivacyPage() {
  const { t, isRTL } = useLanguage();
  const privacySections = getPrivacySections(t);
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('privacy.title')}
          subtitle={t('privacy.subtitle')}
          icon="privacy_tip"
        >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-xl p-8 shadow-md mt-12"
      >
        <p className={`text-blue-900/70 mb-8 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('privacy.lastUpdated')}
        </p>
        
        <div className="space-y-8">
          {privacySections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.05 * index }}
            >
              <h2 className={`text-xl font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
                {index + 1}. {section.title}
              </h2>
              <p className={`text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
                {section.content}
              </p>
            </motion.div>
          ))}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
          className="mt-12 pt-8 border-t border-gray-200"
        >
          <h2 className={`text-xl font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('privacy.yourChoices.title')}
          </h2>
          <p className={`text-blue-900/70 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('privacy.yourChoices.description')}
          </p>
          <ul className={`list-disc pl-5 space-y-2 text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
            <li>{t('privacy.yourChoices.access')}</li>
            <li>{t('privacy.yourChoices.correct')}</li>
            <li>{t('privacy.yourChoices.delete')}</li>
            <li>{t('privacy.yourChoices.optOut')}</li>
            <li>{t('privacy.yourChoices.cookies')}</li>
          </ul>
        </motion.div>
      </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
