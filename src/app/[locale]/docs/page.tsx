'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';

// Documentation categories - now using translations
const getDocCategories = (t: any) => [
  {
    title: t('docs.categories.gettingStarted'),
    icon: 'rocket_launch',
    docs: [
      { title: 'Introduction to Mokhba', path: '/docs/introduction' },
      { title: 'Creating a Wallet', path: '/docs/create-wallet' },
      { title: 'Securing Your Wallet', path: '/docs/security' },
      { title: 'Importing an Existing Wallet', path: '/docs/import-wallet' },
    ],
  },
  {
    title: t('docs.categories.usingMokhba'),
    icon: 'wallet',
    docs: [
      { title: 'Sending Crypto', path: '/docs/sending' },
      { title: 'Receiving Crypto', path: '/docs/receiving' },
      { title: 'Managing Assets', path: '/docs/assets' },
      { title: 'Transaction History', path: '/docs/transactions' },
    ],
  },
  {
    title: t('docs.categories.advancedFeatures'),
    icon: 'settings',
    docs: [
      { title: 'Multi-Chain Support', path: '/docs/multi-chain' },
      { title: 'Bridging Assets', path: '/docs/bridging' },
      { title: 'DApp Browser', path: '/docs/dapp-browser' },
      { title: 'Hardware Wallet Integration', path: '/docs/hardware-wallets' },
    ],
  },
  {
    title: t('docs.categories.troubleshooting'),
    icon: 'build',
    docs: [
      { title: 'Common Issues', path: '/docs/common-issues' },
      { title: 'Transaction Failures', path: '/docs/transaction-failures' },
      { title: 'Recovery Options', path: '/docs/recovery' },
      { title: 'Contacting Support', path: '/docs/support' },
    ],
  },
];

// Popular documentation
const popularDocs = [
  { title: 'Creating a Wallet', path: '/docs/create-wallet', views: 12453 },
  { title: 'Sending Crypto', path: '/docs/sending', views: 9872 },
  { title: 'Securing Your Wallet', path: '/docs/security', views: 8741 },
  { title: 'Recovering Your Wallet', path: '/docs/recovery', views: 7654 },
  { title: 'Bridging Assets', path: '/docs/bridging', views: 6543 },
];

export default function DocsPage() {
  const { t, isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const docCategories = getDocCategories(t);
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('docs.title')}
          subtitle={t('docs.subtitle')}
          icon="menu_book"
        >
      {/* Search Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mt-12 max-w-2xl mx-auto"
      >
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={t('docs.searchPlaceholder')}
            className="w-full px-4 py-3 pl-12 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 shadow-sm"
          />
          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 material-symbols-outlined text-gray-400">
            search
          </span>
        </div>
      </motion.div>

      {/* Documentation Categories */}
      <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
        {docCategories.map((category, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 * index + 0.1 }}
            className="bg-white rounded-xl p-6 shadow-md"
          >
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                <span className="material-symbols-outlined text-xl text-primary">
                  {category.icon}
                </span>
              </div>
              <h2 className={`text-xl font-bold text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
                {category.title}
              </h2>
            </div>
            <ul className="space-y-3 mt-4">
              {category.docs.map((doc, docIndex) => (
                <li key={docIndex}>
                  <Link 
                    href={doc.path} 
                    className="flex items-center text-blue-900/80 hover:text-primary transition"
                  >
                    <span className="material-symbols-outlined text-sm mr-2">
                      article
                    </span>
                    <span className={isRTL ? 'font-tajawal' : ''}>
                      {doc.title}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
            <div className="mt-4 pt-4 border-t border-gray-100">
              <Link 
                href={`/docs/${category.title.toLowerCase().replace(' ', '-')}`}
                className="text-primary text-sm font-medium hover:text-primary/80 transition flex items-center"
              >
{t('docs.viewAllIn')} {category.title}
                <span className="material-symbols-outlined text-sm ml-1">arrow_forward</span>
              </Link>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Popular Documentation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="mt-16 bg-primary/5 rounded-xl p-8 border border-primary/10"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('docs.popularDocumentation')}
        </h2>
        <div className="space-y-4">
          {popularDocs.map((doc, index) => (
            <div 
              key={index}
              className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <Link 
                href={doc.path} 
                className="flex items-center text-blue-950 hover:text-primary transition"
              >
                <span className="material-symbols-outlined text-primary mr-3">
                  description
                </span>
                <span className={`font-medium ${isRTL ? 'font-tajawal' : ''}`}>
                  {doc.title}
                </span>
              </Link>
              <div className="flex items-center text-gray-500 text-sm">
                <span className="material-symbols-outlined text-sm mr-1">
                  visibility
                </span>
                {doc.views.toLocaleString()}
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Developer Resources */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mt-16"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('docs.developerResources')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <span className="material-symbols-outlined text-2xl text-primary">
                code
              </span>
            </div>
            <h3 className={`text-lg font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              API Documentation
            </h3>
            <p className={`text-blue-900/70 mb-4 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
              Comprehensive API reference for integrating with Mokhba wallet.
            </p>
            <Link 
              href="/docs/api" 
              className="text-primary text-sm font-medium hover:text-primary/80 transition flex items-center"
            >
              View API Docs
              <span className="material-symbols-outlined text-sm ml-1">arrow_forward</span>
            </Link>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <span className="material-symbols-outlined text-2xl text-primary">
                integration_instructions
              </span>
            </div>
            <h3 className={`text-lg font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              SDK & Libraries
            </h3>
            <p className={`text-blue-900/70 mb-4 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
              Software development kits and libraries for various programming languages.
            </p>
            <Link 
              href="/docs/sdk" 
              className="text-primary text-sm font-medium hover:text-primary/80 transition flex items-center"
            >
              Explore SDKs
              <span className="material-symbols-outlined text-sm ml-1">arrow_forward</span>
            </Link>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <span className="material-symbols-outlined text-2xl text-primary">
                school
              </span>
            </div>
            <h3 className={`text-lg font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              Tutorials
            </h3>
            <p className={`text-blue-900/70 mb-4 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
              Step-by-step guides for building applications with Mokhba wallet.
            </p>
            <Link 
              href="/docs/tutorials" 
              className="text-primary text-sm font-medium hover:text-primary/80 transition flex items-center"
            >
              View Tutorials
              <span className="material-symbols-outlined text-sm ml-1">arrow_forward</span>
            </Link>
          </div>
        </div>
      </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
