'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';

export default function Dashboard() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();

      if (data.user) {
        setUser(data.user);
      } else {
        router.push('/');
      }

      setLoading(false);
    };

    getUser();
  }, [router]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container-custom py-4 flex justify-between items-center">
          <Link href="/" className="flex items-center">
            <Image
              src="/logo.svg"
              alt="Mokhba Logo"
              width={80}
              height={80}
              className="mr-2"
              unoptimized
            />
          </Link>

          <div className="flex items-center space-x-4">
            <span className="text-gray-700">
              {user?.email}
            </span>
            <button
              onClick={handleSignOut}
              className="btn-primary"
            >
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Dashboard Content */}
      <main className="flex-grow">
        <div className="container-custom py-8">
          <h1 className="heading-2 mb-6">Dashboard</h1>

          <div className="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold text-primary mb-4">Welcome to Mokhba!</h2>
            <p className="text-gray-700 mb-4">
              This is a simple dashboard placeholder. In a real application, you would see your wallet details,
              transaction history, and portfolio overview here.
            </p>
            <p className="text-gray-700">
              You are currently signed in as: <strong>{user?.email}</strong>
            </p>
          </div>

          {/* Placeholder Wallet Section */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-bold text-primary mb-4">Your Wallet</h2>
              <div className="p-4 bg-gray-100 rounded-lg mb-4">
                <p className="text-gray-500 text-sm mb-1">Wallet Address</p>
                <p className="font-mono text-sm">0x1234...5678</p>
              </div>
              <button className="btn-primary w-full">
                Create New Wallet
              </button>
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-bold text-primary mb-4">Portfolio Overview</h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center mr-3">
                      <span className="text-primary font-bold">Ξ</span>
                    </div>
                    <span>Ethereum</span>
                  </div>
                  <span className="font-medium">0.00 ETH</span>
                </div>

                <div className="flex justify-between items-center p-3 bg-gray-100 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center mr-3">
                      <span className="text-primary font-bold">B</span>
                    </div>
                    <span>Bitcoin</span>
                  </div>
                  <span className="font-medium">0.00 BTC</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-primary/10 py-6">
        <div className="container-custom text-center">
          <p className="text-gray-600">
            &copy; {new Date().getFullYear()} Mokhba. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
