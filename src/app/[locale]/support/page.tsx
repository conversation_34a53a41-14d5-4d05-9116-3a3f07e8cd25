'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import SupportTicketForm from '@/components/SupportTicketForm';
import { useLanguage } from '@/context/LanguageContext';

export default function SupportPage() {
  const { t, isRTL } = useLanguage();
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);
  const [showTicketForm, setShowTicketForm] = useState(false);
  
  // Support categories data - now using translations
  const supportCategories = [
    { title: t('support.categories.gettingStarted'), icon: 'rocket_launch' },
    { title: t('support.categories.account'), icon: 'account_circle' },
    { title: t('support.categories.transactions'), icon: 'receipt' },
    { title: t('support.categories.security'), icon: 'security' },
    { title: t('support.categories.troubleshooting'), icon: 'build' },
    { title: t('support.categories.contactUs'), icon: 'support_agent' },
  ];

  // FAQ data - now using translations
  const faqData = [
    {
      question: t('support.faq.createWallet.question'),
      answer: t('support.faq.createWallet.answer'),
    },
    {
      question: t('support.faq.safety.question'),
      answer: t('support.faq.safety.answer'),
    },
    {
      question: t('support.faq.supported.question'),
      answer: t('support.faq.supported.answer'),
    },
    {
      question: t('support.faq.recover.question'),
      answer: t('support.faq.recover.answer'),
    },
  ];
  
  const toggleFaq = (index: number) => {
    if (expandedFaq === index) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(index);
    }
  };
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('support.title')}
          subtitle={t('support.subtitle')}
          icon="support_agent"
        >
          {/* Support Categories */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-12">
            {supportCategories.map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.05 * index }}
                onClick={() => {
                  if (category.title === t('support.categories.contactUs')) {
                    setShowTicketForm(true);
                  }
                }}
                className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all hover:-translate-y-1 text-center cursor-pointer"
              >
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                  <span className="material-symbols-outlined text-2xl text-primary">
                    {category.icon}
                  </span>
                </div>
                <h3 className={`font-medium text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
                  {category.title}
                </h3>
              </motion.div>
            ))}
          </div>

          {/* FAQ Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-16"
          >
            <h2 className={`text-2xl font-bold text-blue-950 mb-8 text-center ${isRTL ? 'font-tajawal' : ''}`}>
              {t('support.faq.title')}
            </h2>
            <div className="max-w-3xl mx-auto space-y-4">
              {faqData.map((faq, index) => (
                <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100">
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                  >
                    <h3 className={`font-medium text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
                      {faq.question}
                    </h3>
                    <span className="material-symbols-outlined text-gray-400">
                      {expandedFaq === index ? 'expand_less' : 'expand_more'}
                    </span>
                  </button>
                  {expandedFaq === index && (
                    <div className="px-6 pb-6">
                      <p className={`text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mt-16 bg-primary/5 rounded-xl p-8 border border-primary/10"
          >
            <h2 className={`text-2xl font-bold text-blue-950 mb-6 text-center ${isRTL ? 'font-tajawal' : ''}`}>
              {t('support.needHelp.title')}
            </h2>
            <p className={`text-blue-900/70 text-center mb-8 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('support.needHelp.description')}
            </p>
            <div className="flex justify-center">
              <button 
                onClick={() => setShowTicketForm(true)}
                className="btn-primary-medium"
              >
                {t('support.contactSupport')}
              </button>
            </div>
          </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>

      {/* Support Ticket Form Modal */}
      {showTicketForm && (
        <SupportTicketForm 
          isRTL={isRTL}
          onClose={() => setShowTicketForm(false)}
        />
      )}
    </main>
  );
}
