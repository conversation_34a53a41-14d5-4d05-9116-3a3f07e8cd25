'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';
import CaptchaWrapper, { useCaptcha } from '@/components/CaptchaWrapper';

export default function FeatureRequestPage() {
  const { t, isRTL } = useLanguage();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [category, setCategory] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const { captchaToken, handleCaptchaChange, isCaptchaValid, resetCaptcha } = useCaptcha();
  
  // Feature categories - updated with emojis and matching database schema
  const featureCategories = [
    { value: 'feature', label: '🚀 ' + t('featureRequest.categories.feature') },
    { value: 'collaboration', label: '🤝 ' + t('featureRequest.categories.collaboration') },
    { value: 'feedback', label: '🧠 ' + t('featureRequest.categories.feedback') },
    { value: 'bug', label: '📢 ' + t('featureRequest.categories.bug') },
    { value: 'investment', label: '💼 ' + t('featureRequest.categories.investment') },
    { value: 'other', label: '📄 ' + t('featureRequest.categories.other') },
  ];

  // Popular feature requests
  const popularRequests = [
    { 
      title: 'Dark Mode Support', 
      category: 'UI', 
      votes: 342,
      status: 'In Progress',
      statusColor: 'bg-yellow-100 text-yellow-800',
    },
    { 
      title: 'Hardware Wallet Integration', 
      category: 'Security', 
      votes: 287,
      status: 'Planned',
      statusColor: 'bg-blue-100 text-blue-800',
    },
    { 
      title: 'Portfolio Analytics', 
      category: 'Wallet Functionality', 
      votes: 256,
      status: 'Under Review',
      statusColor: 'bg-purple-100 text-purple-800',
    },
    { 
      title: 'Solana Network Support', 
      category: 'Network Support', 
      votes: 198,
      status: 'Implemented',
      statusColor: 'bg-green-100 text-green-800',
    },
    { 
      title: 'Transaction Notifications', 
      category: 'Wallet Functionality', 
      votes: 176,
      status: 'Planned',
      statusColor: 'bg-blue-100 text-blue-800',
    },
  ];
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/feature-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          phone: phone || null,
          category,
          title,
          description,
          wallet_address: walletAddress || null,
          captchaToken,
        }),
      });

      if (response.ok) {
        setSubmitted(true);
        
        // Reset form after submission
        setTimeout(() => {
          setName('');
          setEmail('');
          setPhone('');
          setCategory('');
          setTitle('');
          setDescription('');
          setWalletAddress('');
          setSubmitted(false);
        }, 3000);
      } else {
        console.error('Failed to submit feature request');
      }
    } catch (error) {
      console.error('Error submitting feature request:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('featureRequest.title')}
          subtitle={t('featureRequest.subtitle')}
          icon="lightbulb"
        >
      {/* Feature Request Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-xl p-8 shadow-md mt-12"
      >
        {submitted ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-8"
          >
            <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
              <span className="material-symbols-outlined text-3xl text-green-600">
                check_circle
              </span>
            </div>
            <h2 className={`text-2xl font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.thankYou')}
            </h2>
            <p className={`text-blue-900/70 max-w-md mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.submitted')}
            </p>
          </motion.div>
        ) : (
          <form onSubmit={handleSubmit}>
            <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.submitTitle')}
            </h2>
            
            <div className="space-y-6">
              {/* Name Field */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.name')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  placeholder={t('featureRequest.namePlaceholder')}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>

              {/* Email Field */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.email')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder={t('featureRequest.emailPlaceholder')}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>

              {/* Phone Field (Required) */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.phone')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  required
                  placeholder={t('featureRequest.phonePlaceholder')}
                  className={`w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 ${isRTL ? 'text-right font-tajawal' : 'text-left'}`}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Category Field */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.category')} <span className="text-red-500">*</span>
                </label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  required
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                >
                  <option value="">{t('featureRequest.categoryPlaceholder')}</option>
                  {featureCategories.map((cat) => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Title Field */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.featureTitle')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                  placeholder={t('featureRequest.titlePlaceholder')}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>
              
              {/* Description Field */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.description')} <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  required
                  placeholder={t('featureRequest.descriptionPlaceholder')}
                  rows={5}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                ></textarea>
              </div>

              {/* Wallet Address Field (Optional) */}
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('featureRequest.walletAddress')}
                </label>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder={t('featureRequest.walletAddressPlaceholder')}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>
              
              {/* CAPTCHA Protection */}
              <div>
                <CaptchaWrapper 
                  onCaptchaChange={handleCaptchaChange}
                  className="mb-4"
                />
              </div>
              
              <div>
                <button 
                  type="submit" 
                  disabled={loading || !isCaptchaValid()}
                  className={`btn-primary-medium ${(loading || !isCaptchaValid()) ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {loading ? t('featureRequest.submitting') : t('featureRequest.submit')}
                </button>
              </div>
            </div>
          </form>
        )}
      </motion.div>

      {/* Popular Feature Requests */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mt-16"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('featureRequest.popular')}
        </h2>
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Feature
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Votes
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {popularRequests.map((request, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-medium text-gray-900 ${isRTL ? 'font-tajawal' : ''}`}>
                      {request.title}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm text-gray-500 ${isRTL ? 'font-tajawal' : ''}`}>
                      {request.category}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="material-symbols-outlined text-sm text-gray-400 mr-1">
                        thumb_up
                      </span>
                      <span className={`text-sm text-gray-900 ${isRTL ? 'font-tajawal' : ''}`}>
                        {request.votes}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${request.statusColor} ${isRTL ? 'font-tajawal' : ''}`}>
                      {request.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Feature Request Process */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mt-16 bg-primary/5 rounded-xl p-8 border border-primary/10"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-8 text-center ${isRTL ? 'font-tajawal' : ''}`}>
          {t('featureRequest.process.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center mx-auto mb-3 shadow-sm">
              <span className="material-symbols-outlined text-primary">
                assessment
              </span>
            </div>
            <h3 className={`font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step1')}
            </h3>
            <p className={`text-sm text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step1Description')}
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center mx-auto mb-3 shadow-sm">
              <span className="material-symbols-outlined text-primary">
                rate_review
              </span>
            </div>
            <h3 className={`font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step2')}
            </h3>
            <p className={`text-sm text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step2Description')}
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center mx-auto mb-3 shadow-sm">
              <span className="material-symbols-outlined text-primary">
                engineering
              </span>
            </div>
            <h3 className={`font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step3')}
            </h3>
            <p className={`text-sm text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step3Description')}
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center mx-auto mb-3 shadow-sm">
              <span className="material-symbols-outlined text-primary">
                rocket_launch
              </span>
            </div>
            <h3 className={`font-bold text-blue-950 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step4')}
            </h3>
            <p className={`text-sm text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('featureRequest.process.step4Description')}
            </p>
          </div>
        </div>
      </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
