'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import StatusSubscription from '@/components/StatusSubscription';
import { useLanguage } from '@/context/LanguageContext';

// System status data
const systemStatus = [
  { name: 'Wallet App', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Browser Extension', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Mobile App', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'API Services', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Exchange Integration', status: 'Degraded Performance', icon: 'info', color: 'text-yellow-500' },
  { name: 'Customer Support', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
];

// Network status data
const networkStatus = [
  { name: 'Ethereum', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Polygon', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Arbitrum', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Optimism', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
  { name: 'Base', status: 'Operational', icon: 'check_circle', color: 'text-green-500' },
];

// Recent incidents
const recentIncidents = [
  {
    date: 'May 8, 2024',
    title: 'Exchange Integration Degraded Performance',
    status: 'Investigating',
    updates: [
      { time: '14:30 UTC', message: 'We are investigating reports of slow transaction confirmations with certain exchange integrations.' },
      { time: '15:15 UTC', message: 'Identified the issue with our exchange API gateway. Working on a fix.' },
      { time: '16:00 UTC', message: 'Deployed a partial fix. Performance is improving but still degraded. Monitoring the situation.' },
    ],
  },
  {
    date: 'May 2, 2024',
    title: 'Mobile App Connectivity Issues',
    status: 'Resolved',
    updates: [
      { time: '09:45 UTC', message: 'Investigating reports of mobile app connectivity issues on iOS devices.' },
      { time: '10:30 UTC', message: 'Identified an issue with our CDN provider affecting iOS connections.' },
      { time: '11:15 UTC', message: 'Issue resolved. Mobile app connectivity has been restored to normal.' },
    ],
  },
];

export default function StatusPage() {
  const { t, isRTL } = useLanguage();
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('status.title')}
          subtitle={t('status.subtitle')}
          icon="monitoring"
        >
      {/* Overall Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-xl p-8 shadow-md mt-12 text-center"
      >
        <div className="inline-flex items-center justify-center mb-4">
          <span className="material-symbols-outlined text-4xl text-yellow-500 mr-2">
            info
          </span>
          <h2 className={`text-2xl font-bold text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('status.overall.partial')}
          </h2>
        </div>
        <p className={`text-blue-900/70 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
          {t('status.overall.description')}
        </p>
      </motion.div>

      {/* System Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mt-12"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('status.systemStatus')}
        </h2>
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="divide-y divide-gray-200">
            {systemStatus.map((system, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-4 hover:bg-gray-50"
              >
                <span className={`font-medium ${isRTL ? 'font-tajawal' : ''}`}>{system.name}</span>
                <div className="flex items-center">
                  <span className={`${system.color} mr-2`}>{system.status}</span>
                  <span className={`material-symbols-outlined ${system.color}`}>
                    {system.icon}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Network Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mt-12"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('status.networkStatus')}
        </h2>
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="divide-y divide-gray-200">
            {networkStatus.map((network, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-4 hover:bg-gray-50"
              >
                <span className={`font-medium ${isRTL ? 'font-tajawal' : ''}`}>{network.name}</span>
                <div className="flex items-center">
                  <span className={`${network.color} mr-2`}>{network.status}</span>
                  <span className={`material-symbols-outlined ${network.color}`}>
                    {network.icon}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Recent Incidents */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-12"
      >
        <h2 className={`text-2xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('status.recentIncidents')}
        </h2>
        <div className="space-y-6">
          {recentIncidents.map((incident, index) => (
            <div 
              key={index}
              className="bg-white rounded-xl shadow-md overflow-hidden"
            >
              <div className="p-4 bg-gray-50 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h3 className={`font-bold text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
                    {incident.title}
                  </h3>
                  <span className={`text-sm px-3 py-1 rounded-full ${
                    incident.status === 'Resolved' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {incident.status}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">{incident.date}</p>
              </div>
              <div className="p-4">
                <ul className="space-y-3">
                  {incident.updates.map((update, updateIndex) => (
                    <li key={updateIndex} className="flex">
                      <span className="text-sm font-medium text-gray-500 w-24 flex-shrink-0">
                        {update.time}
                      </span>
                      <span className={`text-blue-900/70 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
                        {update.message}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Subscribe to Updates */}
      <StatusSubscription isRTL={isRTL} />
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
