'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';
import Link from 'next/link';

export default function ExplorePage() {
  const { t, isRTL } = useLanguage();
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('explore.title')}
          subtitle={t('explore.subtitle')}
          icon="explore"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-16 text-center"
          >
            <div className="relative inline-block">
              <div className="w-32 h-32 rounded-full bg-primary/20 flex items-center justify-center mx-auto mb-8">
                <span className="material-symbols-outlined text-6xl text-white/80">
                  explore
                </span>
              </div>
            </div>
            <h2 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('explore.comingSoon')}
            </h2>
            <p className={`text-blue-900/70 mb-6 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
              {t('explore.description')}
            </p>
            <Link href={`/${isRTL ? 'ar' : 'en'}/app`} className={`text-primary font-medium hover:text-primary/80 transition flex items-center justify-center ${isRTL ? 'font-tajawal' : ''}`}>
              {t('explore.backToDashboard')}
              <span className={`material-symbols-outlined text-sm ${isRTL ? 'mr-1 scale-x-[-1]' : 'ml-1'}`}>
                arrow_forward
              </span>
            </Link>
          </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
