'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import StructuredData from '@/components/StructuredData';
import { useLanguage } from '@/context/LanguageContext';
import { generateStructuredData } from '@/lib/seo';
import Image from 'next/image';

// Company values
const getCompanyValues = (t: any) => [
  { title: t('about.values.security'), icon: 'security' },
  { title: t('about.values.empowerment'), icon: 'person' },
  { title: t('about.values.transparency'), icon: 'visibility' },
  { title: t('about.values.innovation'), icon: 'lightbulb' },
  { title: t('about.values.inclusivity'), icon: 'diversity_3' },
  { title: t('about.values.community'), icon: 'groups' },
];

export default function AboutPage() {
  const { t, isRTL } = useLanguage();
  const companyValues = getCompanyValues(t);
  
  // Generate structured data for the about page
  const organizationData = generateStructuredData('organization');
  const breadcrumbData = generateStructuredData('breadcrumb', {
    breadcrumbs: [
      { name: 'Home', url: '/' },
      { name: 'About', url: '/about' }
    ]
  });

  // FAQ structured data for common questions
  const faqData = generateStructuredData('faq', {
    faqs: [
      {
        question: 'What is Mokhba?',
        answer: 'Mokhba is the first Arabic cryptocurrency wallet that empowers users to securely store and effortlessly manage their crypto assets with full Arabic language support.'
      },
      {
        question: 'What cryptocurrencies does Mokhba support?',
        answer: 'Mokhba supports Bitcoin, Ethereum, and various other popular cryptocurrencies and tokens.'
      },
      {
        question: 'Is Mokhba secure?',
        answer: 'Yes, Mokhba implements industry-leading security measures including encryption, secure storage, and comprehensive security protocols to protect your digital assets.'
      },
      {
        question: 'Does Mokhba support Arabic language?',
        answer: 'Yes, Mokhba is specifically designed for Arabic-speaking users and provides full Arabic language support with RTL layout.'
      }
    ]
  });
  
  return (
    <>
      {/* Structured Data for About Page */}
      <StructuredData data={organizationData} />
      <StructuredData data={breadcrumbData} />
      <StructuredData data={faqData} />
      
      <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
        {/* Fixed Navbar */}
        <header className="fixed top-0 left-0 right-0 z-50 w-full">
          <Navbar />
        </header>

        {/* Main Content with Scroll Animations */}
        <div className="pt-16"> {/* Add padding to account for fixed navbar */}
          <StaticPageLayout
            title={t('about.title')}
            subtitle={t('about.subtitle')}
            icon="info"
          >
        {/* Our Story */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl p-8 shadow-md mt-12"
        >
          <h2 className={`text-2xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('about.ourStory')}
          </h2>
          <div className={`text-blue-900/70 space-y-4 ${isRTL ? 'font-tajawal' : ''}`}>
            <p>
              {t('about.storyParagraph1')}
            </p>
            <p>
              {t('about.storyParagraph2')}
            </p>
            <p>
              {t('about.storyParagraph3')}
            </p>
            <p>
              {t('about.storyParagraph4')}
            </p>
          </div>
        </motion.div>

        {/* Our Values */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-16"
        >
          <h2 className={`text-2xl font-bold text-blue-950 mb-8 text-center ${isRTL ? 'font-tajawal' : ''}`}>
            {t('about.ourValues')}
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {companyValues.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 * index + 0.2 }}
                className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all hover:-translate-y-1 text-center"
              >
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                  <span className="material-symbols-outlined text-2xl text-primary">
                    {value.icon}
                  </span>
                </div>
                <h3 className={`font-medium text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
                  {value.title}
                </h3>
              </motion.div>
            ))}
          </div>
        </motion.div>

          </StaticPageLayout>
          <Footer />
        </div>
      </main>
    </>
  );
}
