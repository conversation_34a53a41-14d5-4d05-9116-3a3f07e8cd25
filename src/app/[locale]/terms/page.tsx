'use client';

import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticPageLayout from '@/components/StaticPageLayout';
import { useLanguage } from '@/context/LanguageContext';

// Terms sections
const getTermsSections = (t: any) => [
  {
    title: t('terms.acceptance.title'),
    content: t('terms.acceptance.content'),
  },
  {
    title: t('terms.services.title'),
    content: t('terms.services.content'),
  },
  {
    title: t('terms.responsibilities.title'),
    content: t('terms.responsibilities.content'),
  },
  {
    title: t('terms.risks.title'),
    content: t('terms.risks.content'),
  },
  {
    title: t('terms.prohibited.title'),
    content: t('terms.prohibited.content'),
  },
  {
    title: t('terms.intellectual.title'),
    content: t('terms.intellectual.content'),
  },
  {
    title: t('terms.liability.title'),
    content: t('terms.liability.content'),
  },
  {
    title: t('terms.modifications.title'),
    content: t('terms.modifications.content'),
  },
  {
    title: t('terms.governing.title'),
    content: t('terms.governing.content'),
  },
  {
    title: t('terms.contact.title'),
    content: t('terms.contact.content'),
  },
];

export default function TermsPage() {
  const { t, isRTL } = useLanguage();
  const termsSections = getTermsSections(t);
  
  return (
    <main className="relative bg-gradient-to-b from-white to-[#73AED2]">
      {/* Fixed Navbar */}
      <header className="fixed top-0 left-0 right-0 z-50 w-full">
        <Navbar />
      </header>

      {/* Main Content with Scroll Animations */}
      <div className="pt-16"> {/* Add padding to account for fixed navbar */}
        <StaticPageLayout
          title={t('terms.title')}
          subtitle={t('terms.subtitle')}
          icon="gavel"
        >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-xl p-8 shadow-md mt-12"
      >
        <p className={`text-blue-900/70 mb-8 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('terms.lastUpdated')}
        </p>
        
        <div className="space-y-8">
          {termsSections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.05 * index }}
            >
              <h2 className={`text-xl font-bold text-blue-950 mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
                {index + 1}. {section.title}
              </h2>
              <p className={`text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
                {section.content}
              </p>
            </motion.div>
          ))}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
          className="mt-12 pt-8 border-t border-gray-200"
        >
          <p className={`text-blue-900/70 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('terms.agreement')}
          </p>
        </motion.div>
      </motion.div>
        </StaticPageLayout>
        <Footer />
      </div>
    </main>
  );
}
