'use client';

import { ReactNode } from 'react';
import { LanguageProvider } from '@/context/LanguageContext';
import { usePathname } from 'next/navigation';
import Head from 'next/head';
import { generateMetadata as generateSEOMetadata, PAGE_SEO_CONFIG, SITE_CONFIG } from '@/lib/seo';

interface LocaleLayoutProps {
  children: ReactNode;
  params: { locale: string };
}

// Component to inject page-specific SEO meta tags
function SEOMetaTags({ locale, pathname }: { locale: string; pathname: string }) {
  // Determine the page key from pathname
  const getPageKey = (path: string): string => {
    const cleanPath = path.replace(`/${locale}`, '').replace(/^\//, '') || 'home';
    
    // Map paths to page keys
    const pathMappings: Record<string, string> = {
      '': 'home',
      'home': 'home',
      'about': 'about',
      'app': 'app',
      'blog': 'blog',
      'docs': 'docs',
      'privacy': 'privacy',
      'terms': 'terms',
      'status': 'status',
      'support': 'support',
      'security': 'security',
      'explore': 'explore',
      'learn': 'learn',
      'dashboard': 'app',
      'feature-request': 'support'
    };

    // Handle nested paths
    const segments = cleanPath.split('/');
    const mainSegment = segments[0];
    
    return pathMappings[mainSegment] || pathMappings[cleanPath] || 'home';
  };

  const pageKey = getPageKey(pathname);
  const pageSEO = PAGE_SEO_CONFIG[pageKey] || PAGE_SEO_CONFIG.home;
  
  // Generate canonical URL
  const canonicalUrl = `${SITE_CONFIG.url}${locale === 'en' ? '' : `/${locale}`}${pathname.replace(`/${locale}`, '')}`;
  
  // Generate page-specific title and description
  const pageTitle = locale === 'ar' 
    ? `${pageSEO.title} | مخبأ`
    : pageSEO.title;
    
  const pageDescription = pageSEO.description;
  const ogImage = pageSEO.ogImage || '/og-image.jpg';

  return (
    <Head>
      {/* Basic meta tags */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={[...SITE_CONFIG.keywords, ...(pageSEO.keywords || [])].join(', ')} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Language alternates */}
      <link rel="alternate" hrefLang="en" href={canonicalUrl.replace('/ar/', '/')} />
      <link rel="alternate" hrefLang="ar" href={locale === 'en' ? canonicalUrl.replace(SITE_CONFIG.url, `${SITE_CONFIG.url}/ar`) : canonicalUrl} />
      <link rel="alternate" hrefLang="x-default" href={canonicalUrl.replace('/ar/', '/')} />
      
      {/* Open Graph tags */}
      <meta property="og:type" content={pageSEO.ogType || 'website'} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:site_name" content={SITE_CONFIG.name} />
      <meta property="og:image" content={`${SITE_CONFIG.url}${ogImage}`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={pageTitle} />
      <meta property="og:locale" content={locale === 'ar' ? 'ar_SA' : 'en_US'} />
      <meta property="og:locale:alternate" content={locale === 'ar' ? 'en_US' : 'ar_SA'} />
      
      {/* Twitter Card tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={SITE_CONFIG.twitterHandle} />
      <meta name="twitter:creator" content={SITE_CONFIG.twitterHandle} />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={`${SITE_CONFIG.url}${ogImage}`} />
      
      {/* Language and direction */}
      <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} />
      
      {/* Mobile optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* Theme color based on locale */}
      <meta name="theme-color" content="#73AED2" />
      
      {/* Robots meta based on page configuration */}
      {pageSEO.noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Article-specific meta tags for blog posts */}
      {pageSEO.article && (
        <>
          <meta property="article:published_time" content={pageSEO.article.publishedTime} />
          {pageSEO.article.modifiedTime && (
            <meta property="article:modified_time" content={pageSEO.article.modifiedTime} />
          )}
          {pageSEO.article.author && (
            <meta property="article:author" content={pageSEO.article.author} />
          )}
          {pageSEO.article.section && (
            <meta property="article:section" content={pageSEO.article.section} />
          )}
          {pageSEO.article.tags && 
            pageSEO.article.tags.map((tag) => (
              <meta key={tag} property="article:tag" content={tag} />
            ))
          }
        </>
      )}
    </Head>
  );
}

export default function LocaleLayout({ children, params }: LocaleLayoutProps) {
  const pathname = usePathname();
  
  // Ensure we have a valid locale, default to 'en' if not
  const validLocale = (params.locale === 'ar' || params.locale === 'en') ? params.locale : 'en';

  return (
    <LanguageProvider initialLanguage={validLocale}>
      <SEOMetaTags locale={validLocale} pathname={pathname} />
      {children}
    </LanguageProvider>
  );
} 