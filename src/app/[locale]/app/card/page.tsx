'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';
import { CardImage } from '@/components/OptimizedImage';
import CaptchaWrapper, { useCaptcha } from '@/components/CaptchaWrapper';

export default function CardPage() {
  const { t, isRTL } = useLanguage();
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone_number: '',
    country: '',
    interest: '',
    card_type: '',
    wallet_address: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | 'already_joined' | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  // CAPTCHA management
  const {
    captchaToken,
    captchaError,
    captchaRef,
    handleCaptcha<PERSON><PERSON>e,
    handleCaptchaError,
    resetCaptcha,
    isCaptchaValid
  } = useCaptcha();

  const interestOptions = [
    { value: 'online_purchases', label: t('card.form.onlinePurchases') },
    { value: 'travel_spending', label: t('card.form.travelSpending') },
    { value: 'fiat_withdrawal', label: t('card.form.fiatWithdrawal') },
    { value: 'crypto_cashback', label: t('card.form.cryptoCashback') },
    { value: 'defi_access', label: t('card.form.defiAccess') },
    { value: 'other', label: t('card.form.other') }
  ];

  const cardTypeOptions = [
    { value: 'virtual', label: t('card.form.virtual') },
    { value: 'physical', label: t('card.form.physical') },
    { value: 'both', label: t('card.form.both') }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);
    setErrorMessage('');

    // Validate CAPTCHA
    if (!isCaptchaValid()) {
      setSubmitStatus('error');
      setErrorMessage('Please complete the CAPTCHA verification.');
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/card-waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          captchaToken
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          full_name: '',
          email: '',
          phone_number: '',
          country: '',
          interest: '',
          card_type: '',
          wallet_address: ''
        });
        resetCaptcha(); // Reset CAPTCHA on success
        setTimeout(() => {
          setShowForm(false);
          setSubmitStatus(null);
        }, 3000);
      } else {
        // Handle duplicate email as info rather than error
        if (response.status === 409) {
          setSubmitStatus('already_joined');
          setErrorMessage(result.error || "You're already on the waitlist!");
        } else {
          setSubmitStatus('error');
          setErrorMessage(result.error || 'Failed to join waitlist');
          resetCaptcha(); // Reset CAPTCHA on error
        }
      }
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage('Network error. Please try again.');
      resetCaptcha(); // Reset CAPTCHA on error
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating geometric shapes */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-primary/20 to-accent/20 rounded-lg opacity-30"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            rotate: [0, -180, -360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-60 right-16 w-16 h-16 bg-gradient-to-r from-accent/20 to-primary/20 rounded-full opacity-25"
        />
        <motion.div
          animate={{
            x: [0, 60, 0],
            y: [0, -80, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-32 left-1/4 w-12 h-12 bg-gradient-to-r from-blue-400/20 to-purple-400/20 transform rotate-45 opacity-20"
        />
      </div>

      <div className="relative z-10 flex flex-col items-center justify-center p-4 md:p-6 min-h-screen py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-6xl w-full text-center mx-auto"
          dir={isRTL ? 'rtl' : 'ltr'}
        >
          {!showForm ? (
            <>
              {/* Hero Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-6"
              >
                <motion.h1 
                  className={`text-2xl md:text-4xl lg:text-5xl font-bold text-white mb-3 ${isRTL ? 'font-tajawal' : ''}`}
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  {t('card.title')}
                </motion.h1>
                <motion.p 
                  className={`text-base md:text-lg text-gray-300 mb-2 max-w-2xl mx-auto leading-relaxed ${isRTL ? 'font-tajawal' : ''}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  {t('card.subtitle')}
                </motion.p>
                <motion.p 
                  className={`text-sm text-gray-400 max-w-xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                >
                  {t('card.description')}
                </motion.p>
              </motion.div>

              {/* Enhanced Card Preview Section */}
              <div className="relative mb-8">
                {/* Card Showcase */}
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8, rotateY: -30 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{ duration: 1, delay: 1 }}
                  className="relative w-full max-w-sm mx-auto mb-5"
                >
                  {/* Glow Effect Background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-primary/30 to-accent/30 rounded-2xl blur-2xl scale-110 animate-pulse"></div>
                  
                  {/* Card Container */}
                  <motion.div 
                    className="relative w-full h-56 mx-auto transform hover:scale-105 transition-all duration-700 group perspective-1000"
                    whileHover={{ 
                      rotateY: 10,
                      rotateX: 5,
                      z: 50
                    }}
                    transition={{ duration: 0.6 }}
                  >
                    {/* Card Shadow */}
                    <div className="absolute inset-0 bg-black/40 rounded-xl blur-lg transform translate-y-5 scale-95 group-hover:scale-100 transition-all duration-700"></div>
                    
                    {/* Main Card */}
                    <div className="relative w-full h-full">
                      <CardImage
                        src="/mokhbacreditcard.svg" 
                        alt="Mokhba Credit Card Design" 
                        className="w-full h-full object-contain drop-shadow-2xl filter group-hover:drop-shadow-4xl transition-all duration-500"
                      />
                      
                      {/* Interactive glow overlay */}
                      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-primary/10 to-accent/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      
                      {/* Holographic effect */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-white/10 via-transparent to-transparent rounded-2xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Stats/Features Grid */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 1.2 }}
                  className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-3xl mx-auto mb-6"
                >
                  {[
                    { icon: "contactless", label: t('card.contactless'), desc: t('card.contactlessDesc'), color: "from-blue-400 to-blue-600" },
                    { icon: "security", label: t('card.bankLevel'), desc: t('card.secure'), color: "from-green-400 to-green-600" },
                    { icon: "flash_on", label: t('card.instant'), desc: t('card.instantDesc'), color: "from-yellow-400 to-yellow-600" },
                    { icon: "globe", label: t('card.global'), desc: t('card.globalDesc'), color: "from-purple-400 to-purple-600" }
                  ].map((feature, index) => (
                    <motion.div
                      key={feature.label}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
                      whileHover={{ scale: 1.05, y: -5 }}
                      className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-lg p-3 hover:bg-white/10 transition-all duration-300 group cursor-pointer"
                    >
                      <motion.div
                        className={`w-10 h-10 mx-auto mb-2 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                        whileHover={{ rotate: 5 }}
                      >
                        <span className="material-symbols-outlined text-white text-lg">
                          {feature.icon}
                        </span>
                      </motion.div>
                      <h3 className={`text-white font-bold text-sm mb-1 ${isRTL ? 'font-tajawal' : ''}`}>
                        {feature.label}
                      </h3>
                      <p className={`text-gray-400 text-xs ${isRTL ? 'font-tajawal' : ''}`}>
                        {feature.desc}
                      </p>
                    </motion.div>
                  ))}
                </motion.div>

                {/* CTA Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 1.8 }}
                  className="space-y-3"
                >
                  {/* Main CTA Button */}
                  <motion.button
                    onClick={() => setShowForm(true)}
                    whileHover={{ scale: 1.05, y: -3 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative bg-gradient-to-r from-primary via-accent to-primary bg-size-200 bg-pos-0 hover:bg-pos-100 text-white font-bold py-3 px-8 rounded-lg text-base shadow-2xl hover:shadow-primary/50 transition-all duration-500 group overflow-hidden"
                  >
                    {/* Button background animation */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-accent/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    {/* Button content */}
                    <div className="relative flex items-center space-x-2">
                      <motion.span 
                        animate={{ rotate: [0, 15, -15, 0] }}
                        transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                        className="material-symbols-outlined text-lg group-hover:text-yellow-300"
                      >
                        notifications
                      </motion.span>
                      <span className={`${isRTL ? 'font-tajawal' : ''}`}>
                        {t('card.form.submit')}
                      </span>
                      <motion.span 
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                        className="material-symbols-outlined group-hover:translate-x-1 transition-transform"
                      >
                        arrow_forward
                      </motion.span>
                    </div>
                  </motion.button>

                  {/* Social proof */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.8, delay: 2 }}
                    className="flex items-center justify-center text-gray-400"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="material-symbols-outlined text-primary text-sm">people</span>
                      <span className={`text-xs ${isRTL ? 'font-tajawal' : ''}`}>{t('card.peopleWaiting')}</span>
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </>
          ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white/10 backdrop-blur-md rounded-xl p-6 w-full max-w-lg mx-auto"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-xl font-bold text-white ${isRTL ? 'font-tajawal' : ''}`}>{t('card.form.title')}</h2>
              <button
                onClick={() => setShowForm(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <span className="material-symbols-outlined">close</span>
              </button>
            </div>

            {submitStatus === 'success' && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-500/20 border border-green-500/50 rounded-lg p-4 mb-4"
              >
                <p className={`text-green-300 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.success')}
                </p>
              </motion.div>
            )}

            {submitStatus === 'error' && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4"
              >
                <p className={`text-red-300 text-sm ${isRTL ? 'font-tajawal' : ''}`}>{errorMessage || t('card.form.error')}</p>
              </motion.div>
            )}

            {submitStatus === 'already_joined' && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-4"
              >
                <div className="flex items-center space-x-2">
                  <span className="material-symbols-outlined text-blue-300 text-lg">info</span>
                  <p className={`text-blue-300 text-sm ${isRTL ? 'font-tajawal' : ''}`}>{errorMessage || t('card.form.alreadyJoined')}</p>
                </div>
              </motion.div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Full Name */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.fullName')} *
                </label>
                <input
                  type="text"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleInputChange}
                  required
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  placeholder={t('card.form.fullNamePlaceholder')}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Email */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.email')} *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  placeholder={t('card.form.emailPlaceholder')}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Phone Number */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.phone')} *
                </label>
                <input
                  type="tel"
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  required
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  placeholder={t('card.form.phonePlaceholder')}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Country */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.country')} *
                </label>
                <input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  required
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  placeholder={t('card.form.countryPlaceholder')}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Interest */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.interest')}
                </label>
                <select
                  name="interest"
                  value={formData.interest}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="">{t('card.form.interest')} (اختياري)</option>
                  {interestOptions.map(option => (
                    <option key={option.value} value={option.value} className="bg-gray-800">
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Card Type */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.cardType')}
                </label>
                <select
                  name="card_type"
                  value={formData.card_type}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="">{t('card.form.cardType')} (اختياري)</option>
                  {cardTypeOptions.map(option => (
                    <option key={option.value} value={option.value} className="bg-gray-800">
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Wallet Address */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('card.form.walletAddress')}
                </label>
                <input
                  type="text"
                  name="wallet_address"
                  value={formData.wallet_address}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
                  placeholder={t('card.form.walletAddressPlaceholder')}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* CAPTCHA Protection */}
              <div>
                <CaptchaWrapper
                  onCaptchaChange={handleCaptchaChange}
                  onCaptchaError={handleCaptchaError}
                  className="mb-4"
                />
                {captchaError && (
                  <p className="mt-2 text-sm text-red-400">{captchaError}</p>
                )}
              </div>

              {/* Submit Button */}
              <motion.button
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                className={`w-full bg-gradient-to-r from-primary to-accent text-white font-semibold py-3 px-6 rounded-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 ${isRTL ? 'font-tajawal' : ''}`}
                dir={isRTL ? 'rtl' : 'ltr'}
              >
                {isSubmitting ? (
                  <>
                    <span className="animate-spin material-symbols-outlined">progress_activity</span>
                    <span>{t('card.form.submitting')}</span>
                  </>
                ) : (
                  <>
                    <span className="material-symbols-outlined">notifications</span>
                    <span>{t('card.form.submit')}</span>
                  </>
                )}
              </motion.button>
            </form>
          </motion.div>
        )}
        </motion.div>
      </div>
    </div>
  );
}
