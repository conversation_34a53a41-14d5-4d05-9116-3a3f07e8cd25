'use client';

import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';

export default function MoveCryptoPage() {
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;

  // Define the option cards
  const options = [
    {
      id: 'buy',
      title: 'Buy',
      icon: 'shopping_cart',
      route: `/${locale}/app/move-crypto/buy`,
      description: 'Purchase crypto with fiat currency'
    },
    {
      id: 'sell',
      title: 'Sell',
      icon: 'attach_money',
      route: `/${locale}/app/move-crypto/sell`,
      description: 'Convert crypto to fiat currency'
    },
    {
      id: 'swap',
      title: 'Swap',
      icon: 'swap_horiz',
      route: `/${locale}/app/move-crypto/swap`,
      description: 'Exchange one crypto for another'
    },
    {
      id: 'send',
      title: 'Send',
      icon: 'send',
      route: `/${locale}/app/move-crypto/send`,
      description: 'Send crypto to another wallet address'
    },
    {
      id: 'transfer',
      title: 'Transfer',
      icon: 'compare_arrows',
      route: `/${locale}/app/move-crypto/transfer`,
      description: 'Move assets between your own accounts'
    },
    {
      id: 'receive',
      title: 'Receive',
      icon: 'download',
      route: `/${locale}/app/move-crypto/receive`,
      description: 'Receive crypto from others'
    },
    {
      id: 'stake',
      title: 'Stake',
      icon: 'savings',
      route: `/${locale}/app/move-crypto/stake`,
      description: 'Earn rewards by staking your crypto'
    }
  ];

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="flex flex-col min-h-full p-4 md:p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
          Move Crypto
        </h1>
        <p className="text-gray-300 text-lg">
          What would you like to do?
        </p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto"
      >
        {options.map((option) => (
          <motion.div
            key={option.id}
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="bg-gray-800 rounded-xl p-6 cursor-pointer hover:bg-gray-750 transition-colors border border-gray-700"
            onClick={() => router.push(option.route)}
          >
            <div className="flex items-start">
              <div className="bg-primary rounded-full p-3 mr-4">
                <span className="material-symbols-outlined text-white">
                  {option.icon}
                </span>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white mb-1">
                  {option.title}
                </h3>
                <p className="text-gray-400">
                  {option.description}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
