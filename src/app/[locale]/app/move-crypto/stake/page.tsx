'use client';

import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import CryptoActionCard from '@/components/CryptoActionCard';

export default function StakePage() {
  const { t, isRTL } = useLanguage();
  const [selectedCrypto, setSelectedCrypto] = useState('Ethereum');
  const [amount, setAmount] = useState('');
  const [stakingPeriod, setStakingPeriod] = useState('3 months');

  const stakingOptions = [
    {
      provider: 'Lido',
      apy: '3.8%',
      lockPeriod: 'No lock',
      minAmount: '0.01 ETH',
      tag: 'POPULAR'
    },
    {
      provider: 'Rocket Pool',
      apy: '4.2%',
      lockPeriod: 'No lock',
      minAmount: '0.01 ETH',
      tag: 'HIGHEST APY'
    },
    {
      provider: 'Coinbase',
      apy: '3.5%',
      lockPeriod: 'No lock',
      minAmount: '0.01 ETH',
      tag: 'TRUSTED'
    }
  ];

  return (
    <CryptoActionCard>
      {/* Coming Soon Overlay */}
      <div className="relative">
        {/* Content with blur effect */}
        <div className="filter blur-sm pointer-events-none">
          {/* Wallet Selection */}
          <div className="mb-5">
        <label className="block text-gray-400 mb-2">Stake from</label>
        <div className="flex items-center justify-between bg-gray-800 rounded-xl p-3 cursor-pointer hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <span className="material-symbols-outlined text-white text-sm">
                account_balance_wallet
              </span>
            </div>
            <span className="text-gray-300">Select Wallet</span>
          </div>
          <span className="material-symbols-outlined text-gray-400">
            expand_more
          </span>
        </div>
      </div>

      {/* Asset Selection */}
      <div className="mb-5">
        <label className="block text-gray-400 mb-2">Asset</label>
        <div className="flex items-center justify-between bg-gray-800 rounded-xl p-3 cursor-pointer hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
              <img 
                src="/ethereum-eth-logo.svg" 
                alt="Ethereum" 
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <div className="text-white">Ethereum</div>
              <div className="text-gray-400 text-sm">Balance: 1.24 ETH</div>
            </div>
          </div>
          <span className="material-symbols-outlined text-gray-400">
            expand_more
          </span>
        </div>
      </div>

      {/* Amount */}
      <div className="mb-5">
        <label className="block text-gray-400 mb-2">Amount</label>
        <div className="bg-gray-800 rounded-xl overflow-hidden hover:bg-gray-750 transition-colors">
          <div className="flex">
            <input 
              type="text" 
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="flex-1 bg-transparent text-white p-3 border-none focus:outline-none"
            />
            <button className="bg-gray-700 text-blue-400 px-3 text-sm">
              MAX
            </button>
          </div>
        </div>
      </div>

      {/* Staking Options */}
      <div className="mb-5">
        <label className="block text-gray-400 mb-2">Staking Options</label>
        {stakingOptions.map((option, index) => (
          <div key={index} className="bg-gray-800 rounded-xl p-4 mb-3 cursor-pointer hover:bg-gray-750 transition-colors">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="font-bold text-white mr-2">{option.provider}</div>
                <span className="material-symbols-outlined text-gray-400 text-sm">
                  info
                </span>
              </div>
              <div className="bg-blue-900 text-blue-300 text-xs px-2 py-1 rounded">
                {option.tag}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <div className="text-gray-400 text-sm">APY</div>
                <div className="text-white">{option.apy}</div>
              </div>
              <div>
                <div className="text-gray-400 text-sm">Lock Period</div>
                <div className="text-white">{option.lockPeriod}</div>
              </div>
              <div>
                <div className="text-gray-400 text-sm">Min Amount</div>
                <div className="text-white">{option.minAmount}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Rewards Estimate */}
      <div className="bg-gray-800 rounded-xl p-4 mb-5 hover:bg-gray-750 transition-colors">
        <div className="text-gray-400 mb-2">Estimated Rewards</div>
        <div className="flex justify-between items-center">
          <div className="text-gray-400">Monthly</div>
          <div className="text-white">~0.0032 ETH</div>
        </div>
        <div className="flex justify-between items-center mt-2">
          <div className="text-gray-400">Yearly</div>
          <div className="text-white">~0.0384 ETH</div>
        </div>
      </div>

          {/* Connect Button */}
          <button className="w-full bg-primary hover:bg-primary/90 text-white py-3 rounded-xl font-medium transition-colors flex items-center justify-center">
            <span className="material-symbols-outlined mr-2">
              wallet
            </span>
            Connect Wallet
          </button>
        </div>

        {/* Coming Soon Overlay */}
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-10 min-h-full">
          <div className="text-center px-6 max-w-md">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {t('common.comingSoon') || 'Coming Soon'}
            </h2>
            <p className="text-gray-300 text-xl">
              {t('stake.comingSoonDesc') || 'This feature will be available soon'}
            </p>
          </div>
        </div>
      </div>
    </CryptoActionCard>
  );
}
