'use client';

import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import CryptoActionCard from '@/components/CryptoActionCard';

export default function SwapPage() {
  const { t, isRTL } = useLanguage();
  const [fromCrypto, setFromCrypto] = useState('Ethereum');
  const [toCrypto, setToCrypto] = useState('Bitcoin');
  const [amount, setAmount] = useState('0.5');

  return (
    <CryptoActionCard>
      {/* Coming Soon Overlay */}
      <div className="relative">
        {/* Content with blur effect */}
        <div className="filter blur-sm pointer-events-none">
          {/* Wallet Selection */}
          <div className="mb-5">
        <div className="flex items-center justify-between bg-gray-800 rounded-xl p-3 cursor-pointer hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <span className="material-symbols-outlined text-white text-sm">
                account_balance_wallet
              </span>
            </div>
            <span className="text-gray-300">Select Wallet</span>
          </div>
          <span className="material-symbols-outlined text-gray-400">
            expand_more
          </span>
        </div>
      </div>

      {/* From Crypto */}
      <div className="mb-2">
        <div className="text-gray-400 mb-2">From</div>
        <div className="bg-gray-800 rounded-xl p-4 hover:bg-gray-750 transition-colors">
          <div className="flex justify-between mb-3">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full mr-3 overflow-hidden">
                <img 
                  src="/ethereum-eth-logo.svg" 
                  alt="Ethereum" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="text-white font-medium">ETH</div>
            </div>
            <div className="flex items-center">
              <input 
                type="text" 
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="bg-transparent text-white text-right focus:outline-none w-24"
              />
              <span className="material-symbols-outlined text-gray-400 ml-2">
                expand_more
              </span>
            </div>
          </div>
          <div className="flex justify-between text-sm">
            <div className="text-gray-400">Ethereum</div>
            <div className="text-gray-400">Balance: 1.24 ETH</div>
          </div>
        </div>
      </div>

      {/* Swap Button */}
      <div className="flex justify-center -my-3 relative z-10">
        <button className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-gray-600 transition-colors">
          <span className="material-symbols-outlined text-white">
            swap_vert
          </span>
        </button>
      </div>

      {/* To Crypto */}
      <div className="mb-6">
        <div className="text-gray-400 mb-2">To</div>
        <div className="bg-gray-800 rounded-xl p-4 hover:bg-gray-750 transition-colors">
          <div className="flex justify-between mb-3">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full mr-3 overflow-hidden">
                <img 
                  src="https://cryptologos.cc/logos/bitcoin-btc-logo.png" 
                  alt="Bitcoin" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="text-white font-medium">BTC</div>
            </div>
            <div className="flex items-center">
              <input 
                type="text" 
                value="0.018"
                readOnly
                className="bg-transparent text-white text-right focus:outline-none w-24"
              />
              <span className="material-symbols-outlined text-gray-400 ml-2">
                expand_more
              </span>
            </div>
          </div>
          <div className="flex justify-between text-sm">
            <div className="text-gray-400">Bitcoin</div>
            <div className="text-gray-400">Balance: 0.05 BTC</div>
          </div>
        </div>
      </div>

      {/* Exchange Rate */}
      <div className="bg-gray-800 rounded-xl p-4 mb-5 hover:bg-gray-750 transition-colors">
        <div className="flex justify-between items-center">
          <div className="text-gray-400">Exchange Rate</div>
          <div className="text-white">1 ETH ≈ 0.036 BTC</div>
        </div>
        <div className="flex justify-between items-center mt-2">
          <div className="text-gray-400">Network Fee</div>
          <div className="text-white">≈ 0.0005 ETH</div>
        </div>
      </div>

      {/* Quote */}
      <div className="mb-5">
        <div className="text-gray-400 mb-2">Recommended Provider</div>
        <div className="bg-gray-800 rounded-xl p-4 hover:bg-gray-750 transition-colors">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <div className="font-bold text-white mr-2">1inch</div>
              <span className="material-symbols-outlined text-gray-400 text-sm">
                info
              </span>
            </div>
            <div className="bg-blue-900 text-blue-300 text-xs px-2 py-1 rounded">
              BEST RATE
            </div>
          </div>
          <div className="flex justify-between items-center">
            <div className="text-gray-400">Estimated Time</div>
            <div className="text-white">~2 minutes</div>
          </div>
        </div>
      </div>

      {/* See more quotes */}
      <div className="text-center mb-5">
        <button className="text-blue-400 hover:text-blue-300 transition-colors">
          See more providers
        </button>
      </div>

          {/* Connect Button */}
          <button className="w-full bg-primary hover:bg-primary/90 text-white py-3 rounded-xl font-medium transition-colors flex items-center justify-center">
            <span className="material-symbols-outlined mr-2">
              wallet
            </span>
            Connect Wallet
          </button>
        </div>

        {/* Coming Soon Overlay */}
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-10">
          <div className="text-center px-6">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {t('common.comingSoon') || 'Coming Soon'}
            </h2>
            <p className="text-gray-300 text-xl">
              {t('swap.comingSoonDesc') || 'This feature will be available soon'}
            </p>
          </div>
        </div>
      </div>
    </CryptoActionCard>
  );
}
