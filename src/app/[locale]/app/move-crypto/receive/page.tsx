'use client';

import { useLanguage } from '@/context/LanguageContext';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import CryptoActionCard from '@/components/CryptoActionCard';
import dynamic from 'next/dynamic';

// Dynamically import the wallet component to prevent SSR issues
const ReceiveWalletComponent = dynamic(
  () => import('@/components/ReceiveWalletComponent'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }
);

export default function ReceivePage() {
  const { t } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  return (
    <CryptoActionCard>
      {/* Tabs */}
      <div className="flex border-b border-gray-700 -mt-5 -mx-5 mb-6 sticky top-0 bg-gray-900 z-10">
        <Link
          href={`/${locale}/app/move-crypto/send`}
          className="flex-1 py-3 text-center text-gray-400 hover:text-gray-300 transition-colors text-sm"
        >
          {t('app.send')}
        </Link>
        <Link
          href={`/${locale}/app/move-crypto/transfer`}
          className="flex-1 py-3 text-center text-gray-400 hover:text-gray-300 transition-colors text-sm"
        >
          {t('app.transfer')}
        </Link>
        <Link
          href={`/${locale}/app/move-crypto/receive`}
          className="flex-1 py-3 text-center text-white font-medium border-b-2 border-primary text-sm"
        >
          {t('app.receive')}
        </Link>
      </div>

      {/* Dynamic Wallet Component */}
      <ReceiveWalletComponent />
    </CryptoActionCard>
  );
}
