'use client';

import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import CryptoActionCard from '@/components/CryptoActionCard';

export default function SellPage() {
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;
  const [selectedCrypto, setSelectedCrypto] = useState('Ethereum');
  const [amount, setAmount] = useState('0.1');
  const [currency, setCurrency] = useState('ETH');

  return (
    <CryptoActionCard>
      {/* Coming Soon Overlay */}
      <div className="relative">
        {/* Content with blur effect */}
        <div className="filter blur-sm pointer-events-none">
          {/* Tabs */}
          <div className="flex border-b border-gray-700 -mt-5 -mx-5 mb-6">
        <Link 
          href={`/${locale}/app/move-crypto/buy`}
          className="flex-1 py-4 text-center text-gray-400 hover:text-gray-300 transition-colors"
        >
          {t('app.buy')}
        </Link>
        <Link 
          href={`/${locale}/app/move-crypto/sell`}
          className="flex-1 py-4 text-center text-white font-medium border-b-2 border-primary"
        >
          {t('app.sell')}
        </Link>
      </div>

      {/* Wallet Selection */}
      <div className="mb-5">
        <div className="flex items-center justify-between bg-gray-800 rounded-xl p-3 cursor-pointer hover:bg-gray-750 transition-colors">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <span className="material-symbols-outlined text-white text-sm">
                account_balance_wallet
              </span>
            </div>
            <span className="text-gray-300">{t('trade.selectWallet')}</span>
          </div>
          <span className="material-symbols-outlined text-gray-400">
            expand_more
          </span>
        </div>
      </div>

      {/* Currency Selection */}
      <div className="flex justify-between mb-6">
        <div className="flex-1 mr-2">
          <div className="flex items-center justify-between bg-gray-800 rounded-xl p-3 cursor-pointer hover:bg-gray-750 transition-colors">
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
                <img 
                  src="/ethereum-eth-logo.svg" 
                  alt="Ethereum" 
                  className="w-full h-full object-cover"
                />
              </div>
              <span className="text-gray-300">ETH</span>
            </div>
            <span className="material-symbols-outlined text-gray-400">
              expand_more
            </span>
          </div>
        </div>
      </div>

      {/* Amount Display */}
      <div className="text-center mb-6">
        <h2 className="text-5xl font-bold text-white mb-2">
          {amount} {currency}
        </h2>
      </div>

      {/* Receive Currency Selection */}
      <div className="mb-5">
        <div className="bg-gray-800 rounded-xl p-4 cursor-pointer hover:bg-gray-750 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full mr-3 overflow-hidden flex items-center justify-center bg-green-500">
                <span className="material-symbols-outlined text-white text-sm">
                  payments
                </span>
              </div>
              <div>
                <div className="text-white font-medium">{t('trade.saudiRiyal')}</div>
                <div className="text-gray-400 text-sm">SAR</div>
              </div>
            </div>
            <span className="material-symbols-outlined text-gray-400">
              expand_more
            </span>
          </div>
        </div>
      </div>

      {/* Payment Method */}
      <div className="mb-6">
        <div className="bg-gray-800 rounded-xl p-4 cursor-pointer hover:bg-gray-750 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="material-symbols-outlined text-white mr-3">
                account_balance
              </span>
              <div className="text-white">{t('trade.bankTransfer')}</div>
            </div>
            <span className="material-symbols-outlined text-gray-400">
              expand_more
            </span>
          </div>
        </div>
      </div>

      {/* Quote */}
      <div className="mb-5">
        <div className="text-gray-400 mb-2">{t('trade.recommendedQuote')}</div>
        <div className="bg-gray-800 rounded-xl p-4 hover:bg-gray-750 transition-colors">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <div className="font-bold text-white mr-2">BANXA</div>
              <span className="material-symbols-outlined text-gray-400 text-sm">
                info
              </span>
            </div>
            <div className="bg-blue-900 text-blue-300 text-xs px-2 py-1 rounded">
              {t('trade.mostReliable')}
            </div>
          </div>
          <div className="flex justify-between items-center">
            <div className="text-gray-400">{t('trade.youllReceiveApproximately')}</div>
            <div className="text-right">
              <div className="text-white font-medium">1,758.60 SAR</div>
              <div className="text-gray-400 text-sm">≈ 0.1 ETH</div>
            </div>
          </div>
        </div>
      </div>

      {/* See more quotes */}
      <div className="text-center mb-5">
        <button className="text-blue-400 hover:text-blue-300 transition-colors">
          {t('trade.seeMoreQuotes')}
        </button>
      </div>

          {/* Connect Button */}
          <button className="w-full bg-primary hover:bg-primary/90 text-white py-3 rounded-xl font-medium transition-colors flex items-center justify-center">
            <span className="material-symbols-outlined mr-2">
              wallet
            </span>
            {t('trade.connectWallet')}
          </button>
        </div>

        {/* Coming Soon Overlay */}
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-10">
          <div className="text-center px-6">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {t('common.comingSoon') || 'Coming Soon'}
            </h2>
            <p className="text-gray-300 text-xl">
              {t('sell.comingSoonDesc') || 'This feature will be available soon'}
            </p>
          </div>
        </div>
      </div>
    </CryptoActionCard>
  );
}
