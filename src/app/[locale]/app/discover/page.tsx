'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';

// Types for API data
interface GlobalData {
  data: {
    total_market_cap: { usd: number };
    total_volume: { usd: number };
    market_cap_percentage: { btc: number };
    active_cryptocurrencies: number;
    markets: number;
    total_exchanges: number;
    market_cap_change_percentage_24h_usd: number;
  };
}

interface CoinData {
  id: string;
  name: string;
  symbol: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  total_volume: number;
  price_change_percentage_24h: number;
  price_change_percentage_7d_in_currency?: number;
  image: string;
  sparkline_in_7d?: {
    price: number[];
  };
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
}

interface TrendingCoin {
  item: {
    id: string;
    name: string;
    symbol: string;
    thumb: string;
    market_cap_rank: number;
    price_btc: number;
  };
}

const formatNumber = (num: number, isRTL: boolean) => {
  if (!num) return '0';
  if (num >= 1e12) return `${(num / 1e12).toFixed(2)}${isRTL ? 'ت' : 'T'}`;
  if (num >= 1e9) return `${(num / 1e9).toFixed(2)}${isRTL ? 'ب' : 'B'}`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(2)}${isRTL ? 'م' : 'M'}`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(2)}${isRTL ? 'ك' : 'K'}`;
  return num.toFixed(2);
};

const formatPrice = (price: number) => {
  if (!price) return '$0.00';
  if (price < 0.01) return `$${price.toFixed(6)}`;
  if (price < 1) return `$${price.toFixed(4)}`;
  if (price < 100) return `$${price.toFixed(2)}`;
  return `$${price.toLocaleString()}`;
};

const MiniSparkline = ({ data, isPositive }: { data: number[], isPositive: boolean }) => {
  if (!data || data.length === 0) return null;
  
  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min;
  
  const points = data.map((price, index) => {
    const x = (index / (data.length - 1)) * 60;
    const y = range === 0 ? 20 : 40 - ((price - min) / range) * 40;
    return `${x},${y}`;
  }).join(' ');
  
  return (
    <svg width="60" height="40" className="opacity-70">
      <polyline
        fill="none"
        stroke={isPositive ? '#10b981' : '#ef4444'}
        strokeWidth="1.5"
        points={points}
      />
    </svg>
  );
};

export default function DiscoverPage() {
  const { t, isRTL } = useLanguage();
  
  // State management
  const [globalData, setGlobalData] = useState<GlobalData | null>(null);
  const [marketData, setMarketData] = useState<CoinData[]>([]);
  const [trendingData, setTrendingData] = useState<TrendingCoin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'trending' | 'gainers' | 'losers'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Categories for filtering
  const categories = [
    { id: 'all', name: t('discover.livePrices'), icon: 'trending_up', gradient: 'from-blue-500 to-purple-600' },
    { id: 'defi', name: t('discover.defi'), icon: 'account_balance', gradient: 'from-green-500 to-emerald-600' },
    { id: 'nft', name: t('discover.nft'), icon: 'palette', gradient: 'from-pink-500 to-rose-600' },
    { id: 'gaming', name: t('discover.gaming'), icon: 'sports_esports', gradient: 'from-orange-500 to-amber-600' },
    { id: 'layer-1', name: t('discover.layer1'), icon: 'layers', gradient: 'from-indigo-500 to-blue-600' },
    { id: 'meme-token', name: t('discover.memecoins'), icon: 'emoji_emotions', gradient: 'from-yellow-500 to-orange-600' },
  ];

  // Fetch data from CoinGecko API
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [globalResponse, marketResponse, trendingResponse] = await Promise.all([
        fetch('https://api.coingecko.com/api/v3/global'),
        fetch('https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=true&price_change_percentage=24h,7d'),
        fetch('https://api.coingecko.com/api/v3/search/trending')
      ]);

      const [globalResult, marketResult, trendingResult] = await Promise.all([
        globalResponse.json(),
        marketResponse.json(),
        trendingResponse.json()
      ]);

      setGlobalData(globalResult);
      setMarketData(marketResult);
      setTrendingData(trendingResult.coins);

    } catch (err) {
      setError('Failed to fetch market data');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, []);

  // Filter and sort data based on active tab
  const getFilteredData = () => {
    if (!marketData.length) return [];
    
    let filtered = [...marketData];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(coin => 
        coin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        coin.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort based on tab
    switch (activeTab) {
      case 'gainers':
        return filtered
          .filter(coin => coin.price_change_percentage_24h > 0)
          .sort((a, b) => b.price_change_percentage_24h - a.price_change_percentage_24h)
          .slice(0, 20);
      case 'losers':
        return filtered
          .filter(coin => coin.price_change_percentage_24h < 0)
          .sort((a, b) => a.price_change_percentage_24h - b.price_change_percentage_24h)
          .slice(0, 20);
      case 'trending':
        return trendingData.slice(0, 10).map(trend => {
          const marketCoin = marketData.find(coin => coin.id === trend.item.id);
          return marketCoin || {
            id: trend.item.id,
            name: trend.item.name,
            symbol: trend.item.symbol,
            current_price: 0,
            market_cap: 0,
            market_cap_rank: trend.item.market_cap_rank,
            total_volume: 0,
            price_change_percentage_24h: 0,
            image: trend.item.thumb,
            market_cap_change_percentage_24h: 0,
            circulating_supply: 0,
            total_supply: 0,
            max_supply: 0,
          };
        });
      default:
        return filtered.slice(0, 20);
    }
  };

  const filteredData = getFilteredData();

  if (loading) {
    return (
      <div className="w-full min-h-full flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="relative w-20 h-20 mx-auto mb-6">
            <div className="absolute inset-0 border-4 border-primary/30 rounded-full"></div>
            <div className="absolute inset-0 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <div className="absolute inset-2 border-2 border-accent/50 border-b-transparent rounded-full animate-spin animation-delay-150"></div>
          </div>
          <h3 className={`text-xl font-semibold text-white mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('discover.loadingMarketData')}
          </h3>
          <p className={`text-gray-400 ${isRTL ? 'font-tajawal' : ''}`}>
            Fetching live market data...
          </p>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full min-h-full flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <div className="w-24 h-24 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="material-symbols-outlined text-4xl text-red-400">error</span>
          </div>
          <h2 className={`text-2xl font-bold text-white mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('discover.error')}
          </h2>
          <p className={`text-gray-400 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>{error}</p>
          <button
            onClick={fetchData}
            className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white px-8 py-3 rounded-xl font-medium transition-all transform hover:scale-105"
          >
            {t('discover.retry')}
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900" dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-32 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-32 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 p-4 md:p-6 lg:p-8 space-y-8 min-h-full">
        {/* Hero Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-8"
        >
          <motion.h1 
            className={`text-4xl md:text-6xl lg:text-7xl font-bold mb-4 ${isRTL ? 'font-tajawal' : ''}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <span className="text-white">
              {t('discover.title')}
            </span>
          </motion.h1>
          <motion.p 
            className={`text-xl text-gray-300 mb-8 max-w-2xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {t('discover.subtitle')}
          </motion.p>
          
          {/* Live indicator */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6 }}
            className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-4 py-2"
          >
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className={`text-green-300 text-sm font-medium ${isRTL ? 'font-tajawal' : ''}`}>
              Live Data
            </span>
          </motion.div>
        </motion.div>

        {/* Global Market Stats */}
        {globalData && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {[
              {
                title: t('discover.totalMarketCap'),
                value: `$${formatNumber(globalData.data.total_market_cap.usd, isRTL)}`,
                change: globalData.data.market_cap_change_percentage_24h_usd,
                icon: 'trending_up',
                gradient: 'from-blue-500 to-purple-600'
              },
              {
                title: t('discover.totalVolume'),
                value: `$${formatNumber(globalData.data.total_volume.usd, isRTL)}`,
                icon: 'bar_chart',
                gradient: 'from-green-500 to-emerald-600'
              },
              {
                title: t('discover.btcDominance'),
                value: `${globalData.data.market_cap_percentage.btc.toFixed(1)}%`,
                icon: 'currency_bitcoin',
                gradient: 'from-orange-500 to-yellow-500'
              },
              {
                title: t('discover.activeCryptos'),
                value: globalData.data.active_cryptocurrencies.toLocaleString(),
                icon: 'token',
                gradient: 'from-pink-500 to-rose-600'
              }
            ].map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-2xl blur"></div>
                <div className="relative bg-gray-800/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:border-gray-600/50 transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${stat.gradient} rounded-xl flex items-center justify-center`}>
                      <span className="material-symbols-outlined text-white text-xl">{stat.icon}</span>
                    </div>
                    {stat.change && (
                      <div className={`flex items-center text-xs px-2 py-1 rounded-full ${
                        stat.change >= 0 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                      }`}>
                        <span className="material-symbols-outlined text-xs mr-1">
                          {stat.change >= 0 ? 'trending_up' : 'trending_down'}
                        </span>
                        {Math.abs(stat.change).toFixed(2)}%
                      </div>
                    )}
                  </div>
                  <h3 className={`text-sm font-medium text-gray-400 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                    {stat.title}
                  </h3>
                  <p className="text-2xl font-bold text-white">
                    {stat.value}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-800/40 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-6 lg:p-8"
        >
          {/* Search Bar */}
          <div className="relative mb-8">
            <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
              <span className="material-symbols-outlined text-gray-400">search</span>
            </div>
            <input
              type="text"
              placeholder={t('discover.searchPlaceholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-12 pr-4 py-4 bg-gray-700/50 border border-gray-600/50 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all ${isRTL ? 'font-tajawal text-right' : ''}`}
              dir={isRTL ? 'rtl' : 'ltr'}
            />
          </div>

          {/* Tab Navigation */}
          <div className="flex flex-wrap gap-3 mb-8">
            {[
              { key: 'all', label: t('discover.livePrices'), icon: 'trending_up', count: marketData.length },
              { key: 'trending', label: t('discover.trending'), icon: 'whatshot', count: trendingData.length },
              { key: 'gainers', label: t('discover.gainers'), icon: 'north', count: marketData.filter(c => c.price_change_percentage_24h > 0).length },
              { key: 'losers', label: t('discover.losers'), icon: 'south', count: marketData.filter(c => c.price_change_percentage_24h < 0).length },
            ].map((tab) => (
              <motion.button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`relative flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-all ${
                  activeTab === tab.key
                    ? 'bg-gradient-to-r from-primary to-accent text-white shadow-lg shadow-primary/25'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white'
                } ${isRTL ? 'font-tajawal' : ''}`}
              >
                <span className="material-symbols-outlined text-lg">{tab.icon}</span>
                <span>{tab.label}</span>
                <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              </motion.button>
            ))}
          </div>

          {/* Coins Grid */}
          <AnimatePresence mode="wait">
            {filteredData.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-16"
              >
                <div className="w-24 h-24 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="material-symbols-outlined text-4xl text-gray-500">search_off</span>
                </div>
                <h3 className={`text-xl font-semibold text-gray-300 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('discover.noResults')}
                </h3>
                <p className={`text-gray-500 ${isRTL ? 'font-tajawal' : ''}`}>
                  Try adjusting your search or filters
                </p>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-4"
              >
                {filteredData.map((coin, index) => (
                  <motion.div
                    key={coin.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="bg-gray-700/30 backdrop-blur border border-gray-600/30 rounded-2xl p-6 hover:border-gray-500/50 transition-all duration-300 cursor-pointer group"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="relative">
                          <img 
                            src={coin.image} 
                            alt={coin.name}
                            className="w-12 h-12 rounded-full ring-2 ring-gray-600 group-hover:ring-primary/50 transition-all"
                          />
                          <div className="absolute -top-1 -right-1 bg-gray-800 text-gray-400 text-xs px-1.5 py-0.5 rounded-full border border-gray-600">
                            #{coin.market_cap_rank || '—'}
                          </div>
                        </div>
                        <div>
                          <h3 className={`font-semibold text-white text-lg group-hover:text-primary transition-colors ${isRTL ? 'font-tajawal' : ''}`}>
                            {coin.name}
                          </h3>
                          <p className="text-gray-400 uppercase font-medium">
                            {coin.symbol}
                          </p>
                        </div>
                      </div>

                      <div className="text-right">
                        <p className="text-xl font-bold text-white mb-1">
                          {formatPrice(coin.current_price)}
                        </p>
                        <div className="flex items-center justify-end space-x-2">
                          <div className={`flex items-center px-2 py-1 rounded-lg text-sm font-medium ${
                            coin.price_change_percentage_24h >= 0 
                              ? 'bg-green-500/20 text-green-400' 
                              : 'bg-red-500/20 text-red-400'
                          }`}>
                            <span className="material-symbols-outlined text-xs mr-1">
                              {coin.price_change_percentage_24h >= 0 ? 'trending_up' : 'trending_down'}
                            </span>
                            {coin.price_change_percentage_24h >= 0 ? '+' : ''}
                            {coin.price_change_percentage_24h?.toFixed(2) || '0.00'}%
                          </div>
                          {coin.sparkline_in_7d?.price && (
                            <MiniSparkline 
                              data={coin.sparkline_in_7d.price} 
                              isPositive={coin.price_change_percentage_24h >= 0}
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-600/30">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className={`text-gray-400 ${isRTL ? 'font-tajawal' : ''}`}>
                            {t('discover.marketCap')}
                          </p>
                          <p className="text-white font-medium">
                            ${formatNumber(coin.market_cap, isRTL)}
                          </p>
                        </div>
                        <div>
                          <p className={`text-gray-400 ${isRTL ? 'font-tajawal' : ''}`}>
                            {t('discover.volume24h')}
                          </p>
                          <p className="text-white font-medium">
                            ${formatNumber(coin.total_volume, isRTL)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Categories Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-gray-800/40 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-6 lg:p-8"
        >
          <div className="text-center mb-8">
            <h2 className={`text-3xl font-bold text-white mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('discover.categories')}
            </h2>
            <p className={`text-gray-400 ${isRTL ? 'font-tajawal' : ''}`}>
              Explore different sectors of the crypto ecosystem
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 0.8 + index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                onClick={() => setSelectedCategory(category.id)}
                className="relative group cursor-pointer"
              >
                <div className={`absolute inset-0 bg-gradient-to-r ${category.gradient} opacity-0 group-hover:opacity-20 rounded-2xl blur transition-opacity duration-300`}></div>
                <div className="relative bg-gray-700/50 hover:bg-gray-600/50 rounded-2xl p-6 border border-gray-600/30 hover:border-gray-500/50 transition-all duration-300">
                  <div className={`w-16 h-16 bg-gradient-to-r ${category.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <span className="material-symbols-outlined text-white text-2xl">
                      {category.icon}
                    </span>
                  </div>
                  <h3 className={`text-lg font-semibold text-white text-center group-hover:text-primary transition-colors ${isRTL ? 'font-tajawal' : ''}`}>
                    {category.name}
                  </h3>
                  <div className="mt-3 text-center">
                    <span className="text-xs bg-gray-600/50 text-gray-300 px-3 py-1 rounded-full">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
