'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { usePathname, useParams } from 'next/navigation';
import { useLanguage } from '@/context/LanguageContext';
import { WalletBalanceProvider } from '@/context/WalletBalanceContext';
import dynamic from 'next/dynamic';

// Dynamically import ConnectWallet to prevent SSR issues with wagmi
const ConnectWallet = dynamic(
  () => import('@/components/ConnectWallet'),
  { 
    ssr: false,
    loading: () => (
      <div className="w-32 h-10 bg-gray-700 rounded-lg animate-pulse"></div>
    )
  }
);

// Menu item and submenu item types
interface MenuItem {
  icon: string;
  path: string;
  id: string;
  badge?: string;
}

interface SubMenuItem {
  icon: string;
  path: string;
  id: string;
}

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [currentPath, setCurrentPath] = useState('');
  const [activeItem, setActiveItem] = useState('dashboard');
  const [isMoveCryptoOpen, setIsMoveCryptoOpen] = useState(false);
  const [activeSubItem, setActiveSubItem] = useState('');
  const pathname = usePathname();
  const params = useParams();
  const locale = params.locale as string;
  const { t, isRTL } = useLanguage();

  // Define menu items inside the component to ensure translations are updated when language changes
  const menuItems: MenuItem[] = [
    { icon: 'dashboard', path: `/${locale}/app`, id: 'dashboard' },
    { icon: 'swap_horiz', path: `/${locale}/app/move-crypto`, id: 'move-crypto' },
    { icon: 'explore', path: `/${locale}/app/discover`, id: 'discover' },
    { icon: 'credit_card', path: `/${locale}/app/card`, id: 'card', badge: t('app.badge.pilot') },
  ];

  // Define submenu items inside the component
  const moveCryptoSubmenu: SubMenuItem[] = [
    { icon: 'shopping_cart', path: `/${locale}/app/move-crypto/buy`, id: 'buy' },
    { icon: 'attach_money', path: `/${locale}/app/move-crypto/sell`, id: 'sell' },
    { icon: 'swap_horiz', path: `/${locale}/app/move-crypto/swap`, id: 'swap' },
    { icon: 'send', path: `/${locale}/app/move-crypto/send`, id: 'send' },
    { icon: 'compare_arrows', path: `/${locale}/app/move-crypto/transfer`, id: 'transfer' },
    { icon: 'download', path: `/${locale}/app/move-crypto/receive`, id: 'receive' },
    { icon: 'savings', path: `/${locale}/app/move-crypto/stake`, id: 'stake' },
  ];

  // Update current path when pathname changes
  useEffect(() => {
    setCurrentPath(pathname);

    // Set the active item based on the path
    if (pathname === `/${locale}/app`) {
      setActiveItem('dashboard');
      setIsMoveCryptoOpen(false);
    } else if (pathname.includes(`/${locale}/app/move-crypto`) || pathname.includes(`/${locale}/app/send`) ||
               pathname.includes(`/${locale}/app/transfer`) || pathname.includes(`/${locale}/app/bridge`)) {
      setActiveItem('move-crypto');
      setIsMoveCryptoOpen(true);

      // Set active submenu item
      if (pathname.includes('/buy')) {
        setActiveSubItem('buy');
      } else if (pathname.includes('/sell')) {
        setActiveSubItem('sell');
      } else if (pathname.includes('/swap')) {
        setActiveSubItem('swap');
      } else if (pathname.includes('/send')) {
        setActiveSubItem('send');
      } else if (pathname.includes('/transfer')) {
        setActiveSubItem('transfer');
      } else if (pathname.includes('/receive')) {
        setActiveSubItem('receive');
      } else if (pathname.includes('/stake')) {
        setActiveSubItem('stake');
      } else {
        setActiveSubItem('');
      }
    } else if (pathname.includes(`/${locale}/app/discover`)) {
      setActiveItem('discover');
      setIsMoveCryptoOpen(false);
    } else if (pathname.includes(`/${locale}/app/card`)) {
      setActiveItem('card');
      setIsMoveCryptoOpen(false);
    }
  }, [pathname]);

  return (
    <WalletBalanceProvider>
      <div className={`min-h-screen bg-gray-900 text-white flex flex-col ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Top Navigation Bar */}
        <header className="bg-gray-800 border-b border-gray-700 py-3 flex items-center justify-between relative">
          {/* Mobile Menu Toggle */}
          <div className="flex items-center">
            <button
              className={`md:hidden ${isRTL ? 'ml-4' : 'mr-4'} text-gray-400 hover:text-white`}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <span className="material-symbols-outlined">
                {isMobileMenuOpen ? 'close' : 'menu'}
              </span>
            </button>
          </div>

          {/* Logo Container - Exactly matches sidebar width */}
          <div className={`absolute ${isRTL ? 'right-0' : 'left-0'} top-0 h-full w-64 flex justify-center items-center`}>
            <Link href={`/${locale}`} className="flex items-center">
              <Image
                src="/logo.svg"
                alt="Mokhba Logo"
                width={55}
                height={55}
                unoptimized
                className="transform scale-[1.44]" // Make the logo 1.44x larger (1.2 * 1.2)
              />
            </Link>
          </div>

          {/* Connect Wallet Button - Aligned to right */}
          <div className="flex items-center">
            <ConnectWallet />
          </div>
        </header>

        <div className="flex flex-1">
          {/* Sidebar - Desktop */}
          <aside className={`hidden md:block w-72 bg-gray-800 ${isRTL ? 'border-l' : 'border-r'} border-gray-700`}>
            <nav className="p-4">
              <ul className="space-y-3">
                {menuItems.map((item) => (
                  <li key={item.id}>
                    {item.id === 'move-crypto' ? (
                      <div>
                        <button
                          onClick={() => setIsMoveCryptoOpen(!isMoveCryptoOpen)}
                          className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                            activeItem === item.id
                              ? 'bg-primary text-white'
                              : 'text-gray-300 hover:bg-gray-700 hover:text-white hover:bg-primary/20'
                          }`}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        >
                          <div className="flex items-center">
                            {isRTL ? (
                              <>
                                <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                                <div className="w-6 h-6 ml-3 flex items-center justify-center">
                                  <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                    {item.icon}
                                  </span>
                                </div>
                              </>
                            ) : (
                              <>
                                <div className="w-6 h-6 mr-3 flex items-center justify-center">
                                  <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                    {item.icon}
                                  </span>
                                </div>
                                <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                              </>
                            )}
                          </div>
                          <span className="material-symbols-outlined text-sm" aria-hidden="true">
                            {isMoveCryptoOpen ? 'expand_less' : 'expand_more'}
                          </span>
                          <span className="sr-only">
                            {isMoveCryptoOpen ? t('common.collapse') : t('common.expand')}
                          </span>
                        </button>

                        {/* Submenu */}
                        {isMoveCryptoOpen && (
                          <ul className={`mt-2 ${isRTL ? 'mr-6' : 'ml-6'} space-y-2`}>
                            {moveCryptoSubmenu.map((subItem) => (
                              <li key={subItem.id}>
                                <Link
                                  href={subItem.path}
                                  className={`flex items-center p-2 rounded-lg transition-colors ${
                                    activeSubItem === subItem.id
                                      ? 'bg-primary/30 text-white'
                                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                                  }`}
                                >
                                  <div className="flex-1 min-w-0">
                                    <span className="text-sm font-medium">{t(`app.${subItem.id}`)}</span>
                                  </div>
                                  <div className={`w-6 h-6 ${isRTL ? 'mr-3' : 'ml-3'} flex items-center justify-center`}>
                                    <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                      {subItem.icon}
                                    </span>
                                  </div>
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ) : (
                      <Link
                        href={item.path}
                        className={`flex items-center p-3 rounded-lg transition-colors ${
                          activeItem === item.id
                            ? 'bg-primary text-white'
                            : 'text-gray-300 hover:bg-gray-700 hover:text-white hover:bg-primary/20'
                        }`}
                        dir={isRTL ? 'rtl' : 'ltr'}
                      >
                        {isRTL ? (
                          <>
                            <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                            <div className="w-6 h-6 ml-3 flex items-center justify-center">
                              <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                {item.icon}
                              </span>
                            </div>
                            {item.badge && (
                              <span className="mr-auto bg-gray-700 text-xs px-2 py-1 rounded-full">
                                {item.badge}
                              </span>
                            )}
                          </>
                        ) : (
                          <>
                            <div className="w-6 h-6 mr-3 flex items-center justify-center">
                              <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                {item.icon}
                              </span>
                            </div>
                            <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                            {item.badge && (
                              <span className="ml-auto bg-gray-700 text-xs px-2 py-1 rounded-full">
                                {item.badge}
                              </span>
                            )}
                          </>
                        )}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </nav>
          </aside>

          {/* Mobile Sidebar - Overlay */}
          {isMobileMenuOpen && (
            <motion.div
              initial={isRTL ? { x: 300 } : { x: -300 }}
              animate={{ x: 0 }}
              exit={isRTL ? { x: 300 } : { x: -300 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 z-50 md:hidden"
            >
              <div
                className="absolute inset-0 bg-black bg-opacity-50"
                onClick={() => setIsMobileMenuOpen(false)}
              ></div>
              <div className={`absolute top-0 ${isRTL ? 'right-0' : 'left-0'} bottom-0 w-72 bg-gray-800 ${isRTL ? 'border-l' : 'border-r'} border-gray-700`}>
                <nav className="p-4">
                  <ul className="space-y-3">
                    {menuItems.map((item) => (
                      <li key={item.id}>
                        {item.id === 'move-crypto' ? (
                          <div>
                            <button
                              onClick={() => setIsMoveCryptoOpen(!isMoveCryptoOpen)}
                              className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                                activeItem === item.id
                                  ? 'bg-primary text-white'
                                  : 'text-gray-300 hover:bg-gray-700 hover:text-white hover:bg-primary/20'
                              }`}
                              dir={isRTL ? 'rtl' : 'ltr'}
                            >
                              <div className="flex items-center">
                                {isRTL ? (
                                  <>
                                    <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                                    <div className="w-6 h-6 ml-3 flex items-center justify-center">
                                      <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                        {item.icon}
                                      </span>
                                    </div>
                                  </>
                                ) : (
                                  <>
                                    <div className="w-6 h-6 mr-3 flex items-center justify-center">
                                      <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                        {item.icon}
                                      </span>
                                    </div>
                                    <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                                  </>
                                )}
                              </div>
                              <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                {isMoveCryptoOpen ? 'expand_less' : 'expand_more'}
                              </span>
                              <span className="sr-only">
                                {isMoveCryptoOpen ? t('common.collapse') : t('common.expand')}
                              </span>
                            </button>

                            {/* Submenu */}
                            {isMoveCryptoOpen && (
                              <ul className={`mt-2 ${isRTL ? 'mr-6' : 'ml-6'} space-y-2`}>
                                {moveCryptoSubmenu.map((subItem) => (
                                  <li key={subItem.id}>
                                    <Link
                                      href={subItem.path}
                                      className={`flex items-center p-2 rounded-lg transition-colors ${
                                        activeSubItem === subItem.id
                                          ? 'bg-primary/30 text-white'
                                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                                      }`}
                                      onClick={() => setIsMobileMenuOpen(false)}
                                    >
                                      <div className="flex-1 min-w-0">
                                        <span className="text-sm font-medium">{t(`app.${subItem.id}`)}</span>
                                      </div>
                                      <div className={`w-6 h-6 ${isRTL ? 'mr-3' : 'ml-3'} flex items-center justify-center`}>
                                        <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                          {subItem.icon}
                                        </span>
                                      </div>
                                    </Link>
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ) : (
                          <Link
                            href={item.path}
                            className={`flex items-center p-3 rounded-lg transition-colors ${
                              activeItem === item.id
                                ? 'bg-primary text-white'
                                : 'text-gray-300 hover:bg-gray-700 hover:text-white hover:bg-primary/20'
                            }`}
                            onClick={() => setIsMobileMenuOpen(false)}
                            dir={isRTL ? 'rtl' : 'ltr'}
                          >
                            {isRTL ? (
                              <>
                                <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                                <div className="w-6 h-6 ml-3 flex items-center justify-center">
                                  <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                    {item.icon}
                                  </span>
                                </div>
                                {item.badge && (
                                  <span className="mr-auto bg-gray-700 text-xs px-2 py-1 rounded-full">
                                    {item.badge}
                                  </span>
                                )}
                              </>
                            ) : (
                              <>
                                <div className="w-6 h-6 mr-3 flex items-center justify-center">
                                  <span className="material-symbols-outlined text-sm" aria-hidden="true">
                                    {item.icon}
                                  </span>
                                </div>
                                <span className="text-sm font-medium">{t(`app.${item.id}`)}</span>
                                {item.badge && (
                                  <span className="ml-auto bg-gray-700 text-xs px-2 py-1 rounded-full">
                                    {item.badge}
                                  </span>
                                )}
                              </>
                            )}
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </motion.div>
          )}

          {/* Main Content */}
          <main className="flex-1 overflow-auto h-[calc(100vh-64px)]">
            {children}
          </main>
        </div>
      </div>
    </WalletBalanceProvider>
  );
}
