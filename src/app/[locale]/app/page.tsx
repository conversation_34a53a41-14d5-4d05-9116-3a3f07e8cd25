'use client';

import { motion } from 'framer-motion';
import dynamic from 'next/dynamic';
import { useLanguage } from '@/context/LanguageContext';

// Lazy load ConnectWallet component to improve performance
const ConnectWallet = dynamic(() => import('@/components/ConnectWallet'), {
  ssr: false, // Don't render on server
  loading: () => (
    <div className="flex items-center justify-center min-w-[160px] h-[48px]">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    </div>
  )
});

export default function AppPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-4 md:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full text-center mx-auto"
        dir={isRTL ? 'rtl' : 'ltr'}
      >
        <h1 className={`text-2xl md:text-3xl font-bold text-white mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('app.getStarted')}
        </h1>
        <p className={`text-gray-300 mb-8 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('app.description')}
        </p>
        <div className="flex flex-row gap-4 justify-center mx-auto" dir="ltr">
          <button className={`btn-outline-medium min-w-[160px] ${isRTL ? 'font-tajawal' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
            {t('app.watchAddress')}
          </button>
          <div dir={isRTL ? 'rtl' : 'ltr'}>
            <ConnectWallet />
          </div>
        </div>
      </motion.div>
    </div>
  );
}
