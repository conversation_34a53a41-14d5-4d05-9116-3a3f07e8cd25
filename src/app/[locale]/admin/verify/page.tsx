'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import Image from 'next/image';
import Link from 'next/link';

export default function AdminVerify() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const verifyMagicLink = async () => {
      try {
        // Get the token from URL params
        const token_hash = searchParams.get('token_hash');
        const type = searchParams.get('type');

        if (!token_hash || type !== 'magiclink') {
          throw new Error('Invalid or missing token');
        }

        // Verify the magic link token
        const { data, error } = await supabase.auth.verifyOtp({
          token_hash,
          type: 'magiclink'
        });

        if (error) throw error;

        if (data.user) {
          // Check if user is admin
          const { data: userRecord } = await supabase
            .from('users')
            .select('is_admin')
            .eq('email', data.user.email)
            .single();

          if (userRecord?.is_admin) {
            setStatus('success');
            setMessage('Successfully authenticated! Redirecting to admin dashboard...');
            
            // Redirect to admin dashboard after 2 seconds
            setTimeout(() => {
              router.push('/en/admin/dashboard');
            }, 2000);
          } else {
            throw new Error('Not authorized as admin');
          }
        } else {
          throw new Error('Authentication failed');
        }

      } catch (error: any) {
        console.error('Verification error:', error);
        setStatus('error');
        setMessage(error.message || 'Authentication failed');
        
        // Redirect to login with error after 3 seconds
        setTimeout(() => {
          router.push('/en/admin/login?error=verification_failed');
        }, 3000);
      }
    };

    verifyMagicLink();
  }, [searchParams, router]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container-custom py-4">
        <Link href="/en" className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Mokhba Logo"
            width={80}
            height={80}
            className="mr-2"
            unoptimized
          />
        </Link>
      </div>

      <div className="flex-grow flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            {status === 'loading' && (
              <>
                <div className="w-16 h-16 bg-gradient-to-r from-primary to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Verifying...</h1>
                <p className="text-gray-600">Please wait while we verify your magic link</p>
              </>
            )}

            {status === 'success' && (
              <>
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="material-symbols-outlined text-white text-2xl">check_circle</span>
                </div>
                <h1 className="text-2xl font-bold text-green-900 mb-2">Success!</h1>
                <p className="text-green-600">{message}</p>
              </>
            )}

            {status === 'error' && (
              <>
                <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="material-symbols-outlined text-white text-2xl">error</span>
                </div>
                <h1 className="text-2xl font-bold text-red-900 mb-2">Authentication Failed</h1>
                <p className="text-red-600 mb-4">{message}</p>
                <Link 
                  href="/en/admin/login"
                  className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <span className="material-symbols-outlined mr-2">arrow_back</span>
                  Back to Login
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 