'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useLanguage } from '@/context/LanguageContext';

export default function AdminLogin() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t, isRTL } = useLanguage();

  useEffect(() => {
    const errorParam = searchParams.get('error');
    if (errorParam) {
      switch (errorParam) {
        case 'invalid_token':
          setError('Invalid or malformed token');
          break;
        case 'expired_token':
          setError('Magic link has expired. Please request a new one.');
          break;
        case 'unauthorized':
          setError('You are not authorized to access the admin area.');
          break;
        case 'session_error':
          setError('Failed to create session. Please try again.');
          break;
        case 'verification_failed':
          setError('Token verification failed. Please try again.');
          break;
        default:
          setError('An error occurred during authentication.');
      }
    }
  }, [searchParams]);

  const handleMagicLink = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      const response = await fetch('/api/auth/magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(data.message);
        setEmail(''); // Clear email field
      } else {
        setError(data.error || 'Failed to send magic link');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100" dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="container-custom py-4">
        <Link href="/en" className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Mokhba Logo"
            width={80}
            height={80}
            className="mr-2"
            unoptimized
          />
        </Link>
      </div>

      <div className="flex-grow flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-r from-primary to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="material-symbols-outlined text-white text-2xl">admin_panel_settings</span>
              </div>
              <h1 className={`text-2xl font-bold text-gray-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                Admin Access
              </h1>
              <p className={`text-gray-600 ${isRTL ? 'font-tajawal' : ''}`}>
                Enter your admin email to receive a secure magic link
              </p>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg">
                <div className="flex items-center">
                  <span className="material-symbols-outlined text-red-500 mr-2">error</span>
                  {error}
                </div>
              </div>
            )}

            {success && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-lg">
                <div className="flex items-center">
                  <span className="material-symbols-outlined text-green-500 mr-2">check_circle</span>
                  {success}
                </div>
                <p className="mt-2 text-sm text-green-600">
                  Check your email and click the magic link to access the admin dashboard.
                </p>
              </div>
            )}

            <form onSubmit={handleMagicLink} className="space-y-6">
              <div>
                <label htmlFor="admin-email" className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  Admin Email Address
                </label>
                <input
                  id="admin-email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="<EMAIL>"
                  disabled={loading}
                />
              </div>

              <button
                type="submit"
                disabled={loading || !email.trim()}
                className="w-full bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Sending Magic Link...
                  </>
                ) : (
                  <>
                    <span className="material-symbols-outlined mr-2">send</span>
                    Send Magic Link
                  </>
                )}
              </button>
            </form>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-center text-sm text-gray-500">
                Magic links expire in 10 minutes for security.
              </p>
              <p className="text-center text-xs text-gray-400 mt-2">
                Limited to 3 requests per hour per email.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 