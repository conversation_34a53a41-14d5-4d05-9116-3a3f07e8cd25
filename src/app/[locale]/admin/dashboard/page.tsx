'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import { useLanguage } from '@/context/LanguageContext';

interface SupportTicket {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  wallet_address?: string;
  created_at: string;
  updated_at: string;
}

interface CardWaitlist {
  id: string;
  full_name: string;
  email: string;
  phone_number: string;
  country: string;
  interest?: string;
  card_type?: string;
  wallet_address?: string;
  status: string;
  created_at: string;
}

interface FeatureRequest {
  id: string;
  name: string;
  email: string;
  phone?: string;
  category: string;
  title: string;
  description: string;
  wallet_address?: string;
  status: string;
  priority: string;
  created_at: string;
}

interface StatusSubscription {
  id: string;
  email: string;
  wallet_address?: string;
  is_active: boolean;
  created_at: string;
}

interface DashboardStats {
  totalSupportTickets: number;
  openTickets: number;
  cardWaitlistCount: number;
  featureRequestsCount: number;
  activeSubscriptions: number;
  recentActivity: number;
}

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'tickets' | 'waitlist' | 'features' | 'subscriptions'>('overview');
  
  // Data states
  const [stats, setStats] = useState<DashboardStats>({
    totalSupportTickets: 0,
    openTickets: 0,
    cardWaitlistCount: 0,
    featureRequestsCount: 0,
    activeSubscriptions: 0,
    recentActivity: 0
  });
  
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [cardWaitlist, setCardWaitlist] = useState<CardWaitlist[]>([]);
  const [featureRequests, setFeatureRequests] = useState<FeatureRequest[]>([]);
  const [statusSubscriptions, setStatusSubscriptions] = useState<StatusSubscription[]>([]);
  
  const [dataLoading, setDataLoading] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [selectedFeatureRequest, setSelectedFeatureRequest] = useState<FeatureRequest | null>(null);
  
  const router = useRouter();
  const { t, isRTL } = useLanguage();

  useEffect(() => {
    const checkAdminAccess = async () => {
      try {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        
        if (!currentUser) {
          router.push('/en/admin/login');
          return;
        }

        const { data: userProfile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('email', currentUser.email)
          .single();

        if (!userProfile || !userProfile.is_admin) {
          await supabase.auth.signOut();
          router.push('/en/admin/login?error=unauthorized');
          return;
        }

        setUser(currentUser);
        setIsAdmin(true);
        await fetchAllData();
      } catch (error) {
        console.error('Admin check error:', error);
        router.push('/en/admin/login?error=verification_failed');
      } finally {
        setLoading(false);
      }
    };

    checkAdminAccess();
  }, [router]);

  const fetchAllData = async () => {
    setDataLoading(true);
    try {
      // Get current session for authenticated API calls
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        console.error('No valid session found');
        router.push('/en/admin/login');
        return;
      }

      const authHeaders = {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      };

      // Fetch all data in parallel with authentication
      const [ticketsRes, waitlistRes, featuresRes, subscriptionsRes] = await Promise.all([
        fetch('/api/support-tickets?limit=100', { headers: authHeaders }),
        fetch('/api/card-waitlist?limit=100', { headers: authHeaders }),
        fetch('/api/feature-requests?limit=100', { headers: authHeaders }),
        fetch('/api/status-subscriptions?limit=100', { headers: authHeaders })
      ]);

      const [ticketsData, waitlistData, featuresData, subscriptionsData] = await Promise.all([
        ticketsRes.json(),
        waitlistRes.json(),
        featuresRes.json(),
        subscriptionsRes.json()
      ]);

      // Set data
      setSupportTickets(ticketsData.support_tickets || []);
      setCardWaitlist(waitlistData.card_waitlist || []);
      setFeatureRequests(featuresData.feature_requests || []);
      setStatusSubscriptions(subscriptionsData.status_subscriptions || []);

      // Calculate stats
      const tickets = ticketsData.support_tickets || [];
      const waitlist = waitlistData.card_waitlist || [];
      const features = featuresData.feature_requests || [];
      const subscriptions = subscriptionsData.status_subscriptions || [];

      setStats({
        totalSupportTickets: tickets.length,
        openTickets: tickets.filter((t: SupportTicket) => t.status === 'open').length,
        cardWaitlistCount: waitlist.length,
        featureRequestsCount: features.length,
        activeSubscriptions: subscriptions.filter((s: StatusSubscription) => s.is_active).length,
        recentActivity: tickets.filter((t: SupportTicket) => 
          new Date(t.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
        ).length
      });

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setDataLoading(false);
    }
  };

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/en/admin/login');
  };

  const updateTicketStatus = async (ticketId: string, status: string) => {
    try {
      const response = await fetch(`/api/support-tickets?id=${ticketId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        await fetchAllData(); // Refresh data
        setSelectedTicket(null);
      }
    } catch (error) {
      console.error('Error updating ticket:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'resolved': return 'bg-green-100 text-green-800 border-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="relative w-16 h-16 mx-auto mb-4">
            <div className="absolute inset-0 border-4 border-primary/30 rounded-full"></div>
            <div className="absolute inset-0 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-gray-600 font-medium">Verifying admin access...</p>
        </motion.div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <Link href="/en" className="flex items-center group">
            <motion.div whileHover={{ scale: 1.05 }}>
              <Image
                src="/logo.svg"
                alt="Mokhba Logo"
                width={50}
                height={50}
                className="mr-3"
                unoptimized
              />
            </motion.div>
            <div>
              <span className="text-xl font-bold text-gray-900">Admin Panel</span>
              <p className="text-sm text-gray-500">Mokhba Dashboard</p>
            </div>
          </Link>

          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">{user?.email}</p>
              <p className="text-xs text-gray-500">Administrator</p>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleSignOut}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              Sign Out
            </motion.button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: 'dashboard' },
              { id: 'tickets', label: 'Support Tickets', icon: 'support_agent', count: stats.openTickets },
              { id: 'waitlist', label: 'Card Waitlist', icon: 'credit_card', count: stats.cardWaitlistCount },
              { id: 'features', label: 'Feature Requests', icon: 'lightbulb', count: stats.featureRequestsCount },
              { id: 'subscriptions', label: 'Subscriptions', icon: 'notifications', count: stats.activeSubscriptions }
            ].map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                whileHover={{ y: -2 }}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="material-symbols-outlined text-lg">{tab.icon}</span>
                <span>{tab.label}</span>
                {tab.count !== undefined && tab.count > 0 && (
                  <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                )}
              </motion.button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-8"
            >
              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    title: 'Total Support Tickets',
                    value: stats.totalSupportTickets,
                    icon: 'support_agent',
                    color: 'from-blue-500 to-blue-600',
                    change: `${stats.openTickets} open`
                  },
                  {
                    title: 'Card Waitlist',
                    value: stats.cardWaitlistCount,
                    icon: 'credit_card',
                    color: 'from-green-500 to-green-600',
                    change: 'Total signups'
                  },
                  {
                    title: 'Feature Requests',
                    value: stats.featureRequestsCount,
                    icon: 'lightbulb',
                    color: 'from-purple-500 to-purple-600',
                    change: 'Ideas submitted'
                  },
                  {
                    title: 'Active Subscriptions',
                    value: stats.activeSubscriptions,
                    icon: 'notifications',
                    color: 'from-orange-500 to-orange-600',
                    change: 'Status updates'
                  }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-md p-6 border border-gray-200"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center`}>
                        <span className="material-symbols-outlined text-white text-xl">{stat.icon}</span>
                      </div>
                      {dataLoading && (
                        <div className="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
                      )}
                    </div>
                    <h3 className="text-sm font-medium text-gray-600 mb-1">{stat.title}</h3>
                    <p className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</p>
                    <p className="text-sm text-gray-500">{stat.change}</p>
                  </motion.div>
                ))}
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-xl shadow-md border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-bold text-gray-900">Recent Activity</h2>
                  <p className="text-gray-600">Latest submissions and updates</p>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {supportTickets.slice(0, 5).map((ticket) => (
                      <div key={ticket.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="material-symbols-outlined text-blue-600">support_agent</span>
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{ticket.subject}</p>
                          <p className="text-sm text-gray-600">by {ticket.name} • {formatDate(ticket.created_at)}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(ticket.status)}`}>
                          {ticket.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'tickets' && (
            <motion.div
              key="tickets"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Support Tickets</h1>
                  <p className="text-gray-600">Manage customer support requests</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={fetchAllData}
                  className="bg-primary text-white px-4 py-2 rounded-lg font-medium"
                >
                  Refresh
                </motion.button>
              </div>

              <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {supportTickets.map((ticket) => (
                        <tr key={ticket.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div>
                              <p className="font-medium text-gray-900">{ticket.subject}</p>
                              <p className="text-sm text-gray-600 truncate max-w-xs">{ticket.message}</p>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div>
                              <p className="font-medium text-gray-900">{ticket.name}</p>
                              <p className="text-sm text-gray-600">{ticket.email}</p>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(ticket.status)}`}>
                              {ticket.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {formatDate(ticket.created_at)}
                          </td>
                          <td className="px-6 py-4">
                            <button
                              onClick={() => setSelectedTicket(ticket)}
                              className="text-primary hover:text-primary/80 font-medium text-sm"
                            >
                              View
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'waitlist' && (
            <motion.div
              key="waitlist"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Card Waitlist</h1>
                  <p className="text-gray-600">Users waiting for card access</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={fetchAllData}
                  className="bg-primary text-white px-4 py-2 rounded-lg font-medium"
                >
                  Refresh
                </motion.button>
              </div>

              <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interest</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Card Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {cardWaitlist.map((entry) => (
                        <tr key={entry.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <p className="font-medium text-gray-900">{entry.full_name}</p>
                          </td>
                          <td className="px-6 py-4">
                            <div>
                              <p className="text-sm text-gray-900">{entry.email}</p>
                              <p className="text-sm text-gray-600">{entry.phone_number}</p>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            {entry.country}
                          </td>
                          <td className="px-6 py-4">
                            <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                              {entry.interest || 'Not specified'}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                              {entry.card_type || 'Not specified'}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {formatDate(entry.created_at)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'features' && (
            <motion.div
              key="features"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Feature Requests</h1>
                  <p className="text-gray-600">Community ideas and collaboration requests</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={fetchAllData}
                  className="bg-primary text-white px-4 py-2 rounded-lg font-medium"
                >
                  Refresh
                </motion.button>
              </div>

              <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitter</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {featureRequests.map((request) => (
                        <tr key={request.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div>
                              <p className="font-medium text-gray-900">{request.title}</p>
                              <p className="text-sm text-gray-600 truncate max-w-xs">{request.description}</p>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div>
                              <p className="font-medium text-gray-900">{request.name}</p>
                              <p className="text-sm text-gray-600">{request.email}</p>
                              {request.phone && (
                                <p className="text-sm text-gray-600">{request.phone}</p>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                              {request.category}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(request.priority)}`}>
                              {request.priority}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(request.status)}`}>
                              {request.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {formatDate(request.created_at)}
                          </td>
                          <td className="px-6 py-4">
                            <button
                              onClick={() => setSelectedFeatureRequest(request)}
                              className="text-primary hover:text-primary/80 font-medium text-sm"
                            >
                              View
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'subscriptions' && (
            <motion.div
              key="subscriptions"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Status Subscriptions</h1>
                  <p className="text-gray-600">Users subscribed to status updates</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={fetchAllData}
                  className="bg-primary text-white px-4 py-2 rounded-lg font-medium"
                >
                  Refresh
                </motion.button>
              </div>

              <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wallet Address</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribed</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {statusSubscriptions.map((subscription) => (
                        <tr key={subscription.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <p className="font-medium text-gray-900">{subscription.email}</p>
                          </td>
                          <td className="px-6 py-4">
                            <p className="text-sm text-gray-600 font-mono">
                              {subscription.wallet_address || 'Not provided'}
                            </p>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              subscription.is_active 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {subscription.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {formatDate(subscription.created_at)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </main>

      {/* Ticket Detail Modal */}
      <AnimatePresence>
        {selectedTicket && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedTicket(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">{selectedTicket.subject}</h2>
                    <p className="text-gray-600">Ticket #{selectedTicket.id.slice(0, 8)}</p>
                  </div>
                  <button
                    onClick={() => setSelectedTicket(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="material-symbols-outlined">close</span>
                  </button>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Customer</label>
                    <p className="text-gray-900">{selectedTicket.name}</p>
                    <p className="text-sm text-gray-600">{selectedTicket.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Created</label>
                    <p className="text-gray-900">{formatDate(selectedTicket.created_at)}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">Message</label>
                  <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-900 whitespace-pre-wrap">{selectedTicket.message}</p>
                  </div>
                </div>

                {selectedTicket.wallet_address && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Wallet Address</label>
                    <p className="text-gray-900 font-mono text-sm">{selectedTicket.wallet_address}</p>
                  </div>
                )}

                <div className="flex space-x-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Priority</label>
                    <span className={`inline-block mt-1 px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(selectedTicket.priority)}`}>
                      {selectedTicket.priority}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <span className={`inline-block mt-1 px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(selectedTicket.status)}`}>
                      {selectedTicket.status}
                    </span>
                  </div>
                </div>

                <div className="flex space-x-3 pt-4 border-t border-gray-200">
                  {['open', 'in_progress', 'resolved', 'closed'].map((status) => (
                    <button
                      key={status}
                      onClick={() => updateTicketStatus(selectedTicket.id, status)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        selectedTicket.status === status
                          ? 'bg-primary text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      Mark as {status.replace('_', ' ')}
                    </button>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Feature Request Detail Modal */}
      <AnimatePresence>
        {selectedFeatureRequest && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedFeatureRequest(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">{selectedFeatureRequest.title}</h2>
                    <p className="text-gray-600">Request #{selectedFeatureRequest.id.slice(0, 8)}</p>
                  </div>
                  <button
                    onClick={() => setSelectedFeatureRequest(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Submitter</label>
                    <p className="text-gray-900">{selectedFeatureRequest.name}</p>
                    <p className="text-sm text-gray-600">{selectedFeatureRequest.email}</p>
                    {selectedFeatureRequest.phone && (
                      <p className="text-sm text-gray-600">{selectedFeatureRequest.phone}</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Submitted</label>
                    <p className="text-gray-900">{formatDate(selectedFeatureRequest.created_at)}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">Category</label>
                  <span className="inline-block mt-1 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                    {selectedFeatureRequest.category}
                  </span>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">Description</label>
                  <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-900 whitespace-pre-wrap">{selectedFeatureRequest.description}</p>
                  </div>
                </div>

                {selectedFeatureRequest.wallet_address && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Wallet Address</label>
                    <p className="text-gray-900 font-mono text-sm break-all">{selectedFeatureRequest.wallet_address}</p>
                  </div>
                )}

                <div className="flex space-x-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Priority</label>
                    <span className={`inline-block mt-1 px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(selectedFeatureRequest.priority)}`}>
                      {selectedFeatureRequest.priority}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <span className={`inline-block mt-1 px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(selectedFeatureRequest.status)}`}>
                      {selectedFeatureRequest.status}
                    </span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600 mb-2 w-full">Update Status:</div>
                  {['submitted', 'under_review', 'planned', 'in_progress', 'completed', 'rejected'].map((status) => (
                    <button
                      key={status}
                      className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
                        selectedFeatureRequest.status === status
                          ? 'bg-primary text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {status.replace('_', ' ')}
                    </button>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 