import './globals.css';
import type { Metadata } from 'next';
import { Inter, Cairo, Tajawal } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import PageErrorBoundary from '@/components/PageErrorBoundary';
import StructuredData from '@/components/StructuredData';
import { generateStructuredData, SITE_CONFIG } from '@/lib/seo';
import dynamic from 'next/dynamic';

// Dynamically import the client-only wallet provider to prevent SSR issues
const UnifiedWalletProvider = dynamic(
  () => import('@/providers/WalletProvider.client').then(mod => ({ default: mod.UnifiedWalletProvider })),
  { 
    ssr: false,
    loading: () => null
  }
);

// Define fonts
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
});

const cairo = Cairo({
  subsets: ['arabic'],
  display: 'swap',
  variable: '--font-cairo'
});

const tajawal = Tajawal({
  subsets: ['arabic'],
  weight: ['400', '500', '700', '800'],
  display: 'swap',
  variable: '--font-tajawal'
});

export const metadata: Metadata = {
  title: {
    default: SITE_CONFIG.title,
    template: `%s | ${SITE_CONFIG.name}`
  },
  description: SITE_CONFIG.description,
  keywords: SITE_CONFIG.keywords.join(', '),
  authors: [{ name: 'Mokhba Team', url: SITE_CONFIG.url }],
  creator: 'Mokhba',
  publisher: 'Mokhba',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(SITE_CONFIG.url),
  alternates: {
    canonical: SITE_CONFIG.url,
    languages: {
      'en': SITE_CONFIG.url,
      'ar': `${SITE_CONFIG.url}/ar`,
      'x-default': SITE_CONFIG.url
    }
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    alternateLocale: ['ar_SA'],
    url: SITE_CONFIG.url,
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    siteName: SITE_CONFIG.name,
    images: [
      {
        url: '/images/social/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Mokhba - Arabic Cryptocurrency Wallet',
        type: 'image/svg+xml'
      },
      {
        url: '/logo.svg',
        width: 400,
        height: 400,
        alt: 'Mokhba Logo',
        type: 'image/svg+xml'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    site: SITE_CONFIG.twitterHandle,
    creator: SITE_CONFIG.twitterHandle,
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    images: ['/images/social/og-image.svg']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#73AED2'
      }
    ]
  },
  manifest: '/manifest.json',
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    other: {
      'facebook-domain-verification': process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION || '',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default'
    }
  },
  category: 'technology',
  classification: 'Financial Technology',
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-title': SITE_CONFIG.name,
    'application-name': SITE_CONFIG.name,
    'msapplication-TileColor': '#73AED2',
    'theme-color': '#73AED2'
  }
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Generate structured data for the organization and website
  const organizationData = generateStructuredData('organization');
  const websiteData = generateStructuredData('website');
  const applicationData = generateStructuredData('application');

  return (
    <html lang="en" className={`${inter.variable} ${cairo.variable} ${tajawal.variable}`}>
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Material Symbols */}
        <link 
          rel="stylesheet" 
          href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&display=swap" 
        />
        
        {/* Viewport and mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content={SITE_CONFIG.name} />
        <meta name="application-name" content={SITE_CONFIG.name} />
        
        {/* Theme colors */}
        <meta name="theme-color" content="#73AED2" />
        <meta name="msapplication-TileColor" content="#73AED2" />
        
        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        
        {/* Language and locale */}
        <meta httpEquiv="Content-Language" content="en,ar" />
        
        {/* Structured Data */}
        <StructuredData data={organizationData} />
        <StructuredData data={websiteData} />
        <StructuredData data={applicationData} />
      </head>
      <body className="min-h-screen bg-gradient-main">
        <UnifiedWalletProvider>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 5000,
              style: {
                background: '#1F2937',
                color: '#fff',
                border: '1px solid #374151',
              },
            }}
          />
          <PageErrorBoundary>
            {children}
          </PageErrorBoundary>
        </UnifiedWalletProvider>
      </body>
    </html>
  );
}
