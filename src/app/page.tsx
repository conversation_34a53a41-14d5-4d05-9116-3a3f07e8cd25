'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function RootPage() {
  const router = useRouter();

  useEffect(() => {
    // Detect user's language preference
    const detectLanguage = () => {
      // Check if user has a saved preference
      const savedLanguage = localStorage.getItem('preferred-language');
      if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
        return savedLanguage;
      }

      // Check browser language preference
      const browserLanguage = navigator.language.toLowerCase();
      
      // If browser is set to Arabic or any Arabic variant, redirect to Arabic
      if (browserLanguage.startsWith('ar')) {
        return 'ar';
      }
      
      // Default to English for all other languages
      return 'en';
    };

    const preferredLanguage = detectLanguage();
    
    // Save the detected/preferred language
    localStorage.setItem('preferred-language', preferredLanguage);
    
    // Redirect immediately without showing redirect message
    router.replace(`/${preferredLanguage}`);
  }, [router]);

  // Return null to avoid showing any content during redirect
  return null;
}
