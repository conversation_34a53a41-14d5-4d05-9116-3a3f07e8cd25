import { NextRequest, NextResponse } from 'next/server';

/**
 * RPC Proxy API Route
 * Handles blockchain RPC requests server-side to avoid CORS issues
 * 
 * POST /api/rpc
 * Body: { method: string, params: any[], chainId?: number }
 */

// RPC endpoint configurations
const RPC_ENDPOINTS = {
  1: [ // Ethereum Mainnet
    process.env.NEXT_PUBLIC_ALCHEMY_API_KEY ? `https://eth-mainnet.g.alchemy.com/v2/${process.env.NEXT_PUBLIC_ALCHEMY_API_KEY}` : null,
    process.env.NEXT_PUBLIC_INFURA_API_KEY ? `https://mainnet.infura.io/v3/${process.env.NEXT_PUBLIC_INFURA_API_KEY}` : null,
    process.env.NEXT_PUBLIC_ETHEREUM_RPC_URL,
    'https://ethereum.publicnode.com',
    'https://rpc.flashbots.net',
    'https://cloudflare-eth.com',
  ].filter(Boolean),
  137: [ // Polygon
    process.env.NEXT_PUBLIC_ALCHEMY_API_KEY ? `https://polygon-mainnet.g.alchemy.com/v2/${process.env.NEXT_PUBLIC_ALCHEMY_API_KEY}` : null,
    process.env.NEXT_PUBLIC_INFURA_API_KEY ? `https://polygon-mainnet.infura.io/v3/${process.env.NEXT_PUBLIC_INFURA_API_KEY}` : null,
    'https://polygon.llamarpc.com',
    'https://polygon.rpc.blxrbdn.com',
  ].filter(Boolean),
  42161: [ // Arbitrum
    process.env.NEXT_PUBLIC_ALCHEMY_API_KEY ? `https://arb-mainnet.g.alchemy.com/v2/${process.env.NEXT_PUBLIC_ALCHEMY_API_KEY}` : null,
    process.env.NEXT_PUBLIC_INFURA_API_KEY ? `https://arbitrum-mainnet.infura.io/v3/${process.env.NEXT_PUBLIC_INFURA_API_KEY}` : null,
    'https://arbitrum.llamarpc.com',
    'https://arbitrum.rpc.blxrbdn.com',
  ].filter(Boolean),
  10: [ // Optimism
    process.env.NEXT_PUBLIC_ALCHEMY_API_KEY ? `https://opt-mainnet.g.alchemy.com/v2/${process.env.NEXT_PUBLIC_ALCHEMY_API_KEY}` : null,
    process.env.NEXT_PUBLIC_INFURA_API_KEY ? `https://optimism-mainnet.infura.io/v3/${process.env.NEXT_PUBLIC_INFURA_API_KEY}` : null,
    'https://optimism.llamarpc.com',
    'https://optimism.rpc.blxrbdn.com',
  ].filter(Boolean),
  8453: [ // Base
    process.env.NEXT_PUBLIC_ALCHEMY_API_KEY ? `https://base-mainnet.g.alchemy.com/v2/${process.env.NEXT_PUBLIC_ALCHEMY_API_KEY}` : null,
    'https://base.llamarpc.com',
    'https://base.rpc.blxrbdn.com',
  ].filter(Boolean),
};

async function makeRPCRequest(url: string, method: string, params: any[], id: number = 1): Promise<any> {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Mokhba-Wallet/1.0',
    },
    body: JSON.stringify({
      jsonrpc: '2.0',
      method,
      params,
      id,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  
  if (data.error) {
    throw new Error(`RPC Error: ${data.error.message} (Code: ${data.error.code})`);
  }

  return data.result;
}

async function tryRPCEndpoints(chainId: number, method: string, params: any[]): Promise<any> {
  const endpoints = RPC_ENDPOINTS[chainId as keyof typeof RPC_ENDPOINTS] || RPC_ENDPOINTS[1];
  
  if (!endpoints || endpoints.length === 0) {
    throw new Error(`No RPC endpoints configured for chain ID ${chainId}`);
  }

  let lastError: Error | null = null;

  for (const endpoint of endpoints) {
    try {
      console.log(`Trying RPC endpoint: ${endpoint}`);
      const result = await makeRPCRequest(endpoint, method, params);
      console.log(`RPC request successful via: ${endpoint}`);
      return result;
    } catch (error) {
      console.warn(`RPC endpoint failed: ${endpoint}`, error);
      lastError = error as Error;
      continue;
    }
  }

  throw new Error(`All RPC endpoints failed. Last error: ${lastError?.message}`);
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Handle both our custom format and standard JSON-RPC format
    let method, params, chainId, id;

    if (body.jsonrpc) {
      // Standard JSON-RPC format
      method = body.method;
      params = body.params;
      id = body.id || 1;
      chainId = 1; // Default to mainnet, could be extracted from URL params
    } else {
      // Our custom format
      method = body.method;
      params = body.params;
      chainId = body.chainId || 1;
      id = 1;
    }

    // Validate request
    if (!method || !Array.isArray(params)) {
      return NextResponse.json(
        {
          jsonrpc: '2.0',
          error: { code: -32600, message: 'Invalid request. Method and params array required.' },
          id: id || null
        },
        { status: 400 }
      );
    }

    // Security: Only allow specific read-only methods
    const allowedMethods = [
      'eth_getBalance',
      'eth_getTransactionCount',
      'eth_getCode',
      'eth_call',
      'eth_estimateGas',
      'eth_gasPrice',
      'eth_getBlockByNumber',
      'eth_getTransactionByHash',
      'eth_getTransactionReceipt',
      'eth_getLogs',
      'net_version',
      'eth_chainId',
    ];

    if (!allowedMethods.includes(method)) {
      return NextResponse.json(
        {
          jsonrpc: '2.0',
          error: { code: -32601, message: `Method ${method} not allowed` },
          id
        },
        { status: 403 }
      );
    }

    console.log(`RPC Request: ${method} on chain ${chainId} with params:`, params);

    // Make the RPC request
    const result = await tryRPCEndpoints(chainId, method, params);

    return NextResponse.json({
      jsonrpc: '2.0',
      result,
      id,
    });

  } catch (error) {
    console.error('RPC proxy error:', error);

    return NextResponse.json(
      {
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: error instanceof Error ? error.message : 'Internal error',
        },
        id: 1,
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
