import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { withRateLimit, rateLimitConfigs } from '@/lib/rateLimit';
import { verifyCaptcha, isValidCaptchaScore } from '@/lib/captcha';
import { log } from '@/lib/logger';

async function handlePOST(request: NextRequest) {
  try {
    const { email, wallet_address, captchaToken } = await request.json();

    // Verify CAPTCHA (skip in development)
    if (process.env.NODE_ENV !== 'development') {
      if (!captchaToken) {
        log.security('Status subscription rejected: missing CAPTCHA token', {
          endpoint: '/api/status-subscriptions',
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification required' },
          { status: 400 }
        );
      }

      const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                       request.headers.get('x-real-ip');

      const captchaResult = await verifyCaptcha(captchaToken, clientIp || undefined);
      
      if (!captchaResult.success || !isValidCaptchaScore(captchaResult, 0.5)) {
        log.security('Status subscription rejected: CAPTCHA verification failed', {
          endpoint: '/api/status-subscriptions',
          captchaSuccess: captchaResult.success,
          captchaScore: captchaResult.score,
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Insert into database
    console.log('Attempting to insert status subscription:', {
      email, wallet_address
    });
    
    const { error } = await supabase
      .from('status_subscriptions')
      .insert([
        {
          email,
          wallet_address: wallet_address || null,
          is_active: true,
        }
      ]);

    if (error) {
      console.error('Supabase error details:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      console.error('Error details:', error.details);
      
      // Handle duplicate email (unique constraint violation)
      if (error.code === '23505' && error.message.includes('unique_email')) {
        return NextResponse.json(
          { 
            error: "You're already subscribed to status updates!",
            code: error.code
          },
          { status: 409 } // 409 Conflict for duplicates
        );
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to subscribe to status updates',
          details: error.message,
          code: error.code
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Successfully subscribed to status updates' },
      { status: 201 }
    );
  } catch (error) {
    log.error('Error subscribing to status updates', {
      error: error instanceof Error ? error.message : String(error),
      context: 'status_subscriptions'
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to POST requests (form submissions)
export const POST = withRateLimit(rateLimitConfigs.forms, handlePOST);

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Unsubscribe token is required' },
        { status: 400 }
      );
    }

    // Update the subscription to inactive using the unsubscribe token
    const { error } = await supabaseAdmin
      .from('status_subscriptions')
      .update({ is_active: false })
      .eq('unsubscribe_token', token);

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { error: 'Failed to unsubscribe' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Successfully unsubscribed from status updates' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error unsubscribing from status updates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Create authenticated Supabase client
    const token = authHeader.replace('Bearer ', '');
    const authenticatedSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );

    // Verify user is admin
    const { data: { user } } = await authenticatedSupabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Check admin status using authenticated client
    const { data: userProfile } = await authenticatedSupabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!userProfile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const active_only = searchParams.get('active_only') === 'true';

    // Use authenticated client - this will respect RLS
    let query = authenticatedSupabase
      .from('status_subscriptions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (active_only) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch status subscriptions' },
        { status: 500 }
      );
    }

    return NextResponse.json({ status_subscriptions: data });
  } catch (error) {
    console.error('Error fetching status subscriptions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 