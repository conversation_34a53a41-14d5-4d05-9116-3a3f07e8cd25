import { NextRequest, NextResponse } from 'next/server';
import { healthCheck } from '@/lib/startup-validation';

/**
 * Health check endpoint
 * Validates environment configuration and system health
 * 
 * GET /api/health
 * Returns:
 * - 200: System is healthy
 * - 500: System has issues
 */
export async function GET(request: NextRequest) {
  try {
    const health = await healthCheck();
    
    // Return appropriate status code
    const statusCode = health.status === 'healthy' ? 200 : 500;
    
    return NextResponse.json(health, { status: statusCode });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * Detailed health check (for authenticated users)
 * POST /api/health with admin token
 */
export async function POST(request: NextRequest) {
  try {
    // In a real implementation, you might want to verify admin authentication here
    // const authHeader = request.headers.get('authorization');
    
    const health = await healthCheck();
    
    // Add more detailed information for authenticated requests
    const detailedHealth = {
      ...health,
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || 'unknown',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      // Only include basic environment info, never sensitive values
      envChecks: {
        supabaseUrlConfigured: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabaseAnonKeyConfigured: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        supabaseServiceKeyConfigured: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        nodeEnv: process.env.NODE_ENV
      }
    };
    
    const statusCode = health.status === 'healthy' ? 200 : 500;
    
    return NextResponse.json(detailedHealth, { status: statusCode });
  } catch (error) {
    console.error('Detailed health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Detailed health check failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
} 