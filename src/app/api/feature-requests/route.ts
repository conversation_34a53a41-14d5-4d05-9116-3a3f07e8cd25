import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { withRateLimit, rateLimitConfigs } from '@/lib/rateLimit';
import { verifyCaptcha, isValidCaptchaScore } from '@/lib/captcha';
import { log } from '@/lib/logger';

async function handlePOST(request: NextRequest) {
  try {
    const { name, email, phone, category, title, description, wallet_address, captchaToken } = await request.json();

    // Verify CAPTCHA (skip in development)
    if (process.env.NODE_ENV !== 'development') {
      if (!captchaToken) {
        log.security('Feature request rejected: missing CAPTCHA token', {
          endpoint: '/api/feature-requests',
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification required' },
          { status: 400 }
        );
      }

      const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                       request.headers.get('x-real-ip');

      const captchaResult = await verifyCaptcha(captchaToken, clientIp || undefined);
      
      if (!captchaResult.success || !isValidCaptchaScore(captchaResult, 0.5)) {
        log.security('Feature request rejected: CAPTCHA verification failed', {
          endpoint: '/api/feature-requests',
          captchaSuccess: captchaResult.success,
          captchaScore: captchaResult.score,
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    // Validate required fields
    if (!name || !email || !category || !title || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, category, title, description' },
        { status: 400 }
      );
    }

    // Validate category
    const validCategories = ['feature', 'collaboration', 'feedback', 'bug', 'investment', 'other'];
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category. Must be one of: ' + validCategories.join(', ') },
        { status: 400 }
      );
    }

    // Insert into database
    console.log('Attempting to insert feature request:', {
      name, email, phone, category, title, description, wallet_address
    });
    
    const { error } = await supabase
      .from('feature_requests')
      .insert([
        {
          name,
          email,
          phone: phone || null,
          category,
          title,
          description,
          wallet_address: wallet_address || null,
          status: 'submitted',
          priority: 'medium',
        }
      ]);

    if (error) {
      console.error('Supabase error details:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      console.error('Error details:', error.details);
      return NextResponse.json(
        { 
          error: 'Failed to submit feature request',
          details: error.message,
          code: error.code
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Feature request submitted successfully' },
      { status: 201 }
    );
  } catch (error) {
    log.error('Error submitting feature request', {
      error: error instanceof Error ? error.message : String(error),
      context: 'feature_requests'
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to POST requests (form submissions)
export const POST = withRateLimit(rateLimitConfigs.forms, handlePOST);

export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Create authenticated Supabase client
    const token = authHeader.replace('Bearer ', '');
    const authenticatedSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );

    // Verify user is admin
    const { data: { user } } = await authenticatedSupabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Check admin status using authenticated client
    const { data: userProfile } = await authenticatedSupabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!userProfile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Use authenticated client - this will respect RLS
    let query = authenticatedSupabase
      .from('feature_requests')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (category) {
      query = query.eq('category', category);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch feature requests' },
        { status: 500 }
      );
    }

    return NextResponse.json({ feature_requests: data });
  } catch (error) {
    console.error('Error fetching feature requests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 