import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { log } from '@/lib/logger';
import { withRateLimit, rateLimitConfigs } from '@/lib/rateLimit';
import { verifyCaptcha, isValidCaptchaScore } from '@/lib/captcha';

async function handlePOST(request: NextRequest) {
  try {
    const { full_name, email, phone_number, country, interest, card_type, wallet_address, captchaToken } = await request.json();

    // Verify CAPTCHA (skip in development)
    if (process.env.NODE_ENV !== 'development') {
      if (!captchaToken) {
        log.security('Card waitlist request rejected: missing CAPTCHA token', {
          endpoint: '/api/card-waitlist',
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification required' },
          { status: 400 }
        );
      }

      const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                       request.headers.get('x-real-ip');

      const captchaResult = await verifyCaptcha(captchaToken, clientIp || undefined);
      
      if (!captchaResult.success || !isValidCaptchaScore(captchaResult, 0.5)) {
        log.security('Card waitlist request rejected: CAPTCHA verification failed', {
          endpoint: '/api/card-waitlist',
          captchaSuccess: captchaResult.success,
          captchaScore: captchaResult.score,
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    // Validate required fields
    if (!full_name || !email || !phone_number || !country) {
      return NextResponse.json(
        { error: 'Missing required fields: full_name, email, phone_number, country' },
        { status: 400 }
      );
    }

    // Validate interest options
    const validInterests = ['online_purchases', 'travel_spending', 'fiat_withdrawal', 'crypto_cashback', 'defi_access', 'other'];
    if (interest && !validInterests.includes(interest)) {
      return NextResponse.json(
        { error: 'Invalid interest. Must be one of: ' + validInterests.join(', ') },
        { status: 400 }
      );
    }

    // Validate card_type options
    const validCardTypes = ['virtual', 'physical', 'both'];
    if (card_type && !validCardTypes.includes(card_type)) {
      return NextResponse.json(
        { error: 'Invalid card_type. Must be one of: ' + validCardTypes.join(', ') },
        { status: 400 }
      );
    }

    // Insert into database
    log.database('Attempting to insert card waitlist entry', {
      operation: 'insert',
      table: 'card_waitlist',
      fields: ['full_name', 'email', 'phone_number', 'country', 'interest', 'card_type', 'wallet_address']
    });
    
    const { error } = await supabase
      .from('card_waitlist')
      .insert([
        {
          full_name,
          email,
          phone_number,
          country,
          interest: interest || null,
          card_type: card_type || null,
          wallet_address: wallet_address || null,
          status: 'waiting',
        }
      ]);

    if (error) {
      log.error('Database operation failed', {
        operation: 'insert',
        table: 'card_waitlist',
        errorCode: error.code,
        errorMessage: error.message
      });
      
      // Handle duplicate email (unique constraint violation)
      if (error.code === '23505' && error.message.includes('card_waitlist_email_key')) {
        return NextResponse.json(
          { 
            error: "You're already on the waitlist! We'll contact you when the card is available.",
            code: error.code
          },
          { status: 409 } // 409 Conflict for duplicates
        );
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to join card waitlist',
          details: error.message,
          code: error.code
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Successfully joined card waitlist' },
      { status: 201 }
    );
  } catch (error) {
    log.error('Card waitlist operation failed', {
      operation: 'insert',
      error: error instanceof Error ? error.message : String(error)
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to POST requests (form submissions)
export const POST = withRateLimit(rateLimitConfigs.forms, handlePOST);

export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Create authenticated Supabase client
    const token = authHeader.replace('Bearer ', '');
    const authenticatedSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );

    // Verify user is admin
    const { data: { user } } = await authenticatedSupabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Check admin status using authenticated client
    const { data: userProfile } = await authenticatedSupabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!userProfile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Use authenticated client - this will respect RLS
    let query = authenticatedSupabase
      .from('card_waitlist')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      log.error('Failed to fetch card waitlist entries', {
        operation: 'select',
        table: 'card_waitlist',
        errorCode: error.code,
        errorMessage: error.message
      });
      return NextResponse.json(
        { error: 'Failed to fetch card waitlist entries' },
        { status: 500 }
      );
    }

    return NextResponse.json({ card_waitlist: data });
  } catch (error) {
    log.error('Card waitlist fetch operation failed', {
      operation: 'select',
      error: error instanceof Error ? error.message : String(error)
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 