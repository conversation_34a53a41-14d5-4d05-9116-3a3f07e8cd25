import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { withRateLimit, rateLimitConfigs } from '@/lib/rateLimit';
import { verifyCaptcha, isValidCaptchaScore } from '@/lib/captcha';
import { log } from '@/lib/logger';

async function handlePOST(request: NextRequest) {
  try {
    const { email, captchaToken } = await request.json();

    // Verify CAPTCHA (required even in development for auth)
    if (!captchaToken && process.env.NODE_ENV !== 'development') {
      log.security('Magic link request rejected: missing CAPTCHA token', {
        endpoint: '/api/auth/magic-link',
        context: 'captcha_verification'
      });
      return NextResponse.json(
        { error: 'CAPTCHA verification required' },
        { status: 400 }
      );
    }

    if (captchaToken) {
      const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                       request.headers.get('x-real-ip');

      const captchaResult = await verifyCaptcha(captchaToken, clientIp || undefined);
      
      if (!captchaResult.success || !isValidCaptchaScore(captchaResult, 0.7)) {
        log.security('Magic link request rejected: CAPTCHA verification failed', {
          endpoint: '/api/auth/magic-link',
          captchaSuccess: captchaResult.success,
          captchaScore: captchaResult.score,
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Simple magic link generation - exactly like yesterday
    const { data, error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        emailRedirectTo: `${request.nextUrl.origin}/en/admin/dashboard`
      }
    });

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    log.security('Magic link sent successfully', {
      endpoint: '/api/auth/magic-link',
      context: 'authentication'
    });

    return NextResponse.json({ 
      message: 'Magic link sent! Check your email.',
      data: data
    });

  } catch (error) {
    log.error('Magic link error', {
      error: error instanceof Error ? error.message : String(error),
      context: 'authentication'
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply strict rate limiting to authentication endpoints
export const POST = withRateLimit(rateLimitConfigs.auth, handlePOST); 