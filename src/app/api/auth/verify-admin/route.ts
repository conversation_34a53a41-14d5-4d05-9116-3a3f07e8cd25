import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const type = searchParams.get('type');

    if (!token || type !== 'magiclink') {
      return NextResponse.redirect(`${request.nextUrl.origin}/en/admin/login?error=invalid_token`);
    }

    // Verify the magic link token with Supabase
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'magiclink'
    });

    if (error || !data.user) {
      return NextResponse.redirect(`${request.nextUrl.origin}/en/admin/login?error=expired_token`);
    }

    // Check if user is admin
    const { data: userProfile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('email', data.user.email)
      .single();

    if (!userProfile || !userProfile.is_admin) {
      // Sign out the user immediately
      await supabase.auth.signOut();
      return NextResponse.redirect(`${request.nextUrl.origin}/en/admin/login?error=unauthorized`);
    }

    // Create admin session with shorter duration (4 hours)
    const { error: sessionError } = await supabase.auth.setSession({
      access_token: data.session?.access_token || '',
      refresh_token: data.session?.refresh_token || ''
    });

    if (sessionError) {
      return NextResponse.redirect(`${request.nextUrl.origin}/en/admin/login?error=session_error`);
    }

    // Redirect to admin dashboard
    return NextResponse.redirect(`${request.nextUrl.origin}/en/admin/dashboard`);

  } catch (error) {
    console.error('Token verification error:', error);
    return NextResponse.redirect(`${request.nextUrl.origin}/en/admin/login?error=verification_failed`);
  }
} 