import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { withRateLimit, rateLimitConfigs } from '@/lib/rateLimit';
import { verifyCaptcha, isValidCaptchaScore } from '@/lib/captcha';
import { log } from '@/lib/logger';

async function handlePOST(request: NextRequest) {
  try {
    const { name, email, subject, message, wallet_address, captchaToken } = await request.json();

    // Verify CAPTCHA (skip in development)
    if (process.env.NODE_ENV !== 'development') {
      if (!captchaToken) {
        log.security('Support ticket request rejected: missing CAPTCHA token', {
          endpoint: '/api/support-tickets',
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification required' },
          { status: 400 }
        );
      }

      const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                       request.headers.get('x-real-ip');

      const captchaResult = await verifyCaptcha(captchaToken, clientIp || undefined);
      
      if (!captchaResult.success || !isValidCaptchaScore(captchaResult, 0.5)) {
        log.security('Support ticket request rejected: CAPTCHA verification failed', {
          endpoint: '/api/support-tickets',
          captchaSuccess: captchaResult.success,
          captchaScore: captchaResult.score,
          context: 'captcha_verification'
        });
        return NextResponse.json(
          { error: 'CAPTCHA verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, subject, message' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Insert into database
    console.log('Attempting to insert support ticket:', {
      name, email, subject, message, wallet_address
    });
    
    const { data, error } = await supabase
      .from('support_tickets')
      .insert([
        {
          name,
          email,
          subject,
          message,
          priority: 'medium', // Default priority for user-submitted tickets
          wallet_address: wallet_address || null,
          status: 'open',
        }
      ]);

    if (error) {
      console.error('Supabase error details:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      console.error('Error details:', error.details);
      
      return NextResponse.json(
        { 
          error: 'Failed to submit support ticket',
          details: error.message,
          code: error.code
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Support ticket submitted successfully' },
      { status: 201 }
    );
  } catch (error) {
    log.error('Error submitting support ticket', {
      error: error instanceof Error ? error.message : String(error),
      context: 'support_tickets'
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to POST requests (form submissions)
export const POST = withRateLimit(rateLimitConfigs.forms, handlePOST);

export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Create authenticated Supabase client
    const token = authHeader.replace('Bearer ', '');
    const authenticatedSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    );

    // Verify user is admin
    const { data: { user } } = await authenticatedSupabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Check admin status using authenticated client
    const { data: userProfile } = await authenticatedSupabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!userProfile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Use authenticated client - this will respect RLS
    let query = authenticatedSupabase
      .from('support_tickets')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (status) {
      query = query.eq('status', status);
    }

    if (priority) {
      query = query.eq('priority', priority);
    }

    const { data, error } = await query;

    console.log('Support tickets query result:', { data, error, count: data?.length });

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch support tickets', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ support_tickets: data || [] });
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ticketId = searchParams.get('id');
    
    if (!ticketId) {
      return NextResponse.json(
        { error: 'Ticket ID is required' },
        { status: 400 }
      );
    }

    const { status, priority, admin_notes, assigned_to } = await request.json();

    // Validate status if provided
    const validStatuses = ['open', 'in_progress', 'resolved', 'closed'];
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      );
    }

    // Validate priority if provided
    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (priority && !validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: 'Invalid priority. Must be one of: ' + validPriorities.join(', ') },
        { status: 400 }
      );
    }

    // Update ticket
    const updateData: any = { updated_at: new Date().toISOString() };
    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;
    if (admin_notes) updateData.admin_notes = admin_notes;
    if (assigned_to) updateData.assigned_to = assigned_to;

    const { data, error } = await supabaseAdmin
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId)
      .select();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { error: 'Failed to update support ticket' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Support ticket updated successfully',
      ticket: data[0]
    });
  } catch (error) {
    console.error('Error updating support ticket:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 