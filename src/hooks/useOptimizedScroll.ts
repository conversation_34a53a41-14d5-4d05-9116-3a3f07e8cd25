'use client';

import { useRef, useEffect, useCallback } from 'react';

interface ScrollCallbacks {
  onScroll?: (scrollY: number) => void;
  onScrollStart?: () => void;
  onScrollEnd?: () => void;
}

interface UseOptimizedScrollOptions {
  throttleMs?: number;
  debounceMs?: number;
  startThreshold?: number;
  endThreshold?: number;
}

/**
 * Performance-optimized scroll hook that uses requestAnimationFrame and throttling
 * to minimize re-renders and improve scroll performance
 */
export default function useOptimizedScroll(
  callbacks: ScrollCallbacks = {},
  options: UseOptimizedScrollOptions = {}
) {
  const {
    throttleMs = 16, // ~60fps
    debounceMs = 150,
    startThreshold = 50,
    endThreshold = 600
  } = options;

  const scrollYRef = useRef(0);
  const isScrollingRef = useRef(false);
  const rafIdRef = useRef<number>();
  const throttleTimeoutRef = useRef<number>();
  const debounceTimeoutRef = useRef<number>();
  const lastScrollTimeRef = useRef(0);

  // Throttled scroll handler using requestAnimationFrame
  const throttledScrollHandler = useCallback(() => {
    const now = Date.now();
    const currentScrollY = window.scrollY;
    
    // Only process if enough time has passed (throttling)
    if (now - lastScrollTimeRef.current >= throttleMs) {
      scrollYRef.current = currentScrollY;
      lastScrollTimeRef.current = now;
      
      // Trigger scroll start callback if not already scrolling
      if (!isScrollingRef.current && callbacks.onScrollStart) {
        isScrollingRef.current = true;
        callbacks.onScrollStart();
      }
      
      // Call the scroll callback with current position
      if (callbacks.onScroll) {
        callbacks.onScroll(currentScrollY);
      }
      
      // Clear existing debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      // Set up scroll end detection
      debounceTimeoutRef.current = window.setTimeout(() => {
        isScrollingRef.current = false;
        if (callbacks.onScrollEnd) {
          callbacks.onScrollEnd();
        }
      }, debounceMs);
    }
    
    rafIdRef.current = undefined;
  }, [callbacks, throttleMs, debounceMs]);

  // Main scroll event handler
  const handleScroll = useCallback(() => {
    // Only schedule a new RAF if one isn't already pending
    if (!rafIdRef.current) {
      rafIdRef.current = requestAnimationFrame(throttledScrollHandler);
    }
  }, [throttledScrollHandler]);

  useEffect(() => {
    // Set initial scroll position
    scrollYRef.current = window.scrollY;
    
    // Add passive scroll listener for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      
      // Clean up any pending timeouts or RAF
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [handleScroll]);

  // Utility functions for common scroll calculations
  const getScrollProgress = useCallback((start: number, end: number): number => {
    const current = scrollYRef.current;
    if (current <= start) return 0;
    if (current >= end) return 1;
    return (current - start) / (end - start);
  }, []);

  const getScrollValue = useCallback((start: number, end: number, fromValue: number, toValue: number): number => {
    const progress = getScrollProgress(start, end);
    return fromValue + (toValue - fromValue) * progress;
  }, [getScrollProgress]);

  const getEaseOutProgress = useCallback((start: number, end: number): number => {
    const progress = getScrollProgress(start, end);
    return 1 - Math.pow(1 - progress, 3); // Cubic ease-out
  }, [getScrollProgress]);

  const getEaseInOutProgress = useCallback((start: number, end: number): number => {
    const progress = getScrollProgress(start, end);
    return progress < 0.5 
      ? 2 * progress * progress 
      : 1 - Math.pow(-2 * progress + 2, 2) / 2;
  }, [getScrollProgress]);

  return {
    scrollY: scrollYRef.current,
    isScrolling: isScrollingRef.current,
    getScrollProgress,
    getScrollValue,
    getEaseOutProgress,
    getEaseInOutProgress
  };
}

/**
 * Specialized hook for scroll-based animations with built-in easing
 */
export function useScrollAnimation(
  startScroll: number,
  endScroll: number,
  easing: 'linear' | 'ease-out' | 'ease-in-out' = 'ease-out'
) {
  const progressRef = useRef(0);
  const opacityRef = useRef(0);
  const scaleRef = useRef(1);
  const translateYRef = useRef(0);

  const { getScrollProgress, getEaseOutProgress, getEaseInOutProgress } = useOptimizedScroll({
    onScroll: (scrollY) => {
      let progress: number;
      
      switch (easing) {
        case 'ease-out':
          progress = getEaseOutProgress(startScroll, endScroll);
          break;
        case 'ease-in-out':
          progress = getEaseInOutProgress(startScroll, endScroll);
          break;
        default:
          progress = getScrollProgress(startScroll, endScroll);
      }
      
      progressRef.current = progress;
      opacityRef.current = 1 - progress;
      scaleRef.current = 1 - progress * 0.1; // Subtle scale effect
      translateYRef.current = progress * -50; // Move up as it fades
    }
  });

  return {
    progress: progressRef.current,
    opacity: opacityRef.current,
    scale: scaleRef.current,
    translateY: translateYRef.current,
    style: {
      opacity: opacityRef.current,
      transform: `scale(${scaleRef.current}) translateY(${translateYRef.current}px)`,
      willChange: 'opacity, transform'
    }
  };
}

/**
 * Hook for complex multi-stage scroll animations
 */
export function useMultiStageScrollAnimation(stages: Array<{
  start: number;
  end: number;
  properties: {
    opacity?: [number, number];
    scale?: [number, number];
    translateY?: [number, number];
    translateX?: [number, number];
  };
}>) {
  const animationValuesRef = useRef({
    opacity: 1,
    scale: 1,
    translateY: 0,
    translateX: 0
  });

  const { getScrollProgress } = useOptimizedScroll({
    onScroll: (scrollY) => {
      // Find active stage
      const activeStage = stages.find(stage => scrollY >= stage.start && scrollY <= stage.end);
      
      if (activeStage) {
        const progress = getScrollProgress(activeStage.start, activeStage.end);
        
        // Calculate values for each property
        if (activeStage.properties.opacity) {
          const [from, to] = activeStage.properties.opacity;
          animationValuesRef.current.opacity = from + (to - from) * progress;
        }
        
        if (activeStage.properties.scale) {
          const [from, to] = activeStage.properties.scale;
          animationValuesRef.current.scale = from + (to - from) * progress;
        }
        
        if (activeStage.properties.translateY) {
          const [from, to] = activeStage.properties.translateY;
          animationValuesRef.current.translateY = from + (to - from) * progress;
        }
        
        if (activeStage.properties.translateX) {
          const [from, to] = activeStage.properties.translateX;
          animationValuesRef.current.translateX = from + (to - from) * progress;
        }
      }
    }
  });

  return {
    ...animationValuesRef.current,
    style: {
      opacity: animationValuesRef.current.opacity,
      transform: `scale(${animationValuesRef.current.scale}) translate(${animationValuesRef.current.translateX}px, ${animationValuesRef.current.translateY}px)`,
      willChange: 'opacity, transform'
    }
  };
} 