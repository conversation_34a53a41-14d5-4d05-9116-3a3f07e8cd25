'use client';

import { useState, useEffect } from 'react';
import { WagmiProvider } from 'wagmi';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { config } from '@/lib/wagmiConfig';
import dynamic from 'next/dynamic';

// Create a client
const queryClient = new QueryClient();

// Dynamically import SolanaProvider to prevent SSR issues
const SolanaProvider = dynamic(
  () => import('./SolanaProvider').then(mod => ({ default: mod.SolanaProvider })),
  { 
    ssr: false,
    loading: () => null
  }
);

export function UnifiedWalletProvider({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Render children without wallet providers during SSR
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <SolanaProvider>
          {children}
        </SolanaProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
} 