'use client';

import { useState, useEffect } from 'react';
import { useConnect } from 'wagmi';
// Solana wallet imports removed
import { useLanguage } from '@/context/LanguageContext';
import { metaMask } from 'wagmi/connectors';

interface WalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WalletModal({ isOpen, onClose }: WalletModalProps) {
  const { t, isRTL } = useLanguage();
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectingWallet, setConnectingWallet] = useState<string>('');
  const [mounted, setMounted] = useState(false);

  // Ethereum/EVM wallets
  const { connectAsync } = useConnect();

  // Solana wallet hooks removed

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleMetaMaskConnect = async () => {
    setIsConnecting(true);
    setConnectingWallet('metamask');
    try {
      await connectAsync({ connector: metaMask() });
      onClose();
    } catch (error) {
      console.error('MetaMask connection error:', error);
    } finally {
      setIsConnecting(false);
      setConnectingWallet('');
    }
  };



  // Don't render modal on server or if not mounted
  if (!mounted || !isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md border border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className={`text-xl font-bold text-white ${isRTL ? 'font-tajawal' : ''}`}>
            {t('wallet.selectWallet')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <span className="material-symbols-outlined">close</span>
          </button>
        </div>

        {/* Description */}
        <p className={`text-gray-300 mb-6 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
          {t('wallet.selectWalletDescription')}
        </p>

        {/* Wallet Options */}
        <div className="space-y-3">
          {/* MetaMask - Ethereum */}
          <button
            onClick={handleMetaMaskConnect}
            disabled={isConnecting}
            className={`w-full bg-gray-700 hover:bg-gray-600 rounded-xl p-4 transition-colors flex items-center justify-between ${
              isConnecting && connectingWallet === 'metamask' ? 'opacity-70' : ''
            }`}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3 p-1">
                <img
                  src="/MetaMask-icon-fox-with-margins.svg"
                  alt="MetaMask"
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    // Fallback to letter if logo fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.parentElement!.innerHTML = '<span class="text-orange-500 font-bold text-sm">M</span>';
                    target.parentElement!.classList.remove('bg-white', 'p-1');
                    target.parentElement!.classList.add('bg-orange-500');
                  }}
                />
              </div>
              <div className="text-left">
                <div className="text-white font-medium">MetaMask</div>
                <div className="text-gray-400 text-sm">
                  {t('wallet.ethereum')} • ETH, USDC, USDT
                </div>
              </div>
            </div>
            {isConnecting && connectingWallet === 'metamask' ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <span className="material-symbols-outlined text-gray-400">
                arrow_forward
              </span>
            )}
          </button>


        </div>

        {/* Footer */}
        <div className={`mt-6 text-center text-xs text-gray-400 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('wallet.secureConnection')}
        </div>
      </div>
    </div>
  );
} 