import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface CryptoActionCardProps {
  children: ReactNode;
  title?: string;
}

export default function CryptoActionCard({ children, title }: CryptoActionCardProps) {
  return (
    <div className="flex justify-center items-center h-full py-4 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md bg-gray-900 rounded-2xl overflow-hidden shadow-xl border border-gray-800"
        style={{ maxHeight: '90vh', overflowY: 'auto' }}
      >
        {title && (
          <div className="p-4 border-b border-gray-800 sticky top-0 bg-gray-900 z-10">
            <h2 className="text-xl font-bold text-white text-center">{title}</h2>
          </div>
        )}
        <div className="p-5">
          {children}
        </div>
      </motion.div>
    </div>
  );
}
