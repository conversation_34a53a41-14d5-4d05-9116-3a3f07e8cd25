'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';

interface StaticPageLayoutProps {
  children: ReactNode;
  title: string;
  subtitle: string;
  icon?: string; // Material icon name
}

export default function StaticPageLayout({ 
  children, 
  title, 
  subtitle,
  icon
}: StaticPageLayoutProps) {
  const { isRTL } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-main">
      <div className="container-custom py-24 md:py-32">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          {icon && (
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center">
                <span className="material-symbols-outlined text-4xl text-primary">
                  {icon}
                </span>
              </div>
            </div>
          )}
          <h1 className={`text-4xl md:text-5xl font-bold text-blue-950 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
            {title}
          </h1>
          <p className={`text-xl text-blue-900/80 max-w-3xl mx-auto ${isRTL ? 'font-tajawal' : ''}`}>
            {subtitle}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {children}
        </motion.div>
      </div>
    </div>
  );
}
