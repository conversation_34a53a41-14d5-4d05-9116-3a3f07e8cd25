'use client';

import { useEffect, useRef, useState } from 'react';
import ReCAP<PERSON><PERSON> from 'react-google-recaptcha';
import { getCaptchaSiteKey } from '@/lib/captcha';

interface CaptchaWrapperProps {
  onCaptchaChange: (token: string | null) => void;
  onCaptchaError?: (error: any) => void;
  theme?: 'light' | 'dark';
  size?: 'normal' | 'compact' | 'invisible';
  className?: string;
  disabled?: boolean;
}

export default function CaptchaWrapper({
  onCaptchaChange,
  onCaptchaError,
  theme = 'light',
  size = 'normal',
  className = '',
  disabled = false
}: CaptchaWrapperProps) {
  const [siteKey, setSiteKey] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  useEffect(() => {
    // Get the site key from environment
    const key = getCaptchaSiteKey();
    setSiteKey(key);
    setIsLoaded(true);
  }, []);

  const handleCaptchaChange = (token: string | null) => {
    onCaptchaChange(token);
  };

  const handleCaptchaError = () => {
    console.error('CAPTCHA error occurred');
    onCaptchaChange(null);
    if (onCaptchaError) {
      onCaptchaError('CAPTCHA error');
    }
  };

  const handleCaptchaExpired = () => {
    console.warn('CAPTCHA expired');
    onCaptchaChange(null);
  };

  const resetCaptcha = () => {
    if (recaptchaRef.current) {
      recaptchaRef.current.reset();
    }
  };

  // Expose reset function
  useEffect(() => {
    if (recaptchaRef.current) {
      (recaptchaRef.current as any).resetCaptcha = resetCaptcha;
    }
  }, []);

  // Skip CAPTCHA in development if no site key is configured OR using test keys
  const isDevelopment = typeof window !== 'undefined' &&
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
  const isUsingTestKeys = !siteKey || siteKey === '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI';

  if (isDevelopment && isUsingTestKeys) {
    return (
      <div className={`p-3 bg-yellow-50 border border-yellow-200 rounded-md text-sm ${className}`}>
        <p className="text-yellow-800">
          ⚠️ CAPTCHA disabled in development (using test keys)
        </p>
      </div>
    );
  }

  // Show loading state
  if (!isLoaded || !siteKey) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">Loading CAPTCHA...</span>
      </div>
    );
  }

  return (
    <div className={`captcha-wrapper ${className}`}>
      <ReCAPTCHA
        ref={recaptchaRef}
        sitekey={siteKey}
        onChange={handleCaptchaChange}
        onErrored={handleCaptchaError}
        onExpired={handleCaptchaExpired}
        theme={theme}
        size={size}
      />
      
      {/* Helper text */}
      <p className="mt-2 text-xs text-gray-500">
        This site is protected by reCAPTCHA and the Google{' '}
        <a
          href="https://policies.google.com/privacy"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline"
        >
          Privacy Policy
        </a>{' '}
        and{' '}
        <a
          href="https://policies.google.com/terms"
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline"
        >
          Terms of Service
        </a>{' '}
        apply.
      </p>
    </div>
  );
}

// Hook for easier CAPTCHA management in forms
export function useCaptcha() {
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [captchaError, setCaptchaError] = useState<string | null>(null);
  const captchaRef = useRef<ReCAPTCHA | null>(null);

  const handleCaptchaChange = (token: string | null) => {
    setCaptchaToken(token);
    if (token) {
      setCaptchaError(null);
    }
  };

  const handleCaptchaError = (error: Error) => {
    setCaptchaError('CAPTCHA verification failed. Please try again.');
    setCaptchaToken(null);
  };

  const resetCaptcha = () => {
    if (captchaRef.current && 'reset' in captchaRef.current) {
      (captchaRef.current as any).reset();
    }
    setCaptchaToken(null);
    setCaptchaError(null);
  };

  const isCaptchaValid = () => {
    // Skip validation in development if no site key OR using test keys
    const siteKey = getCaptchaSiteKey();

    // Check if we're in development mode and using test keys
    const isDevelopment = typeof window !== 'undefined' &&
      (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
    const isUsingTestKeys = !siteKey || siteKey === '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI';

    // Debug logging
    console.log('isCaptchaValid Debug:', {
      siteKey,
      isDevelopment,
      isUsingTestKeys,
      captchaToken,
      shouldSkip: isDevelopment && isUsingTestKeys,
      result: isDevelopment && isUsingTestKeys ? true : Boolean(captchaToken)
    });

    if (isDevelopment && isUsingTestKeys) {
      return true;
    }

    return Boolean(captchaToken);
  };

  return {
    captchaToken,
    captchaError,
    captchaRef,
    handleCaptchaChange,
    handleCaptchaError,
    resetCaptcha,
    isCaptchaValid
  };
} 