'use client';

import { useState, useEffect } from 'react';
import { useAccount, useBalance, useSendTransaction, useWaitForTransactionReceipt, useEstimateGas, useGasPrice } from 'wagmi';
import { toast } from 'react-hot-toast';
import { useLanguage } from '@/context/LanguageContext';
import dynamic from 'next/dynamic';

// Dynamic import for viem to prevent SSR issues
let parseEther: any;
let formatEther: any;
let isAddress: any;
let getAddress: any;

const initializeViem = async () => {
  if (typeof window !== 'undefined' && !parseEther) {
    const viem = await import('viem');
    parseEther = viem.parseEther;
    formatEther = viem.formatEther;
    isAddress = viem.isAddress;
    getAddress = viem.getAddress;
  }
};

interface SendWalletComponentProps {
  recipientAddress: string;
  amount: string;
  onAddressChange: (address: string) => void;
  onAmountChange: (amount: string) => void;
}

function SendWalletComponent({
  recipientAddress,
  amount,
  onAddressChange,
  onAmountChange
}: SendWalletComponentProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [viemReady, setViemReady] = useState(false);
  const [isAddressValid, setIsAddressValid] = useState(true);
  const [addressError, setAddressError] = useState('');
  const [hasSufficientBalance, setHasSufficientBalance] = useState(true);
  const [balanceError, setBalanceError] = useState('');

  // Wallet hooks
  const account = useAccount();
  const balance = useBalance({
    address: account.address as `0x${string}` | undefined,
    query: {
      enabled: !!account.address && account.isConnected,
      refetchInterval: 10000, // Refetch every 10 seconds
    },
  });
  const { sendTransaction, data: hash } = useSendTransaction();
  const { isLoading: isPending, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  // Debug logging for balance issues
  useEffect(() => {
    if (mounted && viemReady) {
      console.log('Balance Debug:', {
        isConnected: account.isConnected,
        address: account.address,
        chainId: account.chainId,
        balanceData: balance.data,
        balanceError: balance.error,
        balanceIsLoading: balance.isLoading,
        balanceStatus: balance.status,
        errorDetails: balance.error ? {
          name: balance.error.name,
          message: balance.error.message,
          cause: balance.error.cause,
          stack: balance.error.stack?.split('\n').slice(0, 3).join('\n')
        } : null
      });

      // Log RPC configuration
      console.log('RPC Configuration:', {
        alchemyKey: process.env.NEXT_PUBLIC_ALCHEMY_API_KEY ? 'Set' : 'Not set',
        infuraKey: process.env.NEXT_PUBLIC_INFURA_API_KEY ? 'Set' : 'Not set',
        customRPC: process.env.NEXT_PUBLIC_ETHEREUM_RPC_URL || 'Not set',
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL
      });
    }
  }, [account.isConnected, account.address, account.chainId, balance.data, balance.error, balance.isLoading, balance.status, mounted, viemReady]);

  // Gas estimation hooks
  const { data: gasPrice } = useGasPrice();
  const { data: gasEstimate } = useEstimateGas({
    to: recipientAddress && isAddressValid && viemReady && isAddress && isAddress(recipientAddress) 
      ? recipientAddress as `0x${string}` 
      : undefined,
    value: amount && viemReady && parseEther ? parseEther(amount) : undefined,
    account: account.address,
  });

  useEffect(() => {
    setMounted(true);
    initializeViem().then(() => setViemReady(true));
  }, []);

  useEffect(() => {
    if (isSuccess) {
      toast.success(t('send.success.transaction'));
    }
  }, [isSuccess, t]);

  // Validate Ethereum address
  const validateAddress = (address: string) => {
    if (!address) {
      setIsAddressValid(true);
      setAddressError('');
      return;
    }

    if (!viemReady || !isAddress) {
      setIsAddressValid(false);
      setAddressError(t('send.error.invalidAddress'));
      return;
    }

    if (isAddress(address)) {
      setIsAddressValid(true);
      setAddressError('');
    } else {
      setIsAddressValid(false);
      setAddressError(t('send.error.invalidAddress'));
    }
  };

  // Validate address whenever it changes
  useEffect(() => {
    validateAddress(recipientAddress);
  }, [recipientAddress, viemReady]);

  // Calculate gas fee
  const calculateGasFee = () => {
    if (!gasEstimate || !gasPrice || !formatEther || !viemReady) {
      return { fee: '0.001', feeInWei: BigInt(0) };
    }
    
    const feeInWei = gasEstimate * gasPrice;
    const fee = formatEther(feeInWei);
    return { fee, feeInWei };
  };

  const { fee: estimatedFee, feeInWei: estimatedFeeInWei } = calculateGasFee();

  // Validate sufficient balance
  const validateBalance = () => {
    if (!amount || !balance.data || !viemReady || !parseEther) {
      setHasSufficientBalance(true);
      setBalanceError('');
      return;
    }

    try {
      const amountInWei = parseEther(amount);
      const totalRequired = amountInWei + estimatedFeeInWei;
      
      if (totalRequired > balance.data.value) {
        setHasSufficientBalance(false);
        setBalanceError(t('send.error.insufficientBalance'));
      } else {
        setHasSufficientBalance(true);
        setBalanceError('');
      }
    } catch (error) {
      setHasSufficientBalance(false);
      setBalanceError(t('send.error.invalidAmount'));
    }
  };

  // Calculate maximum sendable amount
  const calculateMaxAmount = () => {
    if (!balance.data || !viemReady || !formatEther || !estimatedFeeInWei) {
      return '0';
    }

    const maxSendable = balance.data.value - estimatedFeeInWei;
    if (maxSendable <= 0) {
      return '0';
    }

    return formatEther(maxSendable);
  };

  // Handle MAX button click
  const handleMaxClick = () => {
    const maxAmount = calculateMaxAmount();
    onAmountChange(maxAmount);
  };

  // Validate balance whenever amount or fees change
  useEffect(() => {
    validateBalance();
  }, [amount, estimatedFeeInWei, balance.data]);

  const handleSend = async () => {
    if (!viemReady || !parseEther || !sendTransaction || !isAddress) return;
    
    // Double-check address validity before sending
    if (!isAddress(recipientAddress)) {
      toast.error(t('send.error.invalidAddress'));
      return;
    }
    
    try {
      // Use getAddress to ensure proper checksum formatting
      const checksummedAddress = getAddress(recipientAddress);
      
      await sendTransaction({
        to: checksummedAddress as `0x${string}`,
        value: parseEther(amount),
      });
    } catch (error) {
      console.error('Transaction error:', error);
      toast.error(t('send.error.transaction'));
    }
  };

  if (!mounted || !viemReady) {
    return (
      <div className="animate-pulse">
        <div className="h-6 bg-gray-700 rounded mb-2"></div>
        <div className="h-8 bg-gray-700 rounded mb-4"></div>
        <div className="h-24 bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (!account.isConnected) {
    return (
      <div className="text-center py-16">
        <div className="text-yellow-400 mb-4">
          <span className="material-symbols-outlined text-4xl">wallet</span>
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          {t('send.connectWallet')}
        </h3>
        <p className="text-gray-400">
          {t('send.connectWalletDescription')}
        </p>
      </div>
    );
  }

  return (
    <div className="min-h-[400px] flex flex-col">
      {/* Balance Display */}
      <div className="mb-6">
        <div className="text-gray-400 text-sm mb-1">{t('send.availableBalance')}</div>
        <div className="text-white text-2xl font-bold">
          {balance.isLoading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Loading...
            </div>
          ) : balance.error ? (
            <div className="text-red-400 text-base">
              Error loading balance
              <div className="text-xs text-gray-500 mt-1">
                {balance.error.message || 'Failed to fetch balance'}
              </div>
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-600 mt-2 p-2 bg-gray-800 rounded">
                  <div><strong>Debug Info:</strong></div>
                  <div>Chain ID: {account.chainId}</div>
                  <div>Address: {account.address}</div>
                  <div>Error: {balance.error.name}</div>
                  <div>Cause: {balance.error.cause?.toString()}</div>
                </div>
              )}
            </div>
          ) : balance.data && formatEther ? (
            `${formatEther(balance.data.value)} ${balance.data.symbol}`
          ) : account.isConnected ? (
            <div className="text-yellow-400 text-base">
              Connecting to network...
            </div>
          ) : (
            '0.00 ETH'
          )}
        </div>
        {account.isConnected && account.address && (
          <div className="text-gray-500 text-xs mt-1">
            {account.address.slice(0, 6)}...{account.address.slice(-4)}
          </div>
        )}
      </div>

      {/* Send Form */}
      <div className="space-y-4 flex-1">
        <div>
          <label className="block text-gray-300 text-sm font-medium mb-2">
            {t('send.recipientAddress')}
          </label>
          <input
            type="text"
            value={recipientAddress}
            onChange={(e) => onAddressChange(e.target.value)}
            placeholder="0x..."
            className={`w-full px-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none transition-colors ${
              !isAddressValid && recipientAddress 
                ? 'border-red-500 focus:border-red-500' 
                : 'border-gray-600 focus:border-primary'
            }`}
          />
          {!isAddressValid && recipientAddress && (
            <p className="text-red-400 text-sm mt-1">{addressError}</p>
          )}
        </div>

        <div>
          <label className="block text-gray-300 text-sm font-medium mb-2">
            {t('send.amount')}
          </label>
          <div className="relative">
            <input
              type="text"
              value={amount}
              onChange={(e) => onAmountChange(e.target.value)}
              placeholder="0.00"
              className={`w-full px-4 py-3 pr-16 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none transition-colors ${
                !hasSufficientBalance && amount 
                  ? 'border-red-500 focus:border-red-500' 
                  : 'border-gray-600 focus:border-primary'
              }`}
            />
            <button
              type="button"
              onClick={handleMaxClick}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary hover:bg-primary/80 text-white text-xs px-2 py-1 rounded transition-colors"
            >
              MAX
            </button>
          </div>
          {!hasSufficientBalance && amount && (
            <p className="text-red-400 text-sm mt-1">{balanceError}</p>
          )}
        </div>

        {/* Transaction Details */}
        {(amount || recipientAddress) && (
          <div className="bg-gray-800 rounded-lg p-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Network Fee</span>
              <span className="text-white">
                {gasEstimate && gasPrice ? `~${estimatedFee} ETH` : '~0.001 ETH'}
              </span>
            </div>
            {amount && (
              <div className="flex justify-between text-sm pt-2 border-t border-gray-700">
                <span className="text-gray-400 font-medium">Total</span>
                <span className="text-white font-medium">
                  {gasEstimate && gasPrice 
                    ? `${(parseFloat(amount) + parseFloat(estimatedFee)).toFixed(6)} ETH`
                    : `${(parseFloat(amount || '0') + 0.001).toFixed(6)} ETH`
                  }
                </span>
              </div>
            )}
          </div>
        )}

        <button
          onClick={handleSend}
          disabled={isPending || !recipientAddress || !amount || !isAddressValid || !hasSufficientBalance}
          className="w-full bg-primary hover:bg-primary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          {isPending ? t('send.sending') : t('send.sendTransaction')}
        </button>
      </div>
    </div>
  );
}

// Export as dynamic component with SSR disabled
export default dynamic(() => Promise.resolve(SendWalletComponent), {
  ssr: false,
  loading: () => (
    <div className="animate-pulse min-h-[400px]">
      <div className="h-6 bg-gray-700 rounded mb-2"></div>
      <div className="h-8 bg-gray-700 rounded mb-4"></div>
      <div className="h-24 bg-gray-700 rounded"></div>
    </div>
  ),
}); 