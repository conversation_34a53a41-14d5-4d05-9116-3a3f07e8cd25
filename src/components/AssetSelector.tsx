'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';
import { CryptoAsset } from '@/types';
import { getAssetLogoUrl, formatAssetBalance, formatUSDValue } from '@/lib/assets';

interface AssetSelectorProps {
  assets: CryptoAsset[];
  selectedAsset: CryptoAsset | null;
  onAssetSelect: (asset: CryptoAsset) => void;
  onAddAsset?: () => void;
  isOpen: boolean;
  onClose: () => void;
  loading?: boolean;
}

export default function AssetSelector({
  assets,
  selectedAsset,
  onAssetSelect,
  onAddAsset,
  isOpen,
  onClose,
  loading = false
}: AssetSelectorProps) {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAssets, setFilteredAssets] = useState<CryptoAsset[]>(assets);

  // Filter assets based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAssets(assets);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredAssets(
        assets.filter(asset =>
          asset.symbol.toLowerCase().includes(query) ||
          asset.name.toLowerCase().includes(query)
        )
      );
    }
  }, [searchQuery, assets]);

  // Reset search when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
    }
  }, [isOpen]);

  const handleAssetSelect = (asset: CryptoAsset) => {
    onAssetSelect(asset);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end sm:items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 100, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 100, scale: 0.95 }}
        className="bg-gray-800 rounded-t-2xl sm:rounded-2xl w-full max-w-md max-h-[80vh] border border-gray-700 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-bold text-white">
            {t('transfer.selectAsset')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <span className="material-symbols-outlined">close</span>
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-gray-700">
          <div className="relative">
            <span className="material-symbols-outlined absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
              search
            </span>
            <input
              type="text"
              placeholder={t('transfer.searchAssets')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-700 text-white pl-10 pr-4 py-2 rounded-lg border border-gray-600 focus:border-primary focus:outline-none text-sm"
            />
          </div>
        </div>

        {/* Asset List */}
        <div className="flex-1 overflow-y-auto max-h-96">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredAssets.length === 0 ? (
            <div className="text-center py-8">
              <span className="material-symbols-outlined text-gray-400 text-4xl mb-2">
                search_off
              </span>
              <p className="text-gray-400 text-sm">
                {searchQuery ? t('transfer.noAssetsFound') : t('transfer.noAssets')}
              </p>
            </div>
          ) : (
            <div className="p-2">
              {filteredAssets.map((asset) => (
                <motion.button
                  key={`${asset.symbol}-${asset.address || 'native'}`}
                  onClick={() => handleAssetSelect(asset)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors mb-1 ${
                    selectedAsset?.symbol === asset.symbol
                      ? 'bg-primary/20 border border-primary/30'
                      : 'hover:bg-gray-700'
                  }`}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <div className="flex items-center">
                    {/* Asset Logo */}
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gray-600 flex items-center justify-center">
                      <img
                        src={getAssetLogoUrl(asset)}
                        alt={asset.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling!.classList.remove('hidden');
                        }}
                      />
                      <span className="hidden text-white font-bold text-sm">
                        {asset.symbol.charAt(0)}
                      </span>
                    </div>

                    {/* Asset Info */}
                    <div className="text-left">
                      <div className="text-white font-medium text-sm">
                        {asset.symbol}
                      </div>
                      <div className="text-gray-400 text-xs">
                        {asset.name}
                      </div>
                    </div>
                  </div>

                  {/* Balance */}
                  <div className="text-right">
                    <div className="text-white text-sm font-medium">
                      {formatAssetBalance(asset.balance, asset.decimals)}
                    </div>
                    {asset.usdValue && (
                      <div className="text-gray-400 text-xs">
                        {formatUSDValue(asset.usdValue)}
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          )}
        </div>

        {/* Add Asset Button */}
        {onAddAsset && (
          <div className="p-4 border-t border-gray-700">
            <button
              onClick={() => {
                onAddAsset();
                onClose();
              }}
              className="w-full flex items-center justify-center py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              <span className="material-symbols-outlined mr-2 text-sm">add</span>
              {t('transfer.addAsset')}
            </button>
          </div>
        )}
      </motion.div>
    </div>
  );
}

// Asset Display Component (for showing selected asset)
interface AssetDisplayProps {
  asset: CryptoAsset | null;
  onClick: () => void;
  placeholder?: string;
  showBalance?: boolean;
}

export function AssetDisplay({ 
  asset, 
  onClick, 
  placeholder = 'Select Asset',
  showBalance = true 
}: AssetDisplayProps) {
  return (
    <button
      onClick={onClick}
      className="w-full flex items-center justify-between bg-gray-800 rounded-xl p-3 hover:bg-gray-750 transition-colors"
    >
      <div className="flex items-center">
        {asset ? (
          <>
            <div className="w-8 h-8 rounded-full overflow-hidden mr-3 bg-gray-600 flex items-center justify-center">
              <img
                src={getAssetLogoUrl(asset)}
                alt={asset.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling!.classList.remove('hidden');
                }}
              />
              <span className="hidden text-white font-bold text-xs">
                {asset.symbol.charAt(0)}
              </span>
            </div>
            <div className="text-left">
              <div className="text-white font-medium text-sm">
                {asset.symbol}
              </div>
              {showBalance && (
                <div className="text-gray-400 text-xs">
                  {formatAssetBalance(asset.balance, asset.decimals)} {asset.symbol}
                </div>
              )}
            </div>
          </>
        ) : (
          <>
            <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center mr-3">
              <span className="material-symbols-outlined text-gray-400 text-sm">
                currency_bitcoin
              </span>
            </div>
            <span className="text-gray-400 text-sm">{placeholder}</span>
          </>
        )}
      </div>
      <span className="material-symbols-outlined text-gray-400 text-sm">
        expand_more
      </span>
    </button>
  );
}
