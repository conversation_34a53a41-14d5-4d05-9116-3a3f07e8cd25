'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';

interface StatusSubscriptionProps {
  isRTL: boolean;
}

export default function StatusSubscription({ isRTL }: StatusSubscriptionProps) {
  const { t } = useLanguage();
  const [email, setEmail] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | 'already_subscribed' | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);
    setErrorMessage('');

    try {
      const response = await fetch('/api/status-subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          wallet_address: walletAddress || null,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setEmail('');
        setWalletAddress('');
        setTimeout(() => {
          setSubmitStatus(null);
        }, 5000);
      } else {
        // Handle duplicate email as info rather than error
        if (response.status === 409) {
          setSubmitStatus('already_subscribed');
          setErrorMessage(result.error || t('status.alreadySubscribed'));
        } else {
          setSubmitStatus('error');
          setErrorMessage(result.error || t('status.subscribeError'));
        }
      }
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage(t('status.networkError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      className="mt-12 bg-primary/5 rounded-xl p-8 border border-primary/10"
    >
      <h2 className={`text-xl font-bold text-blue-950 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
        {t('status.subscribe')}
      </h2>
      <p className={`text-blue-900/70 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
        {t('status.subscribeDescription')}
      </p>

      {submitStatus === 'success' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-500/20 border border-green-500/50 rounded-lg p-4 mb-4"
        >
          <div className="flex items-center space-x-2">
            <span className="material-symbols-outlined text-green-600 text-lg">check_circle</span>
            <p className="text-green-700 text-sm">
              {t('status.subscribeSuccess')}
            </p>
          </div>
        </motion.div>
      )}

      {submitStatus === 'error' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4"
        >
          <div className="flex items-center space-x-2">
            <span className="material-symbols-outlined text-red-600 text-lg">error</span>
            <p className="text-red-700 text-sm">{errorMessage}</p>
          </div>
        </motion.div>
      )}

      {submitStatus === 'already_subscribed' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-4"
        >
          <div className="flex items-center space-x-2">
            <span className="material-symbols-outlined text-blue-600 text-lg">info</span>
            <p className="text-blue-700 text-sm">{errorMessage}</p>
          </div>
        </motion.div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-3">
          <input 
            type="email" 
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={t('status.emailPlaceholder')} 
            required
            className="flex-1 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
          <input 
            type="text" 
            value={walletAddress}
            onChange={(e) => setWalletAddress(e.target.value)}
            placeholder={t('status.walletPlaceholder')} 
            className="flex-1 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
        </div>
        <motion.button
          type="submit"
          disabled={isSubmitting}
          whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
          whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
          className="w-full sm:w-auto btn-primary whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {isSubmitting ? (
            <>
              <span className="animate-spin material-symbols-outlined">progress_activity</span>
              <span>{t('status.subscribing')}</span>
            </>
          ) : (
            <>
              <span className="material-symbols-outlined">notifications</span>
              <span>{t('status.subscribeButton')}</span>
            </>
          )}
        </motion.button>
      </form>
    </motion.div>
  );
} 