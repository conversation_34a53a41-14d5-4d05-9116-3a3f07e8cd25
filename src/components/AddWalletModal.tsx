'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';
import { ChainType, WalletProvider } from '@/types';
import { ManagedWallet, walletManager } from '@/lib/walletManager';
import { getChainInfo } from '@/lib/assets';

// Helper function to get wallet provider logo
const getProviderLogo = (provider: WalletProvider) => {
  switch (provider) {
    case 'metamask':
      return '/MetaMask-icon-fox-with-margins.svg';
    case 'walletconnect':
      return '/walletconnect-seeklogo.svg';
    case 'coinbase':
      return 'https://images.ctfassets.net/q5ulk4bp65r7/3TBS4oVkD1ghowTqVQJlqj/2dfd4ea3b623a7c0d8deb2ff445dee9e/Consumer_Wordmark.svg';
    default:
      return null;
  }
};

// Helper function to get provider fallback colors
const getProviderColors = (provider: WalletProvider) => {
  switch (provider) {
    case 'metamask':
      return 'bg-orange-500';
    case 'walletconnect':
      return 'bg-blue-500';
    case 'coinbase':
      return 'bg-blue-600';
    default:
      return 'bg-gray-500';
  }
};

interface AddWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWalletAdded: (wallet: ManagedWallet) => void;
  preselectedChain?: ChainType;
}

export default function AddWalletModal({
  isOpen,
  onClose,
  onWalletAdded,
  preselectedChain
}: AddWalletModalProps) {
  const { t } = useLanguage();
  
  // Form state
  const [walletName, setWalletName] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [selectedChain, setSelectedChain] = useState<ChainType>(preselectedChain || 'ethereum');
  const [selectedProvider, setSelectedProvider] = useState<WalletProvider>('metamask');
  const [isDefault, setIsDefault] = useState(false);
  
  // Validation state
  const [errors, setErrors] = useState<{
    name?: string;
    address?: string;
  }>({});
  const [isValidating, setIsValidating] = useState(false);

  // Available chains
  const availableChains: ChainType[] = ['ethereum', 'polygon', 'solana', 'bsc'];
  
  // Available providers
  const availableProviders: WalletProvider[] = ['metamask', 'walletconnect', 'coinbase'];

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setWalletName('');
      setWalletAddress('');
      setSelectedChain(preselectedChain || 'ethereum');
      setSelectedProvider('metamask');
      setIsDefault(false);
      setErrors({});
    }
  }, [isOpen, preselectedChain]);

  // Validate wallet address format
  const validateAddress = (address: string, chainType: ChainType): boolean => {
    if (!address.trim()) return false;
    
    switch (chainType) {
      case 'ethereum':
      case 'polygon':
      case 'bsc':
        return /^0x[a-fA-F0-9]{40}$/.test(address);
      case 'solana':
        return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
      default:
        return false;
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    // Validate name
    if (!walletName.trim()) {
      newErrors.name = t('addWallet.error.nameRequired');
    } else if (walletName.trim().length < 2) {
      newErrors.name = t('addWallet.error.nameTooShort');
    }

    // Validate address
    if (!walletAddress.trim()) {
      newErrors.address = t('addWallet.error.addressRequired');
    } else if (!validateAddress(walletAddress.trim(), selectedChain)) {
      newErrors.address = t('addWallet.error.invalidAddress');
    } else {
      // Check if wallet already exists
      const existingWallet = walletManager.getWalletByAddress(walletAddress.trim());
      if (existingWallet) {
        newErrors.address = t('addWallet.error.walletExists');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsValidating(true);

    try {
      // Create new wallet
      const newWallet = walletManager.addWallet({
        name: walletName.trim(),
        address: walletAddress.trim(),
        provider: selectedProvider,
        chainType: selectedChain,
        isConnected: false,
        isDefault
      });

      // Notify parent component
      onWalletAdded(newWallet);
      
      // Close modal
      onClose();
    } catch (error) {
      console.error('Failed to add wallet:', error);
      setErrors({ address: t('addWallet.error.failed') });
    } finally {
      setIsValidating(false);
    }
  };

  // Generate suggested name
  const generateSuggestedName = () => {
    const chainName = getChainInfo(selectedChain).name;
    const providerName = selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1);
    const existingCount = walletManager.getWalletsByChain(selectedChain).length;
    
    return existingCount === 0 
      ? `${providerName} Wallet`
      : `${providerName} Wallet ${existingCount + 1}`;
  };

  // Auto-fill suggested name
  const handleAutoFillName = () => {
    setWalletName(generateSuggestedName());
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-gray-800 rounded-2xl w-full max-w-md border border-gray-700 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white">
            {t('addWallet.title')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <span className="material-symbols-outlined">close</span>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Wallet Name */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              {t('addWallet.name')}
            </label>
            <div className="relative">
              <input
                type="text"
                value={walletName}
                onChange={(e) => setWalletName(e.target.value)}
                placeholder={t('addWallet.namePlaceholder')}
                className={`w-full bg-gray-700 text-white px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary transition-colors ${
                  errors.name ? 'border-red-500' : 'border-gray-600 focus:border-primary'
                }`}
              />
              <button
                type="button"
                onClick={handleAutoFillName}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-primary hover:text-primary/80 text-sm"
              >
                {t('addWallet.autoFill')}
              </button>
            </div>
            {errors.name && (
              <p className="text-red-400 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          {/* Blockchain Network */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              {t('addWallet.network')}
            </label>
            <div className="grid grid-cols-2 gap-2">
              {availableChains.map((chain) => (
                <button
                  key={chain}
                  type="button"
                  onClick={() => setSelectedChain(chain)}
                  className={`flex items-center p-3 rounded-lg border transition-colors ${
                    selectedChain === chain
                      ? 'border-primary bg-primary/10 text-white'
                      : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  <img
                    src={getChainInfo(chain).logoUrl}
                    alt={getChainInfo(chain).name}
                    className="w-5 h-5 mr-2"
                  />
                  <span className="text-sm font-medium">
                    {getChainInfo(chain).name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Wallet Address */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              {t('addWallet.address')}
            </label>
            <textarea
              value={walletAddress}
              onChange={(e) => setWalletAddress(e.target.value)}
              placeholder={
                selectedChain === 'solana' 
                  ? t('addWallet.addressPlaceholderSolana')
                  : t('addWallet.addressPlaceholderEVM')
              }
              rows={3}
              className={`w-full bg-gray-700 text-white px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary transition-colors resize-none ${
                errors.address ? 'border-red-500' : 'border-gray-600 focus:border-primary'
              }`}
            />
            {errors.address && (
              <p className="text-red-400 text-sm mt-1">{errors.address}</p>
            )}
            <p className="text-gray-500 text-xs mt-1">
              {selectedChain === 'solana' 
                ? t('addWallet.addressHintSolana')
                : t('addWallet.addressHintEVM')
              }
            </p>
          </div>

          {/* Wallet Provider */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              {t('addWallet.provider')}
            </label>
            <div className="grid grid-cols-2 gap-2">
              {availableProviders.map((provider) => (
                <button
                  key={provider}
                  type="button"
                  onClick={() => setSelectedProvider(provider)}
                  className={`flex items-center p-3 rounded-lg border transition-colors ${
                    selectedProvider === provider
                      ? 'border-primary bg-primary/10 text-white'
                      : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                    getProviderLogo(provider) ? 'bg-white p-1' : getProviderColors(provider)
                  }`}>
                    {getProviderLogo(provider) ? (
                      <img
                        src={getProviderLogo(provider)!}
                        alt={provider}
                        className="w-full h-full object-contain"
                        onError={(e) => {
                          // Fallback to first letter if logo fails
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `<span class="text-white font-bold text-xs">${provider.charAt(0).toUpperCase()}</span>`;
                        }}
                      />
                    ) : (
                      <span className="text-white font-bold text-xs">
                        {provider.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  <span className="text-sm font-medium">
                    {provider.charAt(0).toUpperCase() + provider.slice(1)}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Set as Default */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="setDefault"
              checked={isDefault}
              onChange={(e) => setIsDefault(e.target.checked)}
              className="w-4 h-4 text-primary bg-gray-700 border-gray-600 rounded focus:ring-primary focus:ring-2"
            />
            <label htmlFor="setDefault" className="ml-2 text-gray-300 text-sm">
              {t('addWallet.setAsDefault')}
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-3 rounded-lg font-medium transition-colors"
            >
              {t('addWallet.cancel')}
            </button>
            <button
              type="submit"
              disabled={isValidating}
              className="flex-1 bg-primary hover:bg-primary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {isValidating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {t('addWallet.adding')}
                </>
              ) : (
                t('addWallet.add')
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
