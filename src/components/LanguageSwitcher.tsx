'use client';

import { useLanguage } from '@/context/LanguageContext';
import { useParams, usePathname, useRouter } from 'next/navigation';

export default function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();
  const params = useParams();
  const pathname = usePathname();
  const router = useRouter();

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'ar' : 'en';
    setLanguage(newLanguage);
    
    // Save the user's language preference
    localStorage.setItem('preferred-language', newLanguage);
    
    // Get current locale and replace it with the new one
    const currentLocale = params.locale as string;
    const newPath = pathname.replace(`/${currentLocale}`, `/${newLanguage}`);
    
    // Navigate to the new path with the updated locale
    router.push(newPath);
  };

  return (
    <button
      onClick={toggleLanguage}
      className="flex items-center text-blue-950 hover:text-primary transition-colors duration-200"
      dir={language === 'ar' ? 'rtl' : 'ltr'}
    >
      {/* Globe Icon */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="currentColor"
        className="w-5 h-5"
        style={{ 
          marginRight: language === 'ar' ? '0' : '8px',
          marginLeft: language === 'ar' ? '8px' : '0'
        }}
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
      </svg>
      <span className="text-sm font-medium">
        {language === 'en' ? 'العربية' : 'English'}
      </span>
    </button>
  );
}
