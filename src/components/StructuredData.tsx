/**
 * StructuredData Component
 * Injects JSON-LD structured data into the page head for better SEO
 */

import Script from 'next/script';

interface StructuredDataProps {
  data: string | object;
}

export default function StructuredData({ data }: StructuredDataProps) {
  const jsonData = typeof data === 'string' ? data : JSON.stringify(data);

  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: jsonData }}
      strategy="beforeInteractive"
    />
  );
}

// Multiple structured data blocks
interface MultipleStructuredDataProps {
  dataBlocks: (string | object)[];
}

export function MultipleStructuredData({ dataBlocks }: MultipleStructuredDataProps) {
  return (
    <>
      {dataBlocks.map((data, index) => (
        <StructuredData key={index} data={data} />
      ))}
    </>
  );
} 