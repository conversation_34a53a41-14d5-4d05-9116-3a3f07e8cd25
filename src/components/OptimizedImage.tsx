/**
 * OptimizedImage Component
 * A wrapper around Next.js Image with performance optimizations and error handling
 */

'use client';

import { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { 
  getOptimizedImageProps, 
  handleImageError, 
  trackImagePerformance,
  OptimizedImageConfig 
} from '@/lib/imageOptimization';

interface OptimizedImageProps {
  config: OptimizedImageConfig;
  fallbackSrc?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
  trackPerformance?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export default function OptimizedImage({
  config,
  fallbackSrc,
  onLoadComplete,
  onImageError,
  trackPerformance = true,
  className,
  style,
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false);
  const [loadStartTime] = useState(() => performance.now());

  // Get optimized props from config
  const optimizedProps = getOptimizedImageProps(config);

  // Handle image load completion
  const handleLoadComplete = () => {
    if (trackPerformance) {
      trackImagePerformance(config.src, loadStartTime);
    }
    onLoadComplete?.();
  };

  // Handle image error
  const handleError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setImageError(true);
    handleImageError(event, fallbackSrc);
    onImageError?.();
  };

  // Merge props with optimized props
  const finalProps: ImageProps = {
    ...optimizedProps,
    src: imageError && fallbackSrc ? fallbackSrc : config.src,
    alt: config.alt,
    onLoad: handleLoadComplete,
    onError: handleError,
    className: className || optimizedProps.className,
    style: style || optimizedProps.style,
  };

  return <Image {...finalProps} />;
}

// Specialized image components for common use cases
export function LogoImage({ 
  src, 
  alt, 
  className,
  onLoadComplete,
  onImageError
}: { 
  src: string; 
  alt: string; 
  className?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
}) {
  return (
    <OptimizedImage
      config={{
        src,
        alt,
        width: 80,
        height: 80,
        quality: 100,
        priority: true,
        placeholder: 'empty',
        loading: 'eager',
        unoptimized: true, // SVG logos don't need optimization
        className: `object-contain ${className || ''}`
      }}
      onLoadComplete={onLoadComplete}
      onImageError={onImageError}
    />
  );
}

export function HeroImage({ 
  src, 
  alt, 
  className,
  onLoadComplete,
  onImageError
}: { 
  src: string; 
  alt: string; 
  className?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
}) {
  return (
    <OptimizedImage
      config={{
        src,
        alt,
        width: 1200,
        height: 630,
        quality: 85,
        priority: true,
        sizes: '100vw',
        className: `object-cover w-full h-full ${className || ''}`
      }}
      onLoadComplete={onLoadComplete}
      onImageError={onImageError}
    />
  );
}

export function CardImage({ 
  src, 
  alt, 
  className,
  onLoadComplete,
  onImageError
}: { 
  src: string; 
  alt: string; 
  className?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
}) {
  return (
    <OptimizedImage
      config={{
        src,
        alt,
        width: 400,
        height: 300,
        quality: 75,
        sizes: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
        className: `object-cover w-full h-full rounded-lg ${className || ''}`
      }}
      onLoadComplete={onLoadComplete}
      onImageError={onImageError}
    />
  );
}

export function CryptoLogo({ 
  src, 
  alt, 
  size = 32,
  className,
  onLoadComplete,
  onImageError
}: { 
  src: string; 
  alt: string; 
  size?: number;
  className?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
}) {
  return (
    <OptimizedImage
      config={{
        src,
        alt,
        width: size,
        height: size,
        quality: 85,
        sizes: `${size}px`,
        className: `object-contain rounded-full ${className || ''}`,
        placeholder: 'blur'
      }}
      fallbackSrc="/logo.svg"
      onLoadComplete={onLoadComplete}
      onImageError={onImageError}
    />
  );
}

export function Avatar({ 
  src, 
  alt, 
  size = 64,
  className,
  onLoadComplete,
  onImageError
}: { 
  src: string; 
  alt: string; 
  size?: number;
  className?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
}) {
  return (
    <OptimizedImage
      config={{
        src,
        alt,
        width: size,
        height: size,
        quality: 75,
        sizes: `${size}px`,
        className: `object-cover rounded-full ${className || ''}`
      }}
      onLoadComplete={onLoadComplete}
      onImageError={onImageError}
    />
  );
}

export function IconImage({ 
  src, 
  alt, 
  size = 24,
  className,
  onLoadComplete,
  onImageError
}: { 
  src: string; 
  alt: string; 
  size?: number;
  className?: string;
  onLoadComplete?: () => void;
  onImageError?: () => void;
}) {
  return (
    <OptimizedImage
      config={{
        src,
        alt,
        width: size,
        height: size,
        quality: 85,
        sizes: `${size}px`,
        className: `object-contain ${className || ''}`
      }}
      onLoadComplete={onLoadComplete}
      onImageError={onImageError}
    />
  );
} 