'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';

interface SupportTicketFormProps {
  isRTL: boolean;
  onClose?: () => void;
}

export default function SupportTicketForm({ isRTL, onClose }: SupportTicketFormProps) {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    wallet_address: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | null>(null);
  const [errorMessage, setErrorMessage] = useState('');



  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);
    setErrorMessage('');

    try {
      const response = await fetch('/api/support-tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          subject: formData.subject,
          message: formData.message,
          wallet_address: formData.wallet_address || null,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
          wallet_address: ''
        });
        setTimeout(() => {
          setSubmitStatus(null);
          if (onClose) onClose();
        }, 3000);
      } else {
        setSubmitStatus('error');
        setErrorMessage(result.error || t('support.ticket.submitError'));
      }
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage(t('support.ticket.networkError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className={`text-2xl font-bold text-blue-950 ${isRTL ? 'font-tajawal' : ''}`}>
              {t('support.ticket.title')}
            </h2>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <span className="material-symbols-outlined">close</span>
              </button>
            )}
          </div>

          <p className={`text-blue-900/70 mb-6 ${isRTL ? 'font-tajawal' : ''}`}>
            {t('support.ticket.description')}
          </p>

          {submitStatus === 'success' && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-green-500/20 border border-green-500/50 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center space-x-2">
                <span className="material-symbols-outlined text-green-600 text-lg">check_circle</span>
                <p className="text-green-700 text-sm">
                  {t('support.ticket.submitSuccess')}
                </p>
              </div>
            </motion.div>
          )}

          {submitStatus === 'error' && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center space-x-2">
                <span className="material-symbols-outlined text-red-600 text-lg">error</span>
                <p className="text-red-700 text-sm">{errorMessage}</p>
              </div>
            </motion.div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name and Email Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('support.ticket.name')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  placeholder={t('support.ticket.namePlaceholder')}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                  {t('support.ticket.email')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  placeholder={t('support.ticket.emailPlaceholder')}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>
            </div>

            {/* Subject */}
            <div>
              <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                {t('support.ticket.subject')} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                required
                placeholder={t('support.ticket.subjectPlaceholder')}
                className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
              />
            </div>

            {/* Wallet Address */}
            <div>
              <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                {t('support.ticket.walletAddress')}
              </label>
              <input
                type="text"
                name="wallet_address"
                value={formData.wallet_address}
                onChange={handleInputChange}
                placeholder={t('support.ticket.walletPlaceholder')}
                className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50"
              />
            </div>

            {/* Message */}
            <div>
              <label className={`block text-sm font-medium text-blue-900 mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
                {t('support.ticket.message')} <span className="text-red-500">*</span>
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                required
                rows={6}
                placeholder={t('support.ticket.messagePlaceholder')}
                className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 resize-vertical"
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              {onClose && (
                <button
                  type="button"
                  onClick={onClose}
                  disabled={isSubmitting}
                  className="btn-outline-medium disabled:opacity-50"
                >
                  {t('support.ticket.cancel')}
                </button>
              )}
              <motion.button
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                className="btn-primary-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <span className="animate-spin material-symbols-outlined">progress_activity</span>
                    <span>{t('support.ticket.submitting')}</span>
                  </>
                ) : (
                  <>
                    <span className="material-symbols-outlined">send</span>
                    <span>{t('support.ticket.submit')}</span>
                  </>
                )}
              </motion.button>
            </div>
          </form>
        </div>
      </motion.div>
    </motion.div>
  );
} 