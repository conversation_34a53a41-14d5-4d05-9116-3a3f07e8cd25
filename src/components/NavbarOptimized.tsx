'use client';

import { useState, useRef, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import LanguageSwitcher from './LanguageSwitcher';
import { useLanguage } from '@/context/LanguageContext';
import useOptimizedScroll from '@/hooks/useOptimizedScroll';

export default function NavbarOptimized() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;
  
  // Use refs for animation values to avoid re-renders
  const opacityRef = useRef(1);
  const translateYRef = useRef(0);
  const navbarRef = useRef<HTMLElement>(null);

  // Optimized scroll handler with memoized calculations
  const updateNavbarStyle = useCallback((scrollY: number) => {
    // Only animate within relevant scroll range for better performance
    if (scrollY <= 600) {
      // Calculate smooth opacity transition (fade out as we scroll down)
      let opacity: number;
      if (scrollY < 100) {
        opacity = 1;
      } else if (scrollY >= 100 && scrollY < 300) {
        // Fade out between 100px and 300px
        opacity = 1 - (scrollY - 100) / 200;
      } else {
        opacity = 0;
      }
      
      // Calculate translateY with easing
      const translateY = scrollY > 100 ? Math.min(-20, -(scrollY - 100) * 0.1) : 0;
      
      // Only update if values changed significantly (reduces layout thrashing)
      if (Math.abs(opacityRef.current - opacity) > 0.01 || 
          Math.abs(translateYRef.current - translateY) > 0.5) {
        
        opacityRef.current = opacity;
        translateYRef.current = translateY;
        
        // Apply styles directly to avoid re-renders
        if (navbarRef.current) {
          navbarRef.current.style.opacity = opacity.toString();
          navbarRef.current.style.transform = `translateY(${translateY}px)`;
        }
      }
    }
  }, []);

  // Use optimized scroll hook
  useOptimizedScroll({
    onScroll: updateNavbarStyle
  }, {
    throttleMs: 16, // 60fps
    debounceMs: 100
  });

  return (
    <nav 
      ref={navbarRef}
      className="py-4 w-full bg-transparent backdrop-blur-sm fixed top-0 z-50" 
      style={{
        opacity: opacityRef.current,
        transform: `translateY(${translateYRef.current}px)`,
        transition: 'backdrop-filter 0.3s ease-out',
        willChange: 'opacity, transform',
        contain: 'layout style paint' // Optimize rendering
      }}
    >
      <div className="container-custom flex justify-between items-center">
        {/* Logo */}
        <div className="flex-1">
          <Link href="/" className="flex items-center">
            <Image
              src="/logo.svg"
              alt="Mokhba Logo"
              width={80}
              height={80}
              className="mr-2"
              unoptimized
              loading="eager"
              priority
            />
          </Link>
        </div>

        {/* Desktop Navigation - Centered */}
        <div className="hidden md:flex items-center justify-center flex-1">
          <div className="flex items-center" style={{ gap: '2rem' }}>
            <Link 
              href={`/${locale}/security`} 
              className="text-blue-950 hover:text-primary transition-colors duration-200 px-2 font-medium"
            >
              {t('nav.security')}
            </Link>
            <Link 
              href={`/${locale}/learn`} 
              className="text-blue-950 hover:text-primary transition-colors duration-200 px-2 font-medium"
            >
              {t('nav.learn')}
            </Link>
            <Link 
              href={`/${locale}/explore`} 
              className="text-blue-950 hover:text-primary transition-colors duration-200 px-2 font-medium"
            >
              {t('nav.explore')}
            </Link>
            <Link 
              href={`/${locale}/support`} 
              className="text-blue-950 hover:text-primary transition-colors duration-200 px-2 font-medium"
            >
              {t('nav.support')}
            </Link>
          </div>
        </div>

        {/* Sign In Button, Language Switcher and Mobile Menu - Right Side */}
        <div className="flex items-center justify-end flex-1">
          <div className="hidden md:flex items-center" style={{ gap: '1rem' }}>
            <LanguageSwitcher />

            <Link href={`/${locale}/app`} className="btn-primary">
              {t('nav.launch')}
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-blue-950 ml-4 hover:text-primary transition-colors duration-200"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-6 h-6"
            >
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div 
          className="md:hidden container-custom mt-4 bg-white/90 backdrop-blur-md rounded-lg shadow-lg p-4"
          style={{
            willChange: 'transform, opacity',
            animation: 'fadeInDown 0.3s ease-out'
          }}
        >
          <div className="flex flex-col" style={{ gap: '1rem' }}>
            <Link
              href={`/${locale}/security`}
              className="text-blue-950 hover:text-primary transition-colors duration-200 py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.security')}
            </Link>
            <Link
              href={`/${locale}/learn`}
              className="text-blue-950 hover:text-primary transition-colors duration-200 py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.learn')}
            </Link>
            <Link
              href={`/${locale}/explore`}
              className="text-blue-950 hover:text-primary transition-colors duration-200 py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.explore')}
            </Link>
            <Link
              href={`/${locale}/support`}
              className="text-blue-950 hover:text-primary transition-colors duration-200 py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.support')}
            </Link>
            <div className="py-2">
              <LanguageSwitcher />
            </div>

            <Link
              href={`/${locale}/app`}
              className="btn-primary text-center"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.launch')}
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
} 