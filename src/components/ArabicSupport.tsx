'use client';

import useScrollAnimation from '@/hooks/useScrollAnimation';
import LanguageSwitcher from './LanguageSwitcher';
import { useLanguage } from '@/context/LanguageContext';

export default function ArabicSupport() {
  const leftContentAnimation = useScrollAnimation<HTMLDivElement>();
  const rightContentAnimation = useScrollAnimation<HTMLDivElement>();
  const { t, isRTL } = useLanguage();

  return (
    <section className="section-spacing bg-white/30 backdrop-blur-sm">
      <div className="container-custom">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div
            ref={leftContentAnimation.ref}
            className={`slide-in-left ${leftContentAnimation.isVisible ? 'visible' : ''}`}
          >
            <h2 className="heading-2 mb-6 text-primary">
              {t('arabic.title')}
            </h2>
            <p className="paragraph mb-6 text-gray-700">
              {t('arabic.description')}
            </p>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center">
                <LanguageSwitcher />
                <span className={`${isRTL ? 'mr-3 font-tajawal' : 'ml-3'} text-sm text-gray-600`}>
                  {isRTL ? 'جرب التبديل إلى الإنجليزية →' : '← Try switching to Arabic'}
                </span>
              </div>
              <button className="btn-secondary">
                {t('arabic.learnMore')}
              </button>
            </div>
          </div>

          <div
            ref={rightContentAnimation.ref}
            className={`bg-white p-8 rounded-xl shadow-lg slide-in-right ${rightContentAnimation.isVisible ? 'visible' : ''}`}
            dir={isRTL ? 'rtl' : 'ltr'}
          >
            <h3 className={`text-2xl font-bold mb-4 text-primary ${isRTL ? 'font-tajawal' : ''}`}>
              {isRTL ? 'محفظة مخبأ للعملات الرقمية' : 'Mokhba Cryptocurrency Wallet'}
            </h3>
            <p className={`text-gray-700 mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
              {isRTL
                ? 'أول محفظة عربية للعملات الرقمية تمكن المستخدمين من تخزين وإدارة أصولهم الرقمية بأمان وسهولة، مما يحدث ثورة في المشهد المالي في منطقة الشرق الأوسط وشمال أفريقيا.'
                : 'The first Arabic cryptocurrency wallet that enables users to securely store and easily manage their digital assets, revolutionizing the financial landscape in the MENA region.'
              }
            </p>
            <div className={`flex ${isRTL ? 'justify-start' : 'justify-end'}`}>
              <button className={`bg-primary text-white px-6 py-2 rounded-full font-medium hover:opacity-90 transition shadow-md ${isRTL ? 'font-tajawal' : ''}`}>
                {isRTL ? 'تحميل التطبيق' : 'Download App'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
