'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import LanguageSwitcher from './LanguageSwitcher';
import { useLanguage } from '@/context/LanguageContext';
import { LogoImage } from './OptimizedImage';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const { t, isRTL } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
      // Force a re-render on scroll
      if (window.scrollY <= 600) {
        // Only update for the relevant scroll range
        setScrollY(window.scrollY);
      }
    };

    // Initial scroll position
    setScrollY(window.scrollY);

    // Add event listener with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Calculate opacity based on scroll position
  const calculateOpacity = (start: number, end: number, value: number) => {
    if (value <= start) return 1;
    if (value >= end) return 0;
    return 1 - (value - start) / (end - start);
  };

  // Calculate transform based on scroll position
  const calculateTranslateY = (start: number, end: number, distance: number, value: number) => {
    if (value <= start) return 0;
    if (value >= end) return distance;
    return (distance * (value - start)) / (end - start);
  };

  // Helper function to calculate opacity with smooth transitions in both directions
  const calculateSmoothOpacity = (startFadeIn: number, fullyVisible: number, startFadeOut: number, fullyHidden: number, currentScroll: number) => {
    if (currentScroll < startFadeIn) return 0; // Before fade-in starts
    if (currentScroll >= startFadeIn && currentScroll < fullyVisible) {
      // Fading in
      return (currentScroll - startFadeIn) / (fullyVisible - startFadeIn);
    }
    if (currentScroll >= fullyVisible && currentScroll < startFadeOut) {
      // Fully visible
      return 1;
    }
    if (currentScroll >= startFadeOut && currentScroll < fullyHidden) {
      // Fading out
      return 1 - (currentScroll - startFadeOut) / (fullyHidden - startFadeOut);
    }
    return 0; // After fully hidden
  };

  // Navbar animation values - using reverse smooth opacity for bidirectional animations
  const navbarOpacity = scrollY < 600 ? 1 - calculateSmoothOpacity(0, 300, 300, 600, scrollY) : 0;
  const navbarTranslateY = calculateTranslateY(0, 300, -20, scrollY);

  return (
    <nav className="py-4 w-full bg-transparent backdrop-blur-sm" style={{
      opacity: navbarOpacity,
      transform: `translateY(${navbarTranslateY}px)`,
      transition: 'opacity 0.3s ease-out, transform 0.3s ease-out',
      willChange: 'opacity, transform',
      position: 'relative'
    }}>
      <div className="container-custom flex justify-between items-center">
        {/* Logo */}
        <div className="flex-1">
          <Link href="/" className="flex items-center">
            <LogoImage
              src="/logo.svg"
              alt="Mokhba Logo"
              className="mr-2"
            />
          </Link>
        </div>

        {/* Desktop Navigation - Centered */}
        <div className="hidden md:flex items-center justify-center flex-1">
          <div className="flex items-center" style={{ gap: '2rem' }}>
            <Link href={`/${locale}/security`} className="text-blue-950 hover:text-primary transition px-2 font-medium">
              {t('nav.security')}
            </Link>
            <Link href={`/${locale}/learn`} className="text-blue-950 hover:text-primary transition px-2 font-medium">
              {t('nav.learn')}
            </Link>
            <Link href={`/${locale}/explore`} className="text-blue-950 hover:text-primary transition px-2 font-medium">
              {t('nav.explore')}
            </Link>
            <Link href={`/${locale}/support`} className="text-blue-950 hover:text-primary transition px-2 font-medium">
              {t('nav.support')}
            </Link>
          </div>
        </div>

        {/* Sign In Button, Language Switcher and Mobile Menu - Right Side */}
        <div className="flex items-center justify-end flex-1">
          <div className="hidden md:flex items-center" style={{ gap: '1rem' }}>
            <LanguageSwitcher />

            <Link href={`/${locale}/app`} className="btn-primary">
              {t('nav.launch')}
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-blue-950 ml-4 hover:text-primary transition"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-6 h-6"
            >
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden container-custom mt-4 bg-white/90 backdrop-blur-md rounded-lg shadow-lg p-4">
          <div className="flex flex-col" style={{ gap: '1rem' }}>
            <Link
              href={`/${locale}/security`}
              className="text-blue-950 hover:text-primary transition py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.security')}
            </Link>
            <Link
              href={`/${locale}/learn`}
              className="text-blue-950 hover:text-primary transition py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.learn')}
            </Link>
            <Link
              href={`/${locale}/explore`}
              className="text-blue-950 hover:text-primary transition py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.explore')}
            </Link>
            <Link
              href={`/${locale}/support`}
              className="text-blue-950 hover:text-primary transition py-1 font-medium"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.support')}
            </Link>
            <div className="py-2">
              <LanguageSwitcher />
            </div>

            <Link
              href={`/${locale}/app`}
              className="btn-primary text-center"
              onClick={() => setIsMenuOpen(false)}
            >
              {t('nav.launch')}
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
}
