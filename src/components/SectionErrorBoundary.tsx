'use client';

import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { errorHandler, ErrorType, ErrorSeverity } from '@/lib/errorHandler';

interface SectionErrorBoundaryProps {
  children: React.ReactNode;
  sectionName?: string;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface SectionErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorId?: string;
}

class SectionErrorBoundary extends React.Component<SectionErrorBoundaryProps, SectionErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: SectionErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): Partial<SectionErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Handle error through centralized error handler
    const handledError = errorHandler.handleError(
      error,
      ErrorType.UI,
      ErrorSeverity.MEDIUM,
      {
        component: 'SectionErrorBoundary',
        action: 'section_render',
        metadata: {
          sectionName: this.props.sectionName,
          componentStack: errorInfo.componentStack,
          retryCount: this.retryCount
        }
      },
      false // Don't show toast notification as we have our own UI
    );

    this.setState({ errorId: handledError.id });

    // Call custom error handler if provided
    if (this.props.onError) {
      try {
        this.props.onError(error, errorInfo);
      } catch (handlerError) {
        console.error('Section error boundary onError handler failed:', handlerError);
      }
    }
  }

  retry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({ hasError: false, error: undefined, errorId: undefined });
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <SectionErrorFallback 
          error={this.state.error}
          errorId={this.state.errorId}
          sectionName={this.props.sectionName}
          canRetry={this.retryCount < this.maxRetries}
          onRetry={this.retry}
        />
      );
    }

    return this.props.children;
  }
}

interface SectionErrorFallbackProps {
  error?: Error;
  errorId?: string;
  sectionName?: string;
  canRetry?: boolean;
  onRetry?: () => void;
}

function SectionErrorFallback({ 
  error, 
  errorId, 
  sectionName,
  canRetry = false, 
  onRetry 
}: SectionErrorFallbackProps) {
  const { t, isRTL } = useLanguage();

  return (
    <div className="min-h-[300px] flex items-center justify-center bg-gray-800/50 rounded-lg text-white m-4 p-6" dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="text-center max-w-md">
        <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="material-symbols-outlined text-2xl text-red-400">error</span>
        </div>
        
        <h3 className={`text-lg font-bold mb-2 ${isRTL ? 'font-tajawal' : ''}`}>
          {sectionName ? `${sectionName} Error` : (t('error.section.title') || 'Section Error')}
        </h3>
        
        <p className={`text-gray-300 mb-4 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
          {sectionName 
            ? `The ${sectionName} section encountered an error. Other parts of the page should still work.`
            : (t('error.section.description') || 'This section encountered an error. Other parts of the page may still work.')
          }
        </p>

        {process.env.NODE_ENV === 'development' && (
          <details className="mb-4 text-left bg-gray-900 p-3 rounded text-xs">
            <summary className="cursor-pointer text-yellow-400 mb-2">Debug Info</summary>
            <div className="space-y-2 text-gray-300">
              {errorId && <div><strong>Error ID:</strong> {errorId}</div>}
              {sectionName && <div><strong>Section:</strong> {sectionName}</div>}
              {error?.name && <div><strong>Error Type:</strong> {error.name}</div>}
              {error?.message && <div><strong>Message:</strong> {error.message}</div>}
              {error?.stack && (
                <div>
                  <strong>Stack:</strong>
                  <pre className="mt-1 text-xs overflow-auto bg-gray-800 p-2 rounded max-h-32">
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
          </details>
        )}

        <div className="flex gap-2 justify-center">
          {canRetry && onRetry && (
            <button
              onClick={onRetry}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors text-sm"
            >
              {t('error.retry') || 'Try Again'}
            </button>
          )}
          
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded transition-colors text-sm"
          >
            {t('error.refresh') || 'Refresh'}
          </button>
        </div>

        {errorId && (
          <p className="mt-3 text-xs text-gray-500">
            ID: {errorId}
          </p>
        )}
      </div>
    </div>
  );
}

export default SectionErrorBoundary; 