'use client';

import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { log } from '@/lib/logger';

export interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  isolate?: boolean; // If true, only affects this component tree
  level?: 'page' | 'section' | 'component';
  context?: string; // Additional context for debugging
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Generate unique error ID for tracking
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return { 
      hasError: true, 
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const enhancedErrorInfo: ErrorInfo = {
      componentStack: errorInfo.componentStack || '',
      errorBoundary: this.constructor.name
    };

    const errorId = this.state.errorId || `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.setState({ errorInfo: enhancedErrorInfo, errorId });

    // Log error with context using our secure logger
    log.error('React Error Boundary caught error', {
      errorId,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      componentStack: errorInfo.componentStack,
      level: this.props.level || 'component',
      context: this.props.context || 'unknown',
      retryCount: this.retryCount,
      props: this.sanitizeProps(this.props),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'server'
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      try {
        this.props.onError(error, enhancedErrorInfo);
      } catch (handlerError) {
        log.error('Error boundary onError handler failed', {
          originalErrorId: errorId,
          handlerError: handlerError instanceof Error ? handlerError.message : String(handlerError)
        });
      }
    }

    // Report to external error tracking service (if configured)
    this.reportToExternalService(error, enhancedErrorInfo, errorId);
  }

  private sanitizeProps(props: ErrorBoundaryProps) {
    // Remove potentially sensitive data from props for logging
    const { children, onError, ...safeProps } = props;
    return {
      ...safeProps,
      hasChildren: !!children,
      hasOnError: !!onError
    };
  }

  private reportToExternalService(error: Error, errorInfo: ErrorInfo, errorId: string) {
    // Placeholder for external error reporting service
    // This could integrate with Sentry, LogRocket, Bugsnag, etc.
    if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
      try {
        // Example: window.errorReportingService?.captureException(error, { extra: errorInfo });
        log.info('Error reported to external service', {
          errorId,
          service: 'placeholder'
        });
      } catch (reportingError) {
        log.error('Failed to report error to external service', {
          errorId,
          reportingError: reportingError instanceof Error ? reportingError.message : String(reportingError)
        });
      }
    }
  }

  retry = () => {
    const errorId = this.state.errorId || 'unknown';
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      log.info('Error boundary retry attempted', {
        errorId,
        retryCount: this.retryCount,
        maxRetries: this.maxRetries
      });
      this.setState({ hasError: false, error: undefined, errorInfo: undefined, errorId: undefined });
    } else {
      log.warn('Error boundary max retries exceeded', {
        errorId,
        retryCount: this.retryCount,
        maxRetries: this.maxRetries
      });
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorFallback 
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          errorId={this.state.errorId}
          canRetry={this.retryCount < this.maxRetries}
          onRetry={this.retry}
          level={this.props.level}
          context={this.props.context}
        />
      );
    }

    return this.props.children;
  }
}

// Enhanced error fallback component
interface ErrorFallbackProps {
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
  canRetry?: boolean;
  onRetry?: () => void;
  level?: 'page' | 'section' | 'component';
  context?: string;
}

function ErrorFallback({ 
  error, 
  errorInfo, 
  errorId, 
  canRetry = false, 
  onRetry, 
  level = 'component',
  context 
}: ErrorFallbackProps) {
  const { t, isRTL } = useLanguage();

  const getErrorTitle = () => {
    switch (level) {
      case 'page':
        return t('error.page.title') || 'Page Error';
      case 'section':
        return t('error.section.title') || 'Section Error';
      default:
        return t('error.component.title') || 'Component Error';
    }
  };

  const getErrorDescription = () => {
    switch (level) {
      case 'page':
        return t('error.page.description') || 'The page encountered an error and could not be displayed.';
      case 'section':
        return t('error.section.description') || 'This section encountered an error. Other parts of the page may still work.';
      default:
        return t('error.component.description') || 'A component error occurred. This may not affect the rest of the page.';
    }
  };

  const containerClass = level === 'page' 
    ? "min-h-screen flex items-center justify-center bg-gray-900 text-white"
    : level === 'section'
    ? "min-h-[400px] flex items-center justify-center bg-gray-800/50 rounded-lg text-white m-4"
    : "min-h-[200px] flex items-center justify-center bg-gray-700/30 rounded text-white p-4";

  return (
    <div className={containerClass} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="text-center max-w-md">
        <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="material-symbols-outlined text-3xl text-red-400">error</span>
        </div>
        
        <h2 className={`text-xl font-bold mb-3 ${isRTL ? 'font-tajawal' : ''}`}>
          {getErrorTitle()}
        </h2>
        
        <p className={`text-gray-300 mb-4 text-sm ${isRTL ? 'font-tajawal' : ''}`}>
          {getErrorDescription()}
        </p>

        {process.env.NODE_ENV === 'development' && (
          <details className="mb-4 text-left bg-gray-800 p-3 rounded text-xs">
            <summary className="cursor-pointer text-yellow-400 mb-2">Debug Info</summary>
            <div className="space-y-2 text-gray-300">
              {errorId && <div><strong>Error ID:</strong> {errorId}</div>}
              {context && <div><strong>Context:</strong> {context}</div>}
              {error?.name && <div><strong>Error Type:</strong> {error.name}</div>}
              {error?.message && <div><strong>Message:</strong> {error.message}</div>}
              {error?.stack && (
                <div>
                  <strong>Stack:</strong>
                  <pre className="mt-1 text-xs overflow-auto bg-gray-900 p-2 rounded">
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
          </details>
        )}

        <div className="flex gap-3 justify-center">
          {canRetry && onRetry && (
            <button
              onClick={onRetry}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors text-sm"
            >
              {t('error.retry') || 'Try Again'}
            </button>
          )}
          
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded transition-colors text-sm"
          >
            {t('error.refresh') || 'Refresh Page'}
          </button>
        </div>

        {errorId && (
          <p className="mt-4 text-xs text-gray-500">
            Error ID: {errorId}
          </p>
        )}
      </div>
    </div>
  );
}

export default ErrorBoundary; 