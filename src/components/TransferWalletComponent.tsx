'use client';

import { useState, useEffect } from 'react';
import { useAccount, useBalance, useSendTransaction, useWaitForTransactionReceipt } from 'wagmi';
import { useLanguage } from '@/context/LanguageContext';
import { CryptoAsset, ChainType } from '@/types';
import { ManagedWallet, walletManager } from '@/lib/walletManager';
import { createAsset, getAssetsForChain, formatAssetBalance } from '@/lib/assets';
import { feeEstimationService, FeeOptions, FeeEstimate } from '@/lib/feeEstimation';
import AssetSelector, { AssetDisplay } from './AssetSelector';
import WalletSelector, { WalletDisplay } from './WalletSelector';
import dynamic from 'next/dynamic';

// Dynamic import for viem to prevent SSR issues
let parseEther: any;
let formatEther: any;
let isAddress: any;
let getAddress: any;

const initializeViem = async () => {
  if (typeof window !== 'undefined' && !parseEther) {
    const viem = await import('viem');
    parseEther = viem.parseEther;
    formatEther = viem.formatEther;
    isAddress = viem.isAddress;
    getAddress = viem.getAddress;
  }
};

export default function TransferWalletComponent() {
  const { t } = useLanguage();

  // State for client-side rendering
  const [mounted, setMounted] = useState(false);
  const [viemReady, setViemReady] = useState(false);

  // Transfer state
  const [amount, setAmount] = useState('');
  const [selectedAsset, setSelectedAsset] = useState<CryptoAsset | null>(null);
  const [fromWallet, setFromWallet] = useState<ManagedWallet | null>(null);
  const [toWallet, setToWallet] = useState<ManagedWallet | null>(null);
  const [feeOptions, setFeeOptions] = useState<FeeOptions | null>(null);
  const [selectedFeeSpeed, setSelectedFeeSpeed] = useState<'slow' | 'standard' | 'fast'>('standard');
  const [isLoadingFees, setIsLoadingFees] = useState(false);

  // Modal states
  const [showAssetSelector, setShowAssetSelector] = useState(false);
  const [showFromWalletSelector, setShowFromWalletSelector] = useState(false);
  const [showToWalletSelector, setShowToWalletSelector] = useState(false);

  // Validation states
  const [isValidAmount, setIsValidAmount] = useState(true);
  const [hasSufficientBalance, setHasSufficientBalance] = useState(true);
  const [transferError, setTransferError] = useState('');

  // Wallet state with error handling
  const [walletState, setWalletState] = useState({
    address: null as string | null,
    isConnected: false,
    error: null as string | null
  });

  // Available assets and wallets
  const [availableAssets, setAvailableAssets] = useState<CryptoAsset[]>([]);
  const [walletGroups, setWalletGroups] = useState(walletManager.getWalletsGrouped());

  // Safely use wagmi hooks
  let accountResult = { address: null as string | null | undefined, isConnected: false, chainId: undefined };

  try {
    accountResult = useAccount();
  } catch (error) {
    console.warn('Wagmi hooks not available:', error);
  }

  // Balance hook for selected asset
  const balance = useBalance({
    address: fromWallet?.address as `0x${string}` | undefined,
    token: selectedAsset?.address as `0x${string}` | undefined,
    query: {
      enabled: !!fromWallet?.address && !!selectedAsset && fromWallet.isConnected,
      refetchInterval: 10000,
    },
  });

  // Transaction hooks
  const { sendTransaction, data: hash } = useSendTransaction();
  const { isLoading: isPending, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  // Initialize viem and component
  useEffect(() => {
    setMounted(true);
    initializeViem().then(() => setViemReady(true));
  }, []);

  // Update wallet state when mounted
  useEffect(() => {
    if (mounted) {
      try {
        setWalletState({
          address: accountResult.address || null,
          isConnected: accountResult.isConnected,
          error: null
        });

        // Auto-select connected wallet as "from" wallet
        if (accountResult.address && accountResult.isConnected) {
          const connectedWallet = walletManager.getWalletByAddress(accountResult.address);
          if (connectedWallet) {
            setFromWallet(connectedWallet);
          } else {
            // Import the connected wallet
            const chainType = getChainTypeFromChainId(accountResult.chainId);
            if (chainType) {
              const importedWallet = walletManager.importFromConnection({
                address: accountResult.address,
                provider: 'metamask', // Default assumption
                chainType,
                chainId: accountResult.chainId,
                isConnected: true
              });
              setFromWallet(importedWallet);
              setWalletGroups(walletManager.getWalletsGrouped());
            }
          }
        }
      } catch (error) {
        setWalletState(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : String(error)
        }));
      }
    }
  }, [accountResult.address, accountResult.isConnected, accountResult.chainId, mounted]);

  // Load available assets when from wallet changes
  useEffect(() => {
    if (fromWallet && viemReady) {
      loadAvailableAssets(fromWallet.chainType);
    }
  }, [fromWallet, viemReady]);

  // Update asset balance when balance data changes
  useEffect(() => {
    if (selectedAsset && balance.data) {
      const updatedAsset = {
        ...selectedAsset,
        balance: formatEther ? formatEther(balance.data.value) : '0'
      };
      setSelectedAsset(updatedAsset);
    }
  }, [balance.data, selectedAsset, formatEther]);

  // Load fee estimates when transfer details change
  useEffect(() => {
    if (fromWallet && toWallet && selectedAsset && amount && viemReady) {
      loadFeeEstimates();
    }
  }, [fromWallet, toWallet, selectedAsset, amount, viemReady]);

  // Validate amount and balance
  useEffect(() => {
    validateTransfer();
  }, [amount, selectedAsset, feeOptions, selectedFeeSpeed]);

  // Success notification
  useEffect(() => {
    if (isSuccess) {
      console.log(t('transfer.success.transaction'));
      // Reset form
      setAmount('');
      setToWallet(null);
    }
  }, [isSuccess, t]);

  // Helper functions
  const getChainTypeFromChainId = (chainId?: number): ChainType | null => {
    switch (chainId) {
      case 1: return 'ethereum';
      case 137: return 'polygon';
      case 56: return 'bsc';
      default: return 'ethereum'; // Default fallback
    }
  };

  const loadAvailableAssets = async (chainType: ChainType) => {
    try {
      // Get assets for the chain
      const chainAssets = getAssetsForChain(chainType);

      // Create asset objects with mock balances (in real app, fetch from blockchain)
      const assetsWithBalances = chainAssets.map(asset =>
        createAsset(asset.symbol, '0', 0, asset)
      );

      // Add native asset with actual balance if available
      if (balance.data && !selectedAsset) {
        const nativeAsset = chainAssets.find(a => !a.address);
        if (nativeAsset) {
          const nativeWithBalance = createAsset(
            nativeAsset.symbol,
            formatEther ? formatEther(balance.data.value) : '0',
            0,
            nativeAsset
          );
          setSelectedAsset(nativeWithBalance);
        }
      }

      setAvailableAssets(assetsWithBalances);
    } catch (error) {
      console.error('Failed to load assets:', error);
      setAvailableAssets([]);
    }
  };

  const loadFeeEstimates = async () => {
    if (!fromWallet || !toWallet || !selectedAsset) return;

    setIsLoadingFees(true);
    try {
      const fees = await feeEstimationService.getFeeEstimates(
        fromWallet.chainType,
        toWallet.address,
        parseEther ? parseEther(amount).toString() : '0'
      );
      setFeeOptions(fees);
    } catch (error) {
      console.error('Fee estimation failed:', error);
      setFeeOptions(null);
    } finally {
      setIsLoadingFees(false);
    }
  };

  const validateTransfer = () => {
    setTransferError('');
    setIsValidAmount(true);
    setHasSufficientBalance(true);

    if (!amount || !selectedAsset || !feeOptions) return;

    // Validate amount format
    try {
      const amountNum = parseFloat(amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        setIsValidAmount(false);
        setTransferError(t('transfer.error.invalidAmount'));
        return;
      }
    } catch (error) {
      setIsValidAmount(false);
      setTransferError(t('transfer.error.invalidAmount'));
      return;
    }

    // Check sufficient balance
    if (selectedAsset && parseEther && formatEther) {
      try {
        const amountInWei = parseEther(amount);
        const selectedFee = feeOptions[selectedFeeSpeed];
        const totalRequired = amountInWei + selectedFee.feeInWei;
        const availableBalance = parseEther(selectedAsset.balance);

        if (totalRequired > availableBalance) {
          setHasSufficientBalance(false);
          setTransferError(t('transfer.error.insufficientBalance'));
        }
      } catch (error) {
        setIsValidAmount(false);
        setTransferError(t('transfer.error.invalidAmount'));
      }
    }
  };

  const calculateMaxAmount = (): string => {
    if (!selectedAsset || !feeOptions || !parseEther || !formatEther) return '0';

    try {
      const selectedFee = feeOptions[selectedFeeSpeed];
      const availableBalance = parseEther(selectedAsset.balance);
      const maxSendable = availableBalance - selectedFee.feeInWei;

      if (maxSendable <= 0) return '0';
      return formatEther(maxSendable);
    } catch (error) {
      return '0';
    }
  };

  const handleMaxClick = () => {
    const maxAmount = calculateMaxAmount();
    setAmount(maxAmount);
  };

  const handleWalletAdded = (wallet: ManagedWallet) => {
    // Refresh wallet groups
    setWalletGroups(walletManager.getWalletsGrouped());
  };

  // Don't render anything until mounted
  if (!mounted || !viemReady) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show connection prompt if not connected
  if (!walletState.isConnected) {
    return (
      <div className="text-center py-16">
        <div className="text-yellow-400 mb-4">
          <span className="material-symbols-outlined text-4xl">
            wallet
          </span>
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          {t('transfer.connectWallet')}
        </h3>
        <p className="text-gray-400">
          {t('transfer.connectWalletDescription')}
        </p>
      </div>
    );
  }

  const handleTransfer = async () => {
    if (!fromWallet || !toWallet || !selectedAsset || !amount || !feeOptions || !viemReady) return;

    try {
      if (!isAddress(toWallet.address)) {
        console.error(t('transfer.error.invalidAddress'));
        return;
      }

      const checksummedAddress = getAddress(toWallet.address);
      const selectedFee = feeOptions[selectedFeeSpeed];

      await sendTransaction({
        to: checksummedAddress as `0x${string}`,
        value: parseEther(amount),
        gasPrice: BigInt(selectedFee.gasPrice),
        gas: BigInt(selectedFee.gasLimit),
      });
    } catch (error) {
      console.error('Transfer error:', error);
      console.error(t('transfer.error.transaction'));
    }
  };

  return (
    <>
      <div className="min-h-[400px] flex flex-col">
        {/* Transfer From */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2 text-sm">{t('transfer.from')}</label>
          <WalletDisplay
            wallet={fromWallet}
            onClick={() => setShowFromWalletSelector(true)}
            placeholder={t('transfer.selectFromWallet')}
            variant="from"
          />
        </div>

        {/* Transfer To */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2 text-sm">{t('transfer.to')}</label>
          <WalletDisplay
            wallet={toWallet}
            onClick={() => setShowToWalletSelector(true)}
            placeholder={t('transfer.selectToWallet')}
            variant="to"
          />
          <div className="flex justify-end mt-2">
            <button
              onClick={() => setShowToWalletSelector(true)}
              className="text-primary text-xs hover:text-primary/80 transition-colors"
            >
              {t('transfer.addNewWallet')}
            </button>
          </div>
        </div>

        {/* Asset Selection */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2 text-sm">{t('transfer.asset')}</label>
          <AssetDisplay
            asset={selectedAsset}
            onClick={() => setShowAssetSelector(true)}
            placeholder={t('transfer.selectAsset')}
          />
        </div>

        {/* Amount Input */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2 text-sm">{t('transfer.amount')}</label>
          <div className="bg-gray-800 rounded-xl p-4">
            <div className="flex items-center justify-between mb-2">
              <input
                type="text"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className={`bg-transparent text-white text-2xl font-bold border-none focus:outline-none flex-1 ${
                  !isValidAmount ? 'text-red-400' : ''
                }`}
              />
              <button
                onClick={handleMaxClick}
                className="bg-primary/20 text-primary px-3 py-1 rounded-lg text-sm hover:bg-primary/30 transition-colors"
                disabled={!selectedAsset || !feeOptions}
              >
                {t('transfer.max')}
              </button>
            </div>

            {selectedAsset && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">
                  {t('transfer.balance')}: {formatAssetBalance(selectedAsset.balance, selectedAsset.decimals)} {selectedAsset.symbol}
                </span>
                {selectedAsset.usdValue && (
                  <span className="text-gray-400">
                    ≈ ${(parseFloat(amount || '0') * selectedAsset.usdValue).toFixed(2)}
                  </span>
                )}
              </div>
            )}

            {transferError && (
              <div className="text-red-400 text-sm mt-2 flex items-center">
                <span className="material-symbols-outlined text-xs mr-1">error</span>
                {transferError}
              </div>
            )}
          </div>
        </div>

        {/* Fee Selection */}
        {feeOptions && (
          <div className="mb-6">
            <label className="block text-gray-400 mb-3 text-sm">{t('transfer.networkFee')}</label>
            <div className="space-y-2">
              {Object.entries(feeOptions).map(([speed, fee]) => (
                <button
                  key={speed}
                  onClick={() => setSelectedFeeSpeed(speed as 'slow' | 'standard' | 'fast')}
                  className={`w-full p-3 rounded-lg border transition-colors ${
                    selectedFeeSpeed === speed
                      ? 'border-primary bg-primary/10'
                      : 'border-gray-600 bg-gray-800 hover:bg-gray-700'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div className="text-left">
                      <div className="text-white font-medium text-sm capitalize">
                        {t(`transfer.fee.${speed}`)}
                      </div>
                      <div className="text-gray-400 text-xs">
                        {fee.estimatedTime}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-white text-sm">
                        {fee.totalFee} {fromWallet?.chainType.toUpperCase()}
                      </div>
                      {fee.totalFeeUSD && (
                        <div className="text-gray-400 text-xs">
                          ≈ ${fee.totalFeeUSD.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {isLoadingFees && (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary mr-2"></div>
                <span className="text-gray-400 text-sm">{t('transfer.loadingFees')}</span>
              </div>
            )}
          </div>
        )}

        {/* Transfer Button */}
        <button
          onClick={handleTransfer}
          disabled={
            !fromWallet ||
            !toWallet ||
            !selectedAsset ||
            !amount ||
            !feeOptions ||
            !isValidAmount ||
            !hasSufficientBalance ||
            isPending
          }
          className="w-full bg-primary hover:bg-primary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-xl font-medium transition-colors flex items-center justify-center"
        >
          {isPending ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              {t('transfer.processing')}
            </>
          ) : (
            t('transfer.confirm')
          )}
        </button>
      </div>

      {/* Modals */}
      <AssetSelector
        assets={availableAssets}
        selectedAsset={selectedAsset}
        onAssetSelect={setSelectedAsset}
        isOpen={showAssetSelector}
        onClose={() => setShowAssetSelector(false)}
        loading={balance.isLoading}
      />

      <WalletSelector
        walletGroups={walletGroups}
        selectedWallet={fromWallet}
        onWalletSelect={setFromWallet}
        onWalletAdded={handleWalletAdded}
        isOpen={showFromWalletSelector}
        onClose={() => setShowFromWalletSelector(false)}
        title={t('transfer.selectFromWallet')}
      />

      <WalletSelector
        walletGroups={walletGroups}
        selectedWallet={toWallet}
        onWalletSelect={setToWallet}
        onWalletAdded={handleWalletAdded}
        isOpen={showToWalletSelector}
        onClose={() => setShowToWalletSelector(false)}
        title={t('transfer.selectToWallet')}
        excludeWallet={fromWallet}
      />
    </>
  );
}