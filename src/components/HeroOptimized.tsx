'use client';

import { useRef, useCallback } from 'react';
import Image from 'next/image';
import { useLanguage } from '@/context/LanguageContext';
import { useMultiStageScrollAnimation } from '@/hooks/useOptimizedScroll';

export default function HeroOptimized() {
  const { t, isRTL } = useLanguage();
  const containerRef = useRef<HTMLDivElement>(null);
  const heroContentRef = useRef<HTMLDivElement>(null);
  const iPhoneWalletRef = useRef<HTMLDivElement>(null);

  // Define animation stages for better performance
  const heroAnimation = useMultiStageScrollAnimation([
    {
      start: 0,
      end: 600,
      properties: {
        opacity: [1, 0],
        scale: [1, 0.9],
        translateY: [0, -50]
      }
    }
  ]);

  const iPhoneAnimation = useMultiStageScrollAnimation([
    {
      start: 300,
      end: 500,
      properties: {
        opacity: [0, 1],
        scale: [0.9, 1]
      }
    },
    {
      start: 1200,
      end: 1600,
      properties: {
        opacity: [1, 0],
        scale: [1, 0.95]
      }
    }
  ]);

  const arabicSupportAnimation = useMultiStageScrollAnimation([
    {
      start: 1400,
      end: 1600,
      properties: {
        opacity: [0, 1],
        translateY: [30, 0]
      }
    },
    {
      start: 2200,
      end: 2600,
      properties: {
        opacity: [1, 0],
        translateY: [0, -30]
      }
    }
  ]);

  const selfStorageAnimation = useMultiStageScrollAnimation([
    {
      start: 2400,
      end: 2600,
      properties: {
        opacity: [0, 1],
        translateY: [30, 0]
      }
    },
    {
      start: 3400,
      end: 3800,
      properties: {
        opacity: [1, 0],
        translateY: [0, -30]
      }
    }
  ]);

  const handleChromeDownload = useCallback(() => {
    // Redirect to feature request form using current language locale
    const currentLocale = isRTL ? 'ar' : 'en';
    window.location.href = `/${currentLocale}/feature-request`;
  }, [isRTL]);

  return (
    <section className="min-h-screen" ref={containerRef}>
      {/* Hero section with centered content */}
      <div className="h-screen flex items-center justify-center relative">
        {/* Hero Content - Centered and fades out on scroll */}
        <div
          ref={heroContentRef}
          className="text-center max-w-4xl px-4"
          style={heroAnimation.style}
        >
          <h1 className="heading-1 mb-6 text-blue-950">
            {t('hero.title')}
          </h1>

          <p className="paragraph mb-8 text-blue-950">
            {t('hero.subtitle')}
          </p>

          <button
            onClick={handleChromeDownload}
            className="btn-primary-large"
          >
            {t('hero.cta')}
          </button>
        </div>

        {/* Decorative elements with hardware acceleration */}
        <div 
          className="absolute -top-8 -right-8 w-40 h-40 bg-accent/10 rounded-full blur-3xl"
          style={{ willChange: 'transform' }}
        ></div>
        <div 
          className="absolute -bottom-8 -left-8 w-40 h-40 bg-primary/10 rounded-full blur-3xl"
          style={{ willChange: 'transform' }}
        ></div>
      </div>

      {/* Second section - iPhone Wallet Mockup */}
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Enhanced background effects with optimized animations */}
        <div 
          className="absolute w-96 h-96 bg-primary/10 rounded-full blur-3xl"
          style={{ 
            willChange: 'transform',
            animation: 'pulse 4s ease-in-out infinite'
          }}
        ></div>
        <div 
          className="absolute w-64 h-64 bg-accent/10 rounded-full blur-2xl" 
          style={{ 
            willChange: 'transform',
            animation: 'pulse 4s ease-in-out infinite 1s'
          }}
        ></div>
        
        {/* Floating gradient orbs with GPU acceleration */}
        <div 
          className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
          style={{ 
            willChange: 'transform',
            animation: 'float 6s ease-in-out infinite'
          }}
        ></div>
        <div 
          className="absolute bottom-20 right-20 w-24 h-24 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-xl"
          style={{ 
            willChange: 'transform',
            animation: 'float 8s ease-in-out infinite 1.5s'
          }}
        ></div>

        {/* iPhone Wallet Display - Shows after hero content disappears, then fades out */}
        <div
          ref={iPhoneWalletRef}
          className="w-full max-w-md mx-auto relative z-10"
          style={iPhoneAnimation.style}
        >
          {/* iPhone Wallet Display with enhanced presentation */}
          <div className="w-full max-w-md mx-auto h-[700px] flex items-center justify-center relative">
            {/* Main iPhone Frame with enhanced shadow and glow */}
            <div 
              className="relative w-full h-full drop-shadow-2xl"
              style={{ willChange: 'transform' }}
            >
              {/* Glow effect behind phone */}
              <div 
                className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-[3rem] blur-xl scale-110 opacity-60"
                style={{ willChange: 'transform' }}
              ></div>
              
              <Image
                src="/iMockup - iPhone 15 Pro Max.svg"
                alt="iPhone Wallet Demo"
                fill
                style={{ objectFit: 'contain' }}
                priority
                className="relative z-10"
              />
            </div>

            {/* Floating feature cards around the phone */}
            <div 
              className={`absolute ${isRTL ? '-right-32' : '-left-32'} top-16 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 max-w-48`}
              style={{
                ...iPhoneAnimation.style,
                willChange: 'transform, opacity',
                transform: `${iPhoneAnimation.style.transform} translateX(${(1 - iPhoneAnimation.opacity) * (isRTL ? 50 : -50)}px)`,
                transition: 'all 0.8s ease-out'
              }}
            >
              <div className="flex items-center mb-2" dir={isRTL ? 'rtl' : 'ltr'}>
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">security</span>
                </div>
                <h3 className={`font-semibold text-gray-800 text-sm ${isRTL ? 'mr-3 font-tajawal' : 'ml-3'}`}>
                  {t('mockup.secureStorage.title')}
                </h3>
              </div>
              <p className={`text-gray-600 text-xs ${isRTL ? 'font-tajawal' : ''}`}>
                {t('mockup.secureStorage.description')}
              </p>
            </div>

            <div 
              className={`absolute ${isRTL ? '-left-32' : '-right-32'} top-32 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 max-w-48`}
              style={{
                ...iPhoneAnimation.style,
                willChange: 'transform, opacity',
                transform: `${iPhoneAnimation.style.transform} translateX(${(1 - iPhoneAnimation.opacity) * (isRTL ? -50 : 50)}px)`,
                transition: 'all 0.8s ease-out 0.2s'
              }}
            >
              <div className="flex items-center mb-2" dir={isRTL ? 'rtl' : 'ltr'}>
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">language</span>
                </div>
                <h3 className={`font-semibold text-gray-800 text-sm ${isRTL ? 'mr-3 font-tajawal' : 'ml-3'}`}>
                  {t('mockup.multiLanguage.title')}
                </h3>
              </div>
              <p className={`text-gray-600 text-xs ${isRTL ? 'font-tajawal' : ''}`}>
                {t('mockup.multiLanguage.description')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Arabic language support text - Appears after scrolling past iPhone section, then fades out */}
      <div 
        className="min-h-screen flex items-center justify-center relative"
        style={arabicSupportAnimation.style}
      >
        <div className="text-center max-w-4xl px-4">
          <h2 className="heading-2 mb-6 text-blue-950">
            {t('arabicSupport.title')}
          </h2>
          <p className="paragraph text-blue-950">
            {t('arabicSupport.description')}
          </p>
        </div>
      </div>

      {/* Self Storage text - Appears after Arabic support, then fades out */}
      <div 
        className="min-h-screen flex items-center justify-center relative"
        style={selfStorageAnimation.style}
      >
        <div className="text-center max-w-4xl px-4">
          <h2 className="heading-2 mb-6 text-blue-950">
            {t('selfStorage.title')}
          </h2>
          <p className="paragraph text-blue-950">
            {t('selfStorage.description')}
          </p>
        </div>
      </div>
    </section>
  );
} 