'use client';

import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { ErrorType, ErrorSeverity } from '@/lib/errorHandler';

export interface FallbackUIProps {
  errorType?: ErrorType;
  severity?: ErrorSeverity;
  message?: string;
  retry?: () => void;
  canRetry?: boolean;
  context?: string;
  showDetails?: boolean;
  fullPage?: boolean;
  children?: React.ReactNode;
}

// Main fallback UI component
export function FallbackUI({
  errorType = ErrorType.UNKNOWN,
  severity = ErrorSeverity.MEDIUM,
  message,
  retry,
  canRetry = false,
  context,
  showDetails = false,
  fullPage = false,
  children
}: FallbackUIProps) {
  const { t, isRTL } = useLanguage();
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    if (!retry || isRetrying) return;
    
    setIsRetrying(true);
    try {
      await retry();
    } finally {
      setIsRetrying(false);
    }
  };

  const getErrorIcon = () => {
    switch (errorType) {
      case ErrorType.NETWORK:
        return '📡';
      case ErrorType.AUTHENTICATION:
        return '🔐';
      case ErrorType.AUTHORIZATION:
        return '🚫';
      case ErrorType.WALLET:
        return '👛';
      case ErrorType.API:
        return '⚙️';
      case ErrorType.DATABASE:
        return '🗄️';
      case ErrorType.VALIDATION:
        return '✏️';
      default:
        return '⚠️';
    }
  };

  const getErrorTitle = () => {
    switch (errorType) {
      case ErrorType.NETWORK:
        return t('error.network.title') || 'Connection Problem';
      case ErrorType.AUTHENTICATION:
        return t('error.auth.title') || 'Authentication Required';
      case ErrorType.AUTHORIZATION:
        return t('error.authorization.title') || 'Access Denied';
      case ErrorType.WALLET:
        return t('error.wallet.title') || 'Wallet Error';
      case ErrorType.API:
        return t('error.api.title') || 'Service Unavailable';
      case ErrorType.DATABASE:
        return t('error.database.title') || 'Data Error';
      case ErrorType.VALIDATION:
        return t('error.validation.title') || 'Validation Error';
      default:
        return t('error.generic.title') || 'Something went wrong';
    }
  };

  const getErrorDescription = () => {
    if (message) return message;
    
    switch (errorType) {
      case ErrorType.NETWORK:
        return t('error.network.description') || 'Please check your internet connection and try again.';
      case ErrorType.AUTHENTICATION:
        return t('error.auth.description') || 'Please log in to continue.';
      case ErrorType.AUTHORIZATION:
        return t('error.authorization.description') || 'You don\'t have permission to access this resource.';
      case ErrorType.WALLET:
        return t('error.wallet.description') || 'There was a problem with your wallet connection.';
      case ErrorType.API:
        return t('error.api.description') || 'Our services are temporarily unavailable. Please try again later.';
      case ErrorType.DATABASE:
        return t('error.database.description') || 'We\'re having trouble accessing your data. Please try again.';
      case ErrorType.VALIDATION:
        return t('error.validation.description') || 'Please check your input and try again.';
      default:
        return t('error.generic.description') || 'An unexpected error occurred. Please try again.';
    }
  };

  const getSeverityColor = () => {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 'text-red-400 bg-red-500/20';
      case ErrorSeverity.HIGH:
        return 'text-orange-400 bg-orange-500/20';
      case ErrorSeverity.MEDIUM:
        return 'text-yellow-400 bg-yellow-500/20';
      case ErrorSeverity.LOW:
        return 'text-blue-400 bg-blue-500/20';
      default:
        return 'text-gray-400 bg-gray-500/20';
    }
  };

  const containerClass = fullPage
    ? "min-h-screen flex items-center justify-center bg-gray-900 text-white p-6"
    : "min-h-[300px] flex items-center justify-center bg-gray-800/50 rounded-lg text-white p-6 m-4";

  return (
    <div className={containerClass} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="text-center max-w-lg w-full">
        <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 ${getSeverityColor()}`}>
          <span className="text-4xl">{getErrorIcon()}</span>
        </div>
        
        <h2 className={`text-2xl font-bold mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
          {getErrorTitle()}
        </h2>
        
        <p className={`text-gray-300 mb-6 leading-relaxed ${isRTL ? 'font-tajawal' : ''}`}>
          {getErrorDescription()}
        </p>

        {context && showDetails && (
          <div className="mb-6 p-4 bg-gray-700/50 rounded-lg text-left">
            <p className="text-sm text-gray-400 mb-2">Context:</p>
            <p className="text-sm text-gray-300 font-mono">{context}</p>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {canRetry && retry && (
            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className={`px-6 py-3 rounded-lg transition-all duration-200 ${
                isRetrying
                  ? 'bg-gray-600 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
              } text-white font-medium`}
            >
              {isRetrying ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('error.retrying') || 'Retrying...'}
                </span>
              ) : (
                t('error.retry') || 'Try Again'
              )}
            </button>
          )}
          
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-gray-600 hover:bg-gray-700 active:bg-gray-800 text-white rounded-lg transition-all duration-200 font-medium"
          >
            {t('error.refresh') || 'Refresh Page'}
          </button>

          {errorType !== ErrorType.AUTHENTICATION && (
            <button
              onClick={() => window.history.back()}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 active:bg-gray-800 text-white rounded-lg transition-all duration-200 font-medium"
            >
              {t('error.goBack') || 'Go Back'}
            </button>
          )}
        </div>

        {children && (
          <div className="mt-6 pt-6 border-t border-gray-600">
            {children}
          </div>
        )}
      </div>
    </div>
  );
}

// Specialized fallback components
export function NetworkErrorFallback({ retry, canRetry = true }: { retry?: () => void; canRetry?: boolean }) {
  return (
    <FallbackUI
      errorType={ErrorType.NETWORK}
      severity={ErrorSeverity.HIGH}
      retry={retry}
      canRetry={canRetry}
      context="Network connection failed"
    />
  );
}

export function AuthErrorFallback({ message }: { message?: string }) {
  return (
    <FallbackUI
      errorType={ErrorType.AUTHENTICATION}
      severity={ErrorSeverity.HIGH}
      message={message}
      canRetry={false}
    >
      <button
        onClick={() => window.location.href = '/auth/login'}
        className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
      >
        Sign In
      </button>
    </FallbackUI>
  );
}

export function APIErrorFallback({ retry, canRetry = true, context }: { 
  retry?: () => void; 
  canRetry?: boolean;
  context?: string;
}) {
  return (
    <FallbackUI
      errorType={ErrorType.API}
      severity={ErrorSeverity.MEDIUM}
      retry={retry}
      canRetry={canRetry}
      context={context}
    />
  );
}

export function WalletErrorFallback({ retry, canRetry = true }: { retry?: () => void; canRetry?: boolean }) {
  return (
    <FallbackUI
      errorType={ErrorType.WALLET}
      severity={ErrorSeverity.HIGH}
      retry={retry}
      canRetry={canRetry}
      context="Wallet connection error"
    >
      <div className="space-y-3">
        <button
          onClick={() => window.location.reload()}
          className="block w-full px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium"
        >
          Reconnect Wallet
        </button>
        <p className="text-sm text-gray-400">
          Make sure your wallet extension is installed and unlocked
        </p>
      </div>
    </FallbackUI>
  );
}

// Loading state with error recovery
export function LoadingWithFallback({ 
  children, 
  isLoading, 
  error, 
  retry,
  fallbackData,
  showFallbackData = true
}: {
  children: React.ReactNode;
  isLoading: boolean;
  error?: Error | null;
  retry?: () => void;
  fallbackData?: React.ReactNode;
  showFallbackData?: boolean;
}) {
  const [showFallback, setShowFallback] = useState(false);

  useEffect(() => {
    if (error && !isLoading) {
      const timer = setTimeout(() => setShowFallback(true), 2000);
      return () => clearTimeout(timer);
    } else {
      setShowFallback(false);
    }
  }, [error, isLoading]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    if (showFallback && fallbackData && showFallbackData) {
      return (
        <div>
          <div className="mb-4 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
            <p className="text-yellow-400 text-sm">
              ⚠️ Using cached data due to connection issues
            </p>
          </div>
          {fallbackData}
        </div>
      );
    }

    return <FallbackUI retry={retry} canRetry={!!retry} />;
  }

  return <>{children}</>;
}

// Skeleton loader for graceful loading states
export function SkeletonLoader({ lines = 3, className = "" }: { lines?: number; className?: string }) {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={`h-4 bg-gray-700/50 rounded mb-3 ${
            i === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  );
}

// Error boundary wrapper with custom fallback
export function ErrorBoundaryWrapper({ 
  children, 
  fallback, 
  onError 
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}) {
  return (
    <ErrorBoundary
      fallback={fallback || <FallbackUI fullPage={false} />}
      onError={onError}
    >
      {children}
    </ErrorBoundary>
  );
}

// Import the main ErrorBoundary component
import ErrorBoundary from './ErrorBoundary'; 