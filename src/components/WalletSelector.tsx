'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';
import { ManagedWallet, WalletGroup } from '@/lib/walletManager';
import { getChainInfo } from '@/lib/assets';
import { WalletProvider } from '@/types';
import AddWalletModal from './AddWalletModal';

// Helper function to get wallet provider logo
const getProviderLogo = (provider: WalletProvider) => {
  switch (provider) {
    case 'metamask':
      return '/MetaMask-icon-fox-with-margins.svg';
    case 'walletconnect':
      return '/walletconnect-seeklogo.svg';
    case 'coinbase':
      return 'https://images.ctfassets.net/q5ulk4bp65r7/3TBS4oVkD1ghowTqVQJlqj/2dfd4ea3b623a7c0d8deb2ff445dee9e/Consumer_Wordmark.svg';
    default:
      return null;
  }
};

// Helper function to get provider fallback colors
const getProviderColors = (provider: WalletProvider) => {
  switch (provider) {
    case 'metamask':
      return 'from-orange-500 to-orange-600';
    case 'walletconnect':
      return 'from-blue-500 to-blue-600';
    case 'coinbase':
      return 'from-blue-600 to-blue-700';
    default:
      return 'from-blue-500 to-purple-600';
  }
};

interface WalletSelectorProps {
  walletGroups: WalletGroup[];
  selectedWallet: ManagedWallet | null;
  onWalletSelect: (wallet: ManagedWallet) => void;
  onWalletAdded?: (wallet: ManagedWallet) => void;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  excludeWallet?: ManagedWallet; // For transfer, exclude the "from" wallet from "to" selection
}

export default function WalletSelector({
  walletGroups,
  selectedWallet,
  onWalletSelect,
  onWalletAdded,
  isOpen,
  onClose,
  title,
  excludeWallet
}: WalletSelectorProps) {
  const { t } = useLanguage();
  const [showAddWalletModal, setShowAddWalletModal] = useState(false);

  const handleWalletSelect = (wallet: ManagedWallet) => {
    onWalletSelect(wallet);
    onClose();
  };

  const handleAddWallet = () => {
    setShowAddWalletModal(true);
  };

  const handleWalletAdded = (wallet: ManagedWallet) => {
    setShowAddWalletModal(false);
    if (onWalletAdded) {
      onWalletAdded(wallet);
    }
    onWalletSelect(wallet); // Auto-select the newly added wallet
    onClose();
  };

  // Filter out excluded wallet
  const filteredGroups = walletGroups.map(group => ({
    ...group,
    wallets: group.wallets.filter(wallet => 
      !excludeWallet || wallet.id !== excludeWallet.id
    )
  })).filter(group => group.wallets.length > 0);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end sm:items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 100, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 100, scale: 0.95 }}
        className="bg-gray-800 rounded-t-2xl sm:rounded-2xl w-full max-w-md max-h-[80vh] border border-gray-700 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-bold text-white">
            {title || t('transfer.selectWallet')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <span className="material-symbols-outlined">close</span>
          </button>
        </div>

        {/* Wallet List */}
        <div className="flex-1 overflow-y-auto max-h-96">
          {filteredGroups.length === 0 ? (
            <div className="text-center py-8">
              <span className="material-symbols-outlined text-gray-400 text-4xl mb-2">
                account_balance_wallet
              </span>
              <p className="text-gray-400 text-sm mb-4">
                {t('transfer.noWallets')}
              </p>
              <button
                onClick={handleAddWallet}
                className="bg-primary hover:bg-primary/80 text-white px-4 py-2 rounded-lg text-sm transition-colors"
              >
                {t('transfer.addWallet')}
              </button>
            </div>
          ) : (
            <div className="p-2">
              {filteredGroups.map((group) => (
                <div key={group.chainType} className="mb-4">
                  {/* Chain Header */}
                  <div className="flex items-center px-3 py-2 mb-2">
                    <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
                      <img
                        src={getChainInfo(group.chainType).logoUrl}
                        alt={getChainInfo(group.chainType).name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">
                      {getChainInfo(group.chainType).name}
                    </span>
                    <span className="text-gray-500 text-xs ml-2">
                      ({group.wallets.length})
                    </span>
                  </div>

                  {/* Wallets in this chain */}
                  {group.wallets.map((wallet) => (
                    <motion.button
                      key={wallet.id}
                      onClick={() => handleWalletSelect(wallet)}
                      className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors mb-1 ${
                        selectedWallet?.id === wallet.id
                          ? 'bg-primary/20 border border-primary/30'
                          : 'hover:bg-gray-700'
                      }`}
                      whileHover={{ scale: 1.01 }}
                      whileTap={{ scale: 0.99 }}
                    >
                      <div className="flex items-center">
                        {/* Wallet Icon */}
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                          getProviderLogo(wallet.provider) ? 'bg-white p-1' : `bg-gradient-to-br ${getProviderColors(wallet.provider)}`
                        }`}>
                          {getProviderLogo(wallet.provider) ? (
                            <img
                              src={getProviderLogo(wallet.provider)!}
                              alt={wallet.provider}
                              className="w-full h-full object-contain"
                              onError={(e) => {
                                // Fallback to generic wallet icon if logo fails
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.parentElement!.innerHTML = '<span class="material-symbols-outlined text-white text-sm">account_balance_wallet</span>';
                                target.parentElement!.className = `w-10 h-10 rounded-full bg-gradient-to-br ${getProviderColors(wallet.provider)} flex items-center justify-center mr-3`;
                              }}
                            />
                          ) : (
                            <span className="material-symbols-outlined text-white text-sm">
                              account_balance_wallet
                            </span>
                          )}
                        </div>

                        {/* Wallet Info */}
                        <div className="text-left">
                          <div className="flex items-center">
                            <span className="text-white font-medium text-sm">
                              {wallet.name}
                            </span>
                            {wallet.isDefault && (
                              <span className="ml-2 bg-primary/20 text-primary text-xs px-2 py-0.5 rounded-full">
                                {t('transfer.default')}
                              </span>
                            )}
                          </div>
                          <div className="text-gray-400 text-xs">
                            {wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}
                          </div>
                          {wallet.balance && (
                            <div className="text-gray-500 text-xs">
                              {parseFloat(wallet.balance).toFixed(4)} {group.chainType.toUpperCase()}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Connection Status */}
                      <div className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          wallet.isConnected ? 'bg-green-500' : 'bg-gray-500'
                        }`} />
                        <span className="material-symbols-outlined text-gray-400 text-sm">
                          chevron_right
                        </span>
                      </div>
                    </motion.button>
                  ))}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Add Wallet Button */}
        <div className="p-4 border-t border-gray-700">
          <button
            onClick={handleAddWallet}
            className="w-full flex items-center justify-center py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            <span className="material-symbols-outlined mr-2 text-sm">add</span>
            {t('transfer.addWallet')}
          </button>
        </div>
      </motion.div>

      {/* Add Wallet Modal */}
      <AddWalletModal
        isOpen={showAddWalletModal}
        onClose={() => setShowAddWalletModal(false)}
        onWalletAdded={handleWalletAdded}
      />
    </div>
  );
}

// Wallet Display Component (for showing selected wallet)
interface WalletDisplayProps {
  wallet: ManagedWallet | null;
  onClick: () => void;
  placeholder?: string;
  showBalance?: boolean;
  variant?: 'from' | 'to';
}

export function WalletDisplay({ 
  wallet, 
  onClick, 
  placeholder = 'Select Wallet',
  showBalance = true,
  variant = 'from'
}: WalletDisplayProps) {
  const variantColors = {
    from: 'from-blue-500 to-blue-600',
    to: 'from-green-500 to-green-600'
  };

  return (
    <button
      onClick={onClick}
      className="w-full flex items-center justify-between bg-gray-800 rounded-xl p-3 hover:bg-gray-750 transition-colors"
    >
      <div className="flex items-center">
        {wallet ? (
          <>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
              getProviderLogo(wallet.provider) ? 'bg-white p-1' : `bg-gradient-to-br ${getProviderColors(wallet.provider)}`
            }`}>
              {getProviderLogo(wallet.provider) ? (
                <img
                  src={getProviderLogo(wallet.provider)!}
                  alt={wallet.provider}
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    // Fallback to generic wallet icon if logo fails
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.parentElement!.innerHTML = '<span class="material-symbols-outlined text-white text-xs">account_balance_wallet</span>';
                    target.parentElement!.className = `w-8 h-8 rounded-full bg-gradient-to-br ${getProviderColors(wallet.provider)} flex items-center justify-center mr-3`;
                  }}
                />
              ) : (
                <span className="material-symbols-outlined text-white text-xs">
                  account_balance_wallet
                </span>
              )}
            </div>
            <div className="text-left">
              <div className="flex items-center">
                <span className="text-white font-medium text-sm">
                  {wallet.name}
                </span>
                {wallet.isDefault && (
                  <span className="ml-2 bg-primary/20 text-primary text-xs px-1.5 py-0.5 rounded">
                    Default
                  </span>
                )}
              </div>
              <div className="text-gray-400 text-xs">
                {wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}
                {showBalance && wallet.balance && (
                  <span className="ml-2">
                    • {parseFloat(wallet.balance).toFixed(4)} {wallet.chainType.toUpperCase()}
                  </span>
                )}
              </div>
            </div>
          </>
        ) : (
          <>
            <div className={`w-8 h-8 rounded-full bg-gradient-to-br ${variantColors[variant]} opacity-50 flex items-center justify-center mr-3`}>
              <span className="material-symbols-outlined text-white text-xs">
                account_balance_wallet
              </span>
            </div>
            <span className="text-gray-400 text-sm">{placeholder}</span>
          </>
        )}
      </div>
      <span className="material-symbols-outlined text-gray-400 text-sm">
        expand_more
      </span>
    </button>
  );
}
