'use client';

import React, { useState } from 'react';
import { useErrorHandler } from '@/lib/errorHandler';
import SectionErrorBoundary from './SectionErrorBoundary';

interface ErrorTestComponentProps {
  showInDevelopment?: boolean;
}

export default function ErrorTestComponent({ showInDevelopment = true }: ErrorTestComponentProps) {
  const [shouldThrowError, setShouldThrowError] = useState(false);
  const { 
    handleNetworkError, 
    handleAuthError, 
    handleWalletError, 
    handleAPIError, 
    handleValidationError,
    handleCriticalError,
    getErrorHistory,
    getErrorStats
  } = useErrorHandler();

  // Only show in development unless explicitly configured
  if (!showInDevelopment && process.env.NODE_ENV !== 'development') {
    return null;
  }

  // This will trigger a React error boundary
  if (shouldThrowError) {
    throw new Error('Test React Error Boundary - This is intentional for testing');
  }

  const triggerNetworkError = () => {
    handleNetworkError('Test network connection failed', {
      component: 'ErrorTestComponent',
      action: 'test_network_error'
    });
  };

  const triggerAuthError = () => {
    handleAuthError('Test authentication failed', {
      component: 'ErrorTestComponent',
      action: 'test_auth_error'
    });
  };

  const triggerWalletError = () => {
    handleWalletError('Test wallet connection failed', {
      component: 'ErrorTestComponent',
      action: 'test_wallet_error'
    });
  };

  const triggerAPIError = () => {
    handleAPIError('Test API service unavailable', {
      component: 'ErrorTestComponent',
      action: 'test_api_error'
    });
  };

  const triggerValidationError = () => {
    handleValidationError('Test validation: Email is required', {
      component: 'ErrorTestComponent',
      action: 'test_validation_error'
    });
  };

  const triggerCriticalError = () => {
    handleCriticalError('Test critical system failure', {
      component: 'ErrorTestComponent',
      action: 'test_critical_error'
    });
  };

  const triggerReactError = () => {
    setShouldThrowError(true);
  };

  const showErrorStats = () => {
    const stats = getErrorStats();
    const history = getErrorHistory();
    console.log('Error Statistics:', stats);
    console.log('Error History:', history);
    alert(`Error Stats - Total: ${stats.total}, Check console for details`);
  };

  return (
    <div className="bg-gray-800 p-6 rounded-lg m-4">
      <h3 className="text-white text-lg font-bold mb-4">
        🧪 Error Handling Test Component
        {process.env.NODE_ENV === 'development' && (
          <span className="text-yellow-400 text-sm ml-2">(Development Only)</span>
        )}
      </h3>
      
      <p className="text-gray-300 text-sm mb-4">
        Use these buttons to test different error handling scenarios:
      </p>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
        <button
          onClick={triggerNetworkError}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          Network Error
        </button>
        
        <button
          onClick={triggerAuthError}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          Auth Error
        </button>
        
        <button
          onClick={triggerWalletError}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          Wallet Error
        </button>
        
        <button
          onClick={triggerAPIError}
          className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          API Error
        </button>
        
        <button
          onClick={triggerValidationError}
          className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          Validation Error
        </button>
        
        <button
          onClick={triggerCriticalError}
          className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          Critical Error
        </button>
      </div>

      <div className="border-t border-gray-700 pt-4">
        <div className="flex gap-3">
          <button
            onClick={triggerReactError}
            className="bg-red-900 hover:bg-red-800 text-white px-4 py-2 rounded text-sm transition-colors"
          >
            🚨 React Error Boundary
          </button>
          
          <button
            onClick={showErrorStats}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm transition-colors"
          >
            📊 Show Error Stats
          </button>
        </div>
        
        <p className="text-gray-400 text-xs mt-2">
          React Error Boundary test will crash this component (intentionally)
        </p>
      </div>

      {/* Nested section to test section-level error boundary */}
      <SectionErrorBoundary sectionName="Test Section">
        <TestSection />
      </SectionErrorBoundary>
    </div>
  );
}

// Separate component to test section-level error handling
function TestSection() {
  const [shouldCrash, setShouldCrash] = useState(false);

  if (shouldCrash) {
    throw new Error('Test Section Error - This section crashed intentionally');
  }

  return (
    <div className="bg-gray-700 p-4 rounded mt-4">
      <h4 className="text-white font-medium mb-2">Test Section (Section Error Boundary)</h4>
      <p className="text-gray-300 text-sm mb-3">
        This section is wrapped in a SectionErrorBoundary. If it crashes, only this section will show an error.
      </p>
      <button
        onClick={() => setShouldCrash(true)}
        className="bg-red-700 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
      >
        Crash This Section
      </button>
    </div>
  );
} 