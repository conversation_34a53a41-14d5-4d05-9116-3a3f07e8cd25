'use client';

import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { errorHandler, ErrorType, ErrorSeverity } from '@/lib/errorHandler';

interface PageErrorBoundaryProps {
  children: React.ReactNode;
  pageName?: string;
  fallback?: React.ReactNode;
}

interface PageErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorId?: string;
}

class PageErrorBoundary extends React.Component<PageErrorBoundaryProps, PageErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 2;

  constructor(props: PageErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): Partial<PageErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Handle error through centralized error handler
    const handledError = errorHandler.handleError(
      error,
      ErrorType.UI,
      ErrorSeverity.HIGH,
      {
        component: 'PageErrorBoundary',
        action: 'page_render',
        metadata: {
          pageName: this.props.pageName,
          componentStack: errorInfo.componentStack,
          retryCount: this.retryCount
        }
      },
      false // Don't show toast notification as we have our own UI
    );

    this.setState({ errorId: handledError.id });
  }

  retry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({ hasError: false, error: undefined, errorId: undefined });
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <PageErrorFallback 
          error={this.state.error}
          errorId={this.state.errorId}
          pageName={this.props.pageName}
          canRetry={this.retryCount < this.maxRetries}
          onRetry={this.retry}
        />
      );
    }

    return this.props.children;
  }
}

interface PageErrorFallbackProps {
  error?: Error;
  errorId?: string;
  pageName?: string;
  canRetry?: boolean;
  onRetry?: () => void;
}

function PageErrorFallback({ 
  error, 
  errorId, 
  pageName,
  canRetry = false, 
  onRetry 
}: PageErrorFallbackProps) {
  const { t, isRTL } = useLanguage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800 text-white p-4" dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="text-center max-w-lg">
        <div className="w-24 h-24 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-8">
          <span className="material-symbols-outlined text-5xl text-red-400">error</span>
        </div>
        
        <h1 className={`text-3xl font-bold mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('error.page.title') || 'Page Error'}
        </h1>
        
        <p className={`text-gray-300 mb-6 text-lg ${isRTL ? 'font-tajawal' : ''}`}>
          {pageName 
            ? `The ${pageName} page encountered an error and could not be displayed.`
            : (t('error.page.description') || 'The page encountered an error and could not be displayed.')
          }
        </p>

        <p className={`text-gray-400 mb-8 ${isRTL ? 'font-tajawal' : ''}`}>
          {t('error.page.help') || 'Please try refreshing the page or contact support if the problem persists.'}
        </p>

        {process.env.NODE_ENV === 'development' && (
          <details className="mb-8 text-left bg-gray-800 p-4 rounded-lg text-sm">
            <summary className="cursor-pointer text-yellow-400 mb-3 font-medium">Debug Information</summary>
            <div className="space-y-3 text-gray-300">
              {errorId && <div><strong>Error ID:</strong> {errorId}</div>}
              {pageName && <div><strong>Page:</strong> {pageName}</div>}
              {error?.name && <div><strong>Error Type:</strong> {error.name}</div>}
              {error?.message && <div><strong>Message:</strong> {error.message}</div>}
              {error?.stack && (
                <div>
                  <strong>Stack Trace:</strong>
                  <pre className="mt-2 text-xs overflow-auto bg-gray-900 p-3 rounded max-h-40">
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
          </details>
        )}

        <div className="flex gap-4 justify-center">
          {canRetry && onRetry && (
            <button
              onClick={onRetry}
              className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors font-medium"
            >
              {t('error.retry') || 'Try Again'}
            </button>
          )}
          
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-600 hover:bg-gray-700 px-6 py-3 rounded-lg transition-colors font-medium"
          >
            {t('error.refresh') || 'Refresh Page'}
          </button>

          <button
            onClick={() => window.location.href = '/'}
            className="bg-purple-600 hover:bg-purple-700 px-6 py-3 rounded-lg transition-colors font-medium"
          >
            {t('error.home') || 'Go Home'}
          </button>
        </div>

        {errorId && (
          <p className="mt-6 text-xs text-gray-500">
            {t('error.id') || 'Error ID'}: {errorId}
          </p>
        )}
      </div>
    </div>
  );
}

export default PageErrorBoundary; 