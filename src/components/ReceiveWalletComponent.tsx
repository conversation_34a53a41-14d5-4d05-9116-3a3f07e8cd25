'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '@/context/LanguageContext';
import { useParams } from 'next/navigation';
import { ChainType } from '@/types';
import { ManagedWallet, walletManager } from '@/lib/walletManager';
import { getChainInfo, getChainTypeFromChainId } from '@/lib/assets';
import { qrCodeGenerator, PaymentRequest } from '@/lib/qrCodeGenerator';
import WalletSelector, { WalletDisplay } from './WalletSelector';
import AddWalletModal from './AddWalletModal';

export default function ReceiveWalletComponent() {
  const { t } = useLanguage();
  const params = useParams();
  const locale = params.locale as string;

  // State for client-side rendering
  const [mounted, setMounted] = useState(false);

  // Receive state
  const [selectedWallet, setSelectedWallet] = useState<ManagedWallet | null>(null);
  const [selectedChain, setSelectedChain] = useState<ChainType>('ethereum');
  const [requestAmount, setRequestAmount] = useState('');
  const [requestMessage, setRequestMessage] = useState('');
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Modal states
  const [showWalletSelector, setShowWalletSelector] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);

  // Copy feedback
  const [copyFeedback, setCopyFeedback] = useState('');

  // Wallet state with error handling
  const [walletState, setWalletState] = useState({
    address: null as string | null,
    isConnected: false,
    error: null as string | null
  });

  // Available wallets
  const [walletGroups, setWalletGroups] = useState(walletManager.getWalletsGrouped());

  // Safely use wagmi hooks
  let accountResult = { address: null as string | null | undefined, isConnected: false, chainId: undefined as number | undefined };

  try {
    accountResult = useAccount();
  } catch (error) {
    console.warn('Wagmi hooks not available:', error);
  }

  // Update wallet state when mounted
  useEffect(() => {
    setMounted(true);
    try {
      setWalletState({
        address: accountResult.address || null,
        isConnected: accountResult.isConnected,
        error: null
      });

      // Auto-select connected wallet
      if (accountResult.address && accountResult.isConnected) {
        const connectedWallet = walletManager.getWalletByAddress(accountResult.address);
        if (connectedWallet) {
          setSelectedWallet(connectedWallet);
          setSelectedChain(connectedWallet.chainType);
        } else {
          // Import the connected wallet
          const chainType = getChainTypeFromChainId(accountResult.chainId);
          if (chainType) {
            const importedWallet = walletManager.importFromConnection({
              address: accountResult.address,
              provider: 'metamask', // Default assumption
              chainType,
              chainId: accountResult.chainId,
              isConnected: true
            });
            setSelectedWallet(importedWallet);
            setSelectedChain(chainType);
            setWalletGroups(walletManager.getWalletsGrouped());
          }
        }
      }
    } catch (error) {
      setWalletState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error)
      }));
    }
  }, [accountResult.address, accountResult.isConnected, accountResult.chainId]);

  // Generate QR code when wallet or request details change
  useEffect(() => {
    if (selectedWallet && mounted) {
      generateQRCode();
    }
  }, [selectedWallet, requestAmount, requestMessage, mounted]);

  // Helper functions
  const getChainTypeFromChainId = (chainId?: number): ChainType | null => {
    switch (chainId) {
      case 1: return 'ethereum';
      case 137: return 'polygon';
      case 56: return 'bsc';
      default: return 'ethereum'; // Default fallback
    }
  };

  const generateQRCode = async () => {
    if (!selectedWallet) return;

    setIsGeneratingQR(true);
    try {
      let qrData: string;

      if (requestAmount || requestMessage) {
        // Generate payment request QR
        const paymentRequest: PaymentRequest = {
          address: selectedWallet.address,
          chainType: selectedWallet.chainType,
          amount: requestAmount || undefined,
          message: requestMessage || undefined
        };
        qrData = await qrCodeGenerator.generatePaymentRequestQR(paymentRequest, {
          size: 200,
          color: { dark: '#1f2937', light: '#ffffff' }
        });
      } else {
        // Generate simple address QR
        qrData = await qrCodeGenerator.generateAddressQR(
          selectedWallet.address,
          selectedWallet.chainType,
          {
            size: 200,
            color: { dark: '#1f2937', light: '#ffffff' }
          }
        );
      }

      setQrCodeDataUrl(qrData);
    } catch (error) {
      console.error('QR code generation failed:', error);
      setQrCodeDataUrl('');
    } finally {
      setIsGeneratingQR(false);
    }
  };

  const handleCopyAddress = async () => {
    if (!selectedWallet) return;

    try {
      await navigator.clipboard.writeText(selectedWallet.address);
      setCopyFeedback(t('receive.copied'));
      setTimeout(() => setCopyFeedback(''), 2000);
    } catch (error) {
      console.error('Failed to copy address:', error);
      setCopyFeedback(t('receive.copyFailed'));
      setTimeout(() => setCopyFeedback(''), 2000);
    }
  };

  const handleShare = async (method: 'native' | 'qr' | 'link') => {
    if (!selectedWallet) return;

    const shareData = {
      title: t('receive.shareTitle'),
      text: `${t('receive.shareText')}: ${selectedWallet.address}`,
      url: `${window.location.origin}/receive?address=${selectedWallet.address}&chain=${selectedWallet.chainType}`
    };

    switch (method) {
      case 'native':
        if (navigator.share) {
          try {
            await navigator.share(shareData);
          } catch (error) {
            console.log('Share cancelled or failed');
          }
        }
        break;
      case 'qr':
        setShowShareOptions(true);
        break;
      case 'link':
        await navigator.clipboard.writeText(shareData.url || selectedWallet.address);
        setCopyFeedback(t('receive.linkCopied'));
        setTimeout(() => setCopyFeedback(''), 2000);
        break;
    }
  };

  const handleWalletAdded = (wallet: ManagedWallet) => {
    setWalletGroups(walletManager.getWalletsGrouped());
  };

  const getCurrentAddress = (): string => {
    return selectedWallet?.address || walletState.address || '';
  };

  const getCurrentChainInfo = () => {
    const chainType = selectedWallet?.chainType || selectedChain;
    return getChainInfo(chainType);
  };

  // Don't render anything until mounted
  if (!mounted) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show connection prompt if not connected
  if (!walletState.isConnected) {
    return (
      <div className="text-center py-16">
        <div className="text-yellow-400 mb-4">
          <span className="material-symbols-outlined text-4xl">
            wallet
          </span>
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          {t('receive.connectWallet')}
        </h3>
        <p className="text-gray-400">
          {t('receive.connectWalletDescription')}
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-[400px] flex flex-col">
        {/* Wallet Selection */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2 text-sm">{t('receive.selectWallet')}</label>
          <WalletDisplay
            wallet={selectedWallet}
            onClick={() => setShowWalletSelector(true)}
            placeholder={t('receive.selectWalletPlaceholder')}
            showBalance={true}
          />
        </div>

        {selectedWallet && (
          <>
            {/* Network Display */}
            <div className="mb-6">
              <label className="block text-gray-400 mb-2 text-sm">{t('receive.network')}</label>
              <div className="flex items-center bg-gray-800 rounded-xl p-3">
                <div className="w-5 h-5 rounded-full overflow-hidden mr-2">
                  <img
                    src={getCurrentChainInfo().logoUrl}
                    alt={getCurrentChainInfo().name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-gray-300 text-sm">{getCurrentChainInfo().name}</span>
              </div>
            </div>

            {/* Wallet Address Display */}
            <div className="mb-6">
              <label className="block text-gray-400 mb-2 text-sm">{t('receive.walletAddress')}</label>
              <div className="bg-gray-800 rounded-xl p-4">
                <div className="text-white text-sm break-all mb-3 font-mono">
                  {getCurrentAddress()}
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleCopyAddress}
                    className="flex-1 bg-primary/20 text-primary hover:bg-primary/30 transition-colors flex items-center justify-center py-2 px-3 rounded-lg text-sm"
                  >
                    <span className="material-symbols-outlined mr-1 text-xs">
                      content_copy
                    </span>
                    {copyFeedback || t('receive.copyAddress')}
                  </button>
                  <button
                    onClick={() => handleShare('native')}
                    className="bg-gray-700 hover:bg-gray-600 text-white flex items-center justify-center py-2 px-3 rounded-lg text-sm transition-colors"
                  >
                    <span className="material-symbols-outlined text-xs">
                      share
                    </span>
                  </button>
                </div>
              </div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="mb-6">
              <button
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                className="flex items-center text-gray-400 hover:text-white transition-colors text-sm"
              >
                <span className="material-symbols-outlined mr-1 text-sm">
                  {showAdvancedOptions ? 'expand_less' : 'expand_more'}
                </span>
                {t('receive.advancedOptions')}
              </button>

              <AnimatePresence>
                {showAdvancedOptions && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 space-y-4"
                  >
                    {/* Request Amount */}
                    <div>
                      <label className="block text-gray-400 mb-2 text-sm">{t('receive.requestAmount')}</label>
                      <input
                        type="text"
                        value={requestAmount}
                        onChange={(e) => setRequestAmount(e.target.value)}
                        placeholder="0.00"
                        className="w-full bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-primary focus:outline-none text-sm"
                      />
                    </div>

                    {/* Request Message */}
                    <div>
                      <label className="block text-gray-400 mb-2 text-sm">{t('receive.requestMessage')}</label>
                      <input
                        type="text"
                        value={requestMessage}
                        onChange={(e) => setRequestMessage(e.target.value)}
                        placeholder={t('receive.requestMessagePlaceholder')}
                        className="w-full bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-primary focus:outline-none text-sm"
                      />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* QR Code */}
            <div className="mb-6 flex flex-col items-center">
              <div className="bg-white p-4 rounded-xl mb-3 relative">
                {isGeneratingQR ? (
                  <div className="w-48 h-48 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : qrCodeDataUrl ? (
                  <img
                    src={qrCodeDataUrl}
                    alt="QR Code"
                    className="w-48 h-48 object-contain"
                  />
                ) : (
                  <div className="w-48 h-48 bg-gray-200 flex items-center justify-center">
                    <span className="material-symbols-outlined text-gray-600 text-4xl">
                      qr_code_2
                    </span>
                  </div>
                )}
              </div>

              <div className="text-gray-400 text-center text-xs mb-3">
                {requestAmount || requestMessage
                  ? t('receive.qrPaymentDescription')
                  : t('receive.qrDescription')
                }
              </div>

              {qrCodeDataUrl && (
                <button
                  onClick={() => handleShare('qr')}
                  className="text-primary hover:text-primary/80 text-sm transition-colors"
                >
                  {t('receive.shareQR')}
                </button>
              )}
            </div>

            {/* Share Options */}
            <div className="flex gap-2">
              <button
                onClick={() => handleShare('link')}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 rounded-lg text-sm transition-colors"
              >
                {t('receive.shareAddress')}
              </button>
              <button
                onClick={() => handleShare('qr')}
                className="flex-1 bg-primary hover:bg-primary/80 text-white py-2 rounded-lg text-sm transition-colors"
                disabled={!qrCodeDataUrl}
              >
                {t('receive.saveQR')}
              </button>
            </div>

            {/* Warning */}
            <div className="bg-gray-800 rounded-xl p-3 hover:bg-gray-750 transition-colors mt-6">
              <div className="flex items-start">
                <span className="material-symbols-outlined text-yellow-400 mr-2 mt-0.5 text-sm">
                  warning
                </span>
                <div className="text-gray-300 text-xs">
                  {t('receive.warning')}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Connection prompt if no wallet selected */}
        {!selectedWallet && (
          <div className="text-center py-16">
            <div className="text-yellow-400 mb-4">
              <span className="material-symbols-outlined text-4xl">
                account_balance_wallet
              </span>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">
              {t('receive.selectWalletTitle')}
            </h3>
            <p className="text-gray-400 mb-4">
              {t('receive.selectWalletDescription')}
            </p>
            <button
              onClick={() => setShowWalletSelector(true)}
              className="bg-primary hover:bg-primary/80 text-white px-6 py-2 rounded-lg transition-colors"
            >
              {t('receive.selectWallet')}
            </button>
          </div>
        )}
      </div>

      {/* Modals */}
      <WalletSelector
        walletGroups={walletGroups}
        selectedWallet={selectedWallet}
        onWalletSelect={setSelectedWallet}
        onWalletAdded={handleWalletAdded}
        isOpen={showWalletSelector}
        onClose={() => setShowWalletSelector(false)}
        title={t('receive.selectWallet')}
      />

      {/* Share Options Modal */}
      {showShareOptions && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-gray-800 rounded-2xl w-full max-w-sm border border-gray-700 overflow-hidden"
          >
            <div className="p-6">
              <h3 className="text-lg font-bold text-white mb-4">{t('receive.shareOptions')}</h3>

              <div className="space-y-3">
                <button
                  onClick={() => {
                    handleShare('native');
                    setShowShareOptions(false);
                  }}
                  className="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                >
                  <span className="material-symbols-outlined mr-3">share</span>
                  <span className="text-white">{t('receive.shareNative')}</span>
                </button>

                <button
                  onClick={() => {
                    handleShare('link');
                    setShowShareOptions(false);
                  }}
                  className="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                >
                  <span className="material-symbols-outlined mr-3">link</span>
                  <span className="text-white">{t('receive.copyLink')}</span>
                </button>

                {qrCodeDataUrl && (
                  <a
                    href={qrCodeDataUrl}
                    download="wallet-qr-code.png"
                    onClick={() => setShowShareOptions(false)}
                    className="w-full flex items-center p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    <span className="material-symbols-outlined mr-3">download</span>
                    <span className="text-white">{t('receive.downloadQR')}</span>
                  </a>
                )}
              </div>

              <button
                onClick={() => setShowShareOptions(false)}
                className="w-full mt-4 bg-gray-600 hover:bg-gray-500 text-white py-2 rounded-lg transition-colors"
              >
                {t('common.cancel')}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </>
  );
}