'use client';

import { useState, useEffect } from 'react';
import { useAccount, useDisconnect } from 'wagmi';
import { useWallet } from '@solana/wallet-adapter-react';
import { useLanguage } from '@/context/LanguageContext';
import WalletModal from './WalletModal';

export default function ConnectWallet() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [walletState, setWalletState] = useState({
    ethAddress: null as string | null,
    isEthConnected: false,
    solAddress: null as string | null,
    isSolConnected: false,
    error: null as string | null
  });

  // Safely get language context with fallback
  let t = (key: string) => key;
  let isRTL = false;
  
  try {
    const languageContext = useLanguage();
    t = languageContext.t;
    isRTL = languageContext.isRTL;
  } catch (error) {
    console.warn('Language provider not available, using fallback values');
    // Fallback function for translations
    t = (key: string) => {
      const fallbackTranslations: Record<string, string> = {
        'app.connectWallet': 'Connect Wallet',
        'common.ethereum': 'Ethereum',
        'common.solana': 'Solana',
        'wallet.selectWallet': 'Select Wallet',
        'wallet.selectWalletDescription': 'Choose your preferred wallet to connect to Mokhba',
        'wallet.secureConnection': 'Your connection is secure and encrypted'
      };
      return fallbackTranslations[key] || key;
    };
  }

  // Safely get wallet hooks with error handling
  let ethAddress: string | undefined;
  let isEthConnected = false;
  let disconnectEth = async () => {};
  
  try {
    const account = useAccount();
    const { disconnectAsync } = useDisconnect();
    ethAddress = account.address;
    isEthConnected = account.isConnected;
    disconnectEth = disconnectAsync;
  } catch (error) {
    console.warn('Ethereum wallet hooks not available');
  }

  // Safely get Solana wallet hooks
  let solPublicKey: any = null;
  let isSolConnected = false;
  let disconnectSol = () => {};
  
  try {
    const wallet = useWallet();
    solPublicKey = wallet.publicKey;
    isSolConnected = wallet.connected;
    disconnectSol = wallet.disconnect;
  } catch (error) {
    console.warn('Solana wallet hooks not available');
  }

  // Only run effects after component mounts
  useEffect(() => {
    setMounted(true);
  }, []);

  // Update wallet state when hooks change
  useEffect(() => {
    if (!mounted) return;
    
    try {
      setWalletState({
        ethAddress: ethAddress || null,
        isEthConnected,
        solAddress: solPublicKey?.toString() || null,
        isSolConnected,
        error: null
      });
    } catch (error) {
      console.warn('Error updating wallet state:', error);
      setWalletState(prev => ({ ...prev, error: error instanceof Error ? error.message : String(error) }));
    }
  }, [mounted, ethAddress, isEthConnected, solPublicKey, isSolConnected]);

  // Don't render anything until component is mounted
  if (!mounted) {
    return (
      <div className="flex items-center">
        <button
          className="btn-primary-medium"
          disabled
        >
          Connect Wallet
        </button>
      </div>
    );
  }

  const handleDisconnect = async () => {
    try {
      if (walletState.isEthConnected) {
        await disconnectEth();
      }
      if (walletState.isSolConnected) {
        disconnectSol();
      }
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  };

  // If any wallet is connected, show disconnect button
  if (walletState.isEthConnected || walletState.isSolConnected) {
    return (
      <div className="flex items-center space-x-2">
        <div className="hidden sm:block">
          <div className="text-sm text-gray-300">
            {walletState.isEthConnected && walletState.ethAddress && (
              <span>{walletState.ethAddress.slice(0, 6)}...{walletState.ethAddress.slice(-4)}</span>
            )}
            {walletState.isSolConnected && walletState.solAddress && (
              <span>{walletState.solAddress.slice(0, 6)}...{walletState.solAddress.slice(-4)}</span>
            )}
          </div>
        </div>
        <button
          onClick={handleDisconnect}
          className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg font-medium text-white transition-colors"
        >
          Disconnect
        </button>
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <button
        onClick={() => setIsModalOpen(true)}
        className="btn-primary-medium"
      >
        {t('app.connectWallet')}
      </button>
      <WalletModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </div>
  );
}
