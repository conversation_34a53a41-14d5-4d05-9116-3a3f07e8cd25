'use client';

import { useRef, useState, useEffect } from 'react';
import Image from 'next/image';
import { useLanguage } from '@/context/LanguageContext';

export default function Hero() {
  const { t, isRTL } = useLanguage();
  const containerRef = useRef<HTMLDivElement>(null);
  const heroContentRef = useRef<HTMLDivElement>(null);
  const iPhoneWalletRef = useRef<HTMLDivElement>(null);
  const [scrollY, setScrollY] = useState(0);

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      // Force a re-render on scroll
      setScrollY(window.scrollY);
    };

    // Initial scroll position
    setScrollY(window.scrollY);

    // Add event listener with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Calculate animation values based on scroll position
  const calculateOpacity = (start: number, end: number, value: number) => {
    if (value <= start) return 1;
    if (value >= end) return 0;
    return 1 - (value - start) / (end - start);
  };

  const calculateReverseOpacity = (start: number, end: number, value: number) => {
    if (value <= start) return 0;
    if (value >= end) return 1;
    return (value - start) / (end - start);
  };

  const calculateScale = (start: number, end: number, minScale: number, value: number) => {
    if (value <= start) return 1;
    if (value >= end) return minScale;
    return 1 - ((1 - minScale) * (value - start)) / (end - start);
  };

  const calculateTranslateY = (start: number, end: number, distance: number, value: number) => {
    if (value <= start) return 0;
    if (value >= end) return distance;
    return (distance * (value - start)) / (end - start);
  };

  // Helper function to calculate opacity with smooth transitions in both directions
  const calculateSmoothOpacity = (startFadeIn: number, fullyVisible: number, startFadeOut: number, fullyHidden: number, currentScroll: number) => {
    if (currentScroll < startFadeIn) return 0; // Before fade-in starts
    if (currentScroll >= startFadeIn && currentScroll < fullyVisible) {
      // Fading in
      return (currentScroll - startFadeIn) / (fullyVisible - startFadeIn);
    }
    if (currentScroll >= fullyVisible && currentScroll < startFadeOut) {
      // Fully visible
      return 1;
    }
    if (currentScroll >= startFadeOut && currentScroll < fullyHidden) {
      // Fading out
      return 1 - (currentScroll - startFadeOut) / (fullyHidden - startFadeOut);
    }
    return 0; // After fully hidden
  };

  // Hero content animation values - using reverse smooth opacity for bidirectional animations
  const heroOpacity = scrollY < 600 ? 1 - calculateSmoothOpacity(0, 300, 300, 600, scrollY) : 0;
  const heroScale = calculateScale(0, 300, 0.9, scrollY);
  const heroTranslateY = calculateTranslateY(0, 300, -50, scrollY);

  // iPhone animation values - extended visibility range for smoother experience
  // Starts fading in at 300px, fully visible from 500px to 1200px, then fades out until 1600px
  const iPhoneOpacity = calculateSmoothOpacity(300, 500, 1200, 1600, scrollY);

  // Arabic support text animation values - extended visibility range for smoother experience
  // Starts fading in at 1400px, fully visible from 1600px to 2200px, then fades out until 2600px
  const arabicSupportOpacity = calculateSmoothOpacity(1400, 1600, 2200, 2600, scrollY);

  // Self Storage text animation values - extended visibility range and smooth fade out
  // Starts fading in at 2400px, fully visible from 2600px to 3400px, then fades out until 3800px
  const selfStorageOpacity = calculateSmoothOpacity(2400, 2600, 3400, 3800, scrollY);

  const handleChromeDownload = () => {
    // Redirect to feature request form using current language locale
    const currentLocale = isRTL ? 'ar' : 'en';
    window.location.href = `/${currentLocale}/feature-request`;
  };

  return (
    <section className="min-h-screen" ref={containerRef}>
      {/* Hero section with centered content */}
      <div className="h-screen flex items-center justify-center relative">
        {/* Hero Content - Centered and fades out on scroll */}
        <div
          ref={heroContentRef}
          className="text-center max-w-4xl px-4"
          style={{
            opacity: heroOpacity,
            transform: `scale(${heroScale}) translateY(${heroTranslateY}px)`,
            transition: 'opacity 0.3s ease-out, transform 0.3s ease-out',
            willChange: 'opacity, transform'
          }}
        >
          <h1 className="heading-1 mb-6 text-blue-950">
            {t('hero.title')}
          </h1>

          <p className="paragraph mb-8 text-blue-950">
            {t('hero.subtitle')}
          </p>

          <button
            onClick={handleChromeDownload}
            className="btn-primary-large"
          >
            {t('hero.cta')}
          </button>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-8 -right-8 w-40 h-40 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-primary/10 rounded-full blur-3xl"></div>
      </div>

      {/* Second section - iPhone Wallet Mockup */}
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Enhanced background effects */}
        <div className="absolute w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute w-64 h-64 bg-accent/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        
        {/* Floating gradient orbs */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-bounce" style={{ animationDuration: '3s' }}></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-xl animate-bounce" style={{ animationDuration: '4s', animationDelay: '1.5s' }}></div>

        {/* iPhone Wallet Display - Shows after hero content disappears, then fades out */}
        <div
          ref={iPhoneWalletRef}
          className="w-full max-w-md mx-auto relative z-10"
          style={{
            opacity: iPhoneOpacity,
            transition: 'opacity 0.5s ease-out, transform 0.5s ease-out',
            transform: `scale(${1 + (iPhoneOpacity - 0.5) * 0.1})`,
            willChange: 'opacity, transform'
          }}
        >
          {/* iPhone Wallet Display with enhanced presentation */}
          <div className="w-full max-w-md mx-auto h-[700px] flex items-center justify-center relative">
            {/* Main iPhone Frame with enhanced shadow and glow */}
            <div className="relative w-full h-full drop-shadow-2xl">
              {/* Glow effect behind phone */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-[3rem] blur-xl scale-110 opacity-60"></div>
              
              <Image
                src="/iMockup - iPhone 15 Pro Max.svg"
                alt="iPhone Wallet Demo"
                fill
                style={{ objectFit: 'contain' }}
                priority
                className="relative z-10"
              />
            </div>

            {/* Floating feature cards around the phone */}
            <div 
              className={`absolute ${isRTL ? '-right-32' : '-left-32'} top-16 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 max-w-48`}
              style={{
                opacity: iPhoneOpacity,
                transform: `translateX(${(1 - iPhoneOpacity) * (isRTL ? 50 : -50)}px)`,
                transition: 'all 0.8s ease-out'
              }}
            >
              <div className="flex items-center mb-2" dir={isRTL ? 'rtl' : 'ltr'}>
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">security</span>
                </div>
                <h3 className={`font-semibold text-gray-800 text-sm ${isRTL ? 'mr-3 font-tajawal' : 'ml-3'}`}>{t('mockup.secureStorage.title')}</h3>
              </div>
              <p className={`text-gray-600 text-xs ${isRTL ? 'font-tajawal' : ''}`}>{t('mockup.secureStorage.description')}</p>
            </div>

            <div 
              className={`absolute ${isRTL ? '-left-32' : '-right-32'} top-32 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 max-w-48`}
              style={{
                opacity: iPhoneOpacity,
                transform: `translateX(${(1 - iPhoneOpacity) * (isRTL ? -50 : 50)}px)`,
                transition: 'all 0.8s ease-out',
                transitionDelay: '0.2s'
              }}
            >
              <div className="flex items-center mb-2" dir={isRTL ? 'rtl' : 'ltr'}>
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">language</span>
                </div>
                <h3 className={`font-semibold text-gray-800 text-sm ${isRTL ? 'mr-3 font-tajawal' : 'ml-3'}`}>{t('mockup.multiLanguage.title')}</h3>
              </div>
              <p className={`text-gray-600 text-xs ${isRTL ? 'font-tajawal' : ''}`}>{t('mockup.multiLanguage.description')}</p>
            </div>

            <div 
              className={`absolute ${isRTL ? '-right-28' : '-left-28'} bottom-20 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 max-w-48`}
              style={{
                opacity: iPhoneOpacity,
                transform: `translateX(${(1 - iPhoneOpacity) * (isRTL ? 50 : -50)}px)`,
                transition: 'all 0.8s ease-out',
                transitionDelay: '0.4s'
              }}
            >
              <div className="flex items-center mb-2" dir={isRTL ? 'rtl' : 'ltr'}>
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">swap_horiz</span>
                </div>
                <h3 className={`font-semibold text-gray-800 text-sm ${isRTL ? 'mr-3 font-tajawal' : 'ml-3'}`}>{t('mockup.easyTrading.title')}</h3>
              </div>
              <p className={`text-gray-600 text-xs ${isRTL ? 'font-tajawal' : ''}`}>{t('mockup.easyTrading.description')}</p>
            </div>

            <div 
              className={`absolute ${isRTL ? '-left-28' : '-right-28'} bottom-32 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 max-w-48`}
              style={{
                opacity: iPhoneOpacity,
                transform: `translateX(${(1 - iPhoneOpacity) * (isRTL ? -50 : 50)}px)`,
                transition: 'all 0.8s ease-out',
                transitionDelay: '0.6s'
              }}
            >
              <div className="flex items-center mb-2" dir={isRTL ? 'rtl' : 'ltr'}>
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">account_balance_wallet</span>
                </div>
                <h3 className={`font-semibold text-gray-800 text-sm ${isRTL ? 'mr-3 font-tajawal' : 'ml-3'}`}>{t('mockup.multiChain.title')}</h3>
              </div>
              <p className={`text-gray-600 text-xs ${isRTL ? 'font-tajawal' : ''}`}>{t('mockup.multiChain.description')}</p>
            </div>
          </div>

          {/* Floating crypto icons */}
          <div className="absolute inset-0 pointer-events-none">
            <div 
              className="absolute top-8 left-8 w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center shadow-lg animate-float"
              style={{ animationDelay: '0s' }}
            >
              <span className="text-white font-bold text-sm">₿</span>
            </div>
            <div 
              className="absolute top-16 right-12 w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center shadow-lg animate-float"
              style={{ animationDelay: '1s' }}
            >
              <span className="text-white font-bold text-xs">Ξ</span>
            </div>
            <div 
              className="absolute bottom-24 left-16 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg animate-float"
              style={{ animationDelay: '2s' }}
            >
              <span className="text-white font-bold text-xs">$</span>
            </div>
          </div>
        </div>

        {/* Bottom feature highlight */}
        <div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-white/10 backdrop-blur-md rounded-full px-6 py-3 border border-white/20"
          style={{
            opacity: iPhoneOpacity * 0.8,
            transform: `translateX(-50%) translateY(${(1 - iPhoneOpacity) * 30}px)`,
            transition: 'all 0.8s ease-out'
          }}
        >
          <div className={`flex items-center text-white ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
            <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">{t('mockup.livePrices')}</span>
            </div>
            <div className="w-px h-4 bg-white/30"></div>
            <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
              <span className="text-sm font-medium">{t('mockup.realTimeUpdates')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Third section - Arabic Language Support Text */}
      <div className="min-h-screen flex items-center justify-center relative">
        {/* Arabic language support text - Appears after scrolling past iPhone section, then fades out */}
        <div
          className="text-center max-w-2xl px-4"
          style={{
            opacity: arabicSupportOpacity,
            transition: 'opacity 0.8s ease-in-out',
            willChange: 'opacity'
          }}
        >
          <h2 className={`heading-1 text-blue-950 font-bold mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
            {isRTL ? t('arabic.support') : 'Arabic Language Support'}
          </h2>
        </div>
      </div>

      {/* Fourth section - Self Storage Made Easy */}
      <div className="min-h-screen flex items-center justify-center relative">
        {/* Self Storage text - Appears after Arabic text fades out */}
        <div
          className="text-center max-w-2xl px-4"
          style={{
            opacity: selfStorageOpacity,
            transition: 'opacity 0.8s ease-in-out',
            willChange: 'opacity'
          }}
        >
          <h2 className={`heading-1 text-blue-950 font-bold mb-4 ${isRTL ? 'font-tajawal' : ''}`}>
            {isRTL ? t('storage.title') : 'Self Storage Made Easy'}
          </h2>
        </div>
      </div>

    </section>
  );
}
