import { cleanEnv, str, url, bool } from 'envalid';
import { log } from './logger';

/**
 * Environment variable validation schema
 * This file validates all environment variables at startup and fails fast if any are missing or invalid
 * 
 * Security Note: 
 * - Never log sensitive environment variables in production
 * - Validation errors are sanitized to prevent exposure of sensitive values
 */

// Validate client-side environment variables (safe to expose to browser)
export const clientEnv = cleanEnv(
  // Use typeof window check to only run validation on the client
  typeof window !== 'undefined' ? process.env : {},
  {
    NEXT_PUBLIC_SUPABASE_URL: url({
      desc: 'Supabase project URL for database connections',
      example: 'https://your-project-id.supabase.co',
      docs: 'https://supabase.com/docs/guides/database'
    }),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: str({
      desc: 'Supabase anonymous key for client-side operations',
      example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      docs: 'https://supabase.com/docs/guides/auth'
    }),
    // Optional client-side variables
    NEXT_PUBLIC_VERCEL_URL: str({
      desc: 'Vercel deployment URL',
      default: '',
      example: 'your-app.vercel.app'
    }),

    // Blockchain RPC Configuration (Optional)
    NEXT_PUBLIC_ALCHEMY_API_KEY: str({
      desc: 'Alchemy API key for Ethereum RPC',
      default: '',
      example: 'your-alchemy-api-key'
    }),
    NEXT_PUBLIC_INFURA_API_KEY: str({
      desc: 'Infura API key for Ethereum RPC',
      default: '',
      example: 'your-infura-api-key'
    }),
    NEXT_PUBLIC_ETHEREUM_RPC_URL: str({
      desc: 'Custom Ethereum RPC URL',
      default: '',
      example: 'https://mainnet.infura.io/v3/your-key'
    }),
    NEXT_PUBLIC_SOLANA_RPC_URL: str({
      desc: 'Solana RPC URL',
      default: 'https://api.mainnet-beta.solana.com',
      example: 'https://api.mainnet-beta.solana.com'
    }),
    NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: str({
      desc: 'WalletConnect project ID',
      default: '',
      example: 'your-walletconnect-project-id'
    }),
    NEXT_PUBLIC_ANALYTICS_ID: str({
      desc: 'Analytics tracking ID',
      default: '',
      example: 'G-XXXXXXXXXX'
    })
  },
  {
    // Only validate on client-side
    reporter: ({ errors, env }) => {
      if (Object.keys(errors).length > 0 && typeof window !== 'undefined') {
        log.error('Invalid client environment variables detected', {
          errorCount: Object.keys(errors).length,
          variables: Object.keys(errors)
        });
        
        Object.entries(errors).forEach(([key, error]) => {
          log.error(`Client environment variable validation failed: ${key}`, {
            variable: key,
            error: error.message
          });
        });
        
        throw new Error('Invalid client environment variables. Check logs for details.');
      }
    }
  }
);

// Validate server-side environment variables (only available on server)
export const serverEnv = cleanEnv(
  // Only validate on server-side
  typeof window === 'undefined' ? process.env : {},
  {
    // Required server-side variables
    NEXT_PUBLIC_SUPABASE_URL: url({
      desc: 'Supabase project URL for database connections',
      example: 'https://your-project-id.supabase.co'
    }),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: str({
      desc: 'Supabase anonymous key for client-side operations',
      example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    }),
    SUPABASE_SERVICE_ROLE_KEY: str({
      desc: 'Supabase service role key for admin operations (SENSITIVE)',
      example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      default: process.env.NODE_ENV === 'development' ? 'dev-placeholder' : undefined
    }),
    NODE_ENV: str({
      desc: 'Application environment',
      choices: ['development', 'staging', 'production'],
      default: 'development'
    }),
    
    // Optional server-side variables
    SESSION_SECRET: str({
      desc: 'Secret key for session encryption',
      default: '',
      example: 'your-32-character-random-string'
    }),
    ALLOWED_ORIGINS: str({
      desc: 'Comma-separated list of allowed CORS origins',
      default: '',
      example: 'https://mokhba.com,https://www.mokhba.com'
    }),
    DATABASE_URL: str({
      desc: 'Direct database connection URL (alternative to Supabase)',
      default: '',
      example: 'postgresql://localhost:5432/mokhba_dev'
    }),
    DEBUG: bool({
      desc: 'Enable debug logging',
      default: false
    }),
    
    // Email service configuration
    EMAIL_SERVICE_API_KEY: str({
      desc: 'Email service API key',
      default: ''
    }),
    EMAIL_FROM: str({
      desc: 'Default email sender address',
      default: ''
    })
  },
  {
    // Custom error reporter that doesn't expose sensitive values
    reporter: ({ errors, env }) => {
      if (Object.keys(errors).length > 0 && typeof window === 'undefined') {
        log.error('Invalid server environment variables detected', {
          errorCount: Object.keys(errors).length,
          variables: Object.keys(errors)
        });
        
        Object.entries(errors).forEach(([key, error]) => {
          log.error(`Environment variable validation failed: ${key}`, {
            variable: key,
            error: error.message,
            // Never log the actual value, only the error
          });
        });
        
        log.error('Environment setup required', {
          instructions: [
            'Copy .env.example to .env.local',
            'Fill in your actual environment variable values',
            'Restart your development server',
            'For production, set these variables in your hosting platform'
          ]
        });
        
        throw new Error('Invalid server environment variables. Check logs for details.');
      }
    }
  }
);

/**
 * Utility function to safely get environment variables with validation
 */
export function getEnvVar(key: string, fallback?: string): string {
  const value = process.env[key];
  
  if (!value && !fallback) {
    log.warn(`Environment variable not set: ${key}`, { variable: key });
    return '';
  }
  
  return value || fallback || '';
}

/**
 * Check if we're in production environment
 */
export const isProduction = () => {
  return typeof window === 'undefined' 
    ? serverEnv.NODE_ENV === 'production'
    : process.env.NODE_ENV === 'production';
};

/**
 * Check if we're in development environment
 */
export const isDevelopment = () => {
  return typeof window === 'undefined'
    ? serverEnv.NODE_ENV === 'development'
    : process.env.NODE_ENV === 'development';
};

/**
 * Get validated environment variables for the current context (client or server)
 */
export const env = typeof window === 'undefined' ? serverEnv : clientEnv;

/**
 * Utility to validate environment at startup
 * Call this early in your application lifecycle
 */
export function validateEnvironment() {
  try {
    if (typeof window === 'undefined') {
      // Server-side validation
      serverEnv;
      log.startup('Server environment variables validated successfully');
    } else {
      // Client-side validation
      clientEnv;
      log.startup('Client environment variables validated successfully');
    }
  } catch (error) {
    log.error('Environment validation failed', { 
      error: error instanceof Error ? error.message : String(error),
      context: typeof window === 'undefined' ? 'server' : 'client'
    });
    throw error;
  }
}

// Export types for TypeScript
export type ClientEnv = typeof clientEnv;
export type ServerEnv = typeof serverEnv; 