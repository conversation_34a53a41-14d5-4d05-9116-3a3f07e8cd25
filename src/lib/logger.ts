/**
 * Secure Structured Logger for Mokhba Wallet
 * 
 * This logger provides environment-aware, secure logging with:
 * - Development vs Production behavior
 * - Sensitive data sanitization
 * - Structured log formatting
 * - Configurable log levels
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export interface LogContext {
  [key: string]: unknown;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  context?: LogContext;
  environment: string;
  service: string;
}

class SecureLogger {
  private isDevelopment: boolean;
  private isProduction: boolean;
  private logLevel: LogLevel;
  private service: string = 'mokhba-wallet';

  // Sensitive data patterns to sanitize
  private sensitivePatterns = [
    /password/i,
    /secret/i,
    /key/i,
    /token/i,
    /auth/i,
    /credential/i,
    /private/i,
    /seed/i,
    /mnemonic/i,
  ];

  // Sensitive value patterns (for values, not keys)
  private sensitiveValuePatterns = [
    /^sk_/, // Stripe secret keys
    /^pk_/, // Stripe public keys (still sensitive in logs)
    /^eyJ/, // JWT tokens
    /^[a-f0-9]{64}$/i, // 64-char hex strings (likely keys)
    /^[a-f0-9]{40}$/i, // 40-char hex strings (likely keys)
  ];

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.isProduction = process.env.NODE_ENV === 'production';
    
    // Set log level based on environment
    if (this.isProduction) {
      this.logLevel = LogLevel.WARN; // Only warnings and errors in production
    } else if (process.env.DEBUG === 'true') {
      this.logLevel = LogLevel.DEBUG; // Full debugging in development
    } else {
      this.logLevel = LogLevel.INFO; // Info level by default
    }
  }

  /**
   * Sanitize sensitive data from context objects
   */
  private sanitizeContext(context: LogContext | unknown): LogContext {
    if (!context || typeof context !== 'object' || Array.isArray(context)) {
      return {};
    }

    const sanitized: LogContext = {};

    for (const [key, value] of Object.entries(context as Record<string, unknown>)) {
      // Check if key indicates sensitive data
      const isSensitiveKey = this.sensitivePatterns.some(pattern => 
        pattern.test(key)
      );

      if (isSensitiveKey) {
        sanitized[key] = '[REDACTED]';
        continue;
      }

      // Check if value looks like sensitive data
      if (typeof value === 'string') {
        const isSensitiveValue = this.sensitiveValuePatterns.some(pattern => 
          pattern.test(value)
        );
        
        if (isSensitiveValue) {
          sanitized[key] = '[REDACTED]';
          continue;
        }
      }

      // Handle nested objects
      if (value && typeof value === 'object') {
        sanitized[key] = this.sanitizeContext(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Create a structured log entry
   */
  private createLogEntry(level: string, message: string, context?: LogContext): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context ? this.sanitizeContext(context) : undefined,
      environment: process.env.NODE_ENV || 'unknown',
      service: this.service,
    };
  }

  /**
   * Output log entry based on environment
   */
  private output(logEntry: LogEntry): void {
    if (this.isProduction) {
      // In production, use structured JSON logging
      console.log(JSON.stringify(logEntry));
    } else {
      // In development, use readable format
      const timestamp = logEntry.timestamp.split('T')[1].split('.')[0];
      const level = logEntry.level.toUpperCase().padEnd(5);
      const message = logEntry.message;
      
      const contextStr = logEntry.context 
        ? `\n  Context: ${JSON.stringify(logEntry.context, null, 2)}`
        : '';

      console.log(`[${timestamp}] ${level} ${message}${contextStr}`);
    }
  }

  /**
   * Log an error message
   */
  error(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.ERROR) {
      const logEntry = this.createLogEntry('error', message, context);
      this.output(logEntry);
    }
  }

  /**
   * Log a warning message
   */
  warn(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.WARN) {
      const logEntry = this.createLogEntry('warn', message, context);
      this.output(logEntry);
    }
  }

  /**
   * Log an info message
   */
  info(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.INFO) {
      const logEntry = this.createLogEntry('info', message, context);
      this.output(logEntry);
    }
  }

  /**
   * Log a debug message (development only)
   */
  debug(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.DEBUG && this.isDevelopment) {
      const logEntry = this.createLogEntry('debug', message, context);
      this.output(logEntry);
    }
  }

  /**
   * Log successful operations
   */
  success(message: string, context?: LogContext): void {
    if (this.logLevel >= LogLevel.INFO) {
      const logEntry = this.createLogEntry('success', message, context);
      this.output(logEntry);
    }
  }

  /**
   * Log application startup events
   */
  startup(message: string, context?: LogContext): void {
    // Startup logs are always shown for monitoring
    const logEntry = this.createLogEntry('startup', message, context);
    this.output(logEntry);
  }

  /**
   * Log security-related events
   */
  security(message: string, context?: LogContext): void {
    // Security logs are always recorded
    const logEntry = this.createLogEntry('security', message, context);
    this.output(logEntry);
  }

  /**
   * Log API requests/responses (development only)
   */
  api(message: string, context?: LogContext): void {
    if (this.isDevelopment && this.logLevel >= LogLevel.DEBUG) {
      const logEntry = this.createLogEntry('api', message, context);
      this.output(logEntry);
    }
  }

  /**
   * Log database operations
   */
  database(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      const logEntry = this.createLogEntry('database', message, context);
      this.output(logEntry);
    } else {
      // In production, only log database errors
      if (message.toLowerCase().includes('error') || message.toLowerCase().includes('failed')) {
        const logEntry = this.createLogEntry('database', message, context);
        this.output(logEntry);
      }
    }
  }

  /**
   * Create a child logger with additional context
   */
  child(defaultContext: LogContext): SecureLogger {
    const childLogger = new SecureLogger();
    
    // Override output to include default context
    const originalOutput = childLogger.output.bind(childLogger);
    childLogger.output = (logEntry: LogEntry) => {
      logEntry.context = {
        ...defaultContext,
        ...logEntry.context,
      };
      originalOutput(logEntry);
    };

    return childLogger;
  }

  /**
   * Get current log level for debugging
   */
  getLogLevel(): LogLevel {
    return this.logLevel;
  }

  /**
   * Check if a specific level would be logged
   */
  willLog(level: LogLevel): boolean {
    return this.logLevel >= level;
  }

  private sanitizeValue(value: unknown): unknown {
    // Handle null and undefined
    if (value === null || value === undefined) {
      return value;
    }

    // Handle strings - check for potential sensitive data
    if (typeof value === 'string') {
      // Redact potential sensitive data patterns
      return this.redactSensitiveStrings(value);
    }

    // Handle arrays
    if (Array.isArray(value)) {
      return value.map(item => this.sanitizeValue(item));
    }

    // Handle objects
    if (value && typeof value === 'object') {
      const sanitized: Record<string, unknown> = {};
      for (const [key, val] of Object.entries(value)) {
        if (this.isSensitiveKey(key)) {
          sanitized[key] = '[REDACTED]';
        } else {
          sanitized[key] = this.sanitizeValue(val);
        }
      }
      return sanitized;
    }

    // Return primitives as-is
    return value;
  }

  /**
   * Check for sensitive keys using patterns
   */
  private isSensitiveKey(key: string): boolean {
    return this.sensitivePatterns.some(pattern => pattern.test(key));
  }

  /**
   * Redact sensitive strings using patterns
   */
  private redactSensitiveStrings(value: string): string {
    const isSensitive = this.sensitiveValuePatterns.some(pattern => pattern.test(value));
    return isSensitive ? '[REDACTED]' : value;
  }

  /**
   * Development trace helper
   */
  trace(obj: unknown, label?: string): void {
    if (process.env.NODE_ENV === 'development') {
      const sanitized = this.sanitizeContext(obj);
      if (label) {
        console.log(`🔍 ${label}:`, sanitized);
      } else {
        console.log('🔍 Trace:', sanitized);
      }
    }
  }
}

// Export singleton instance
export const logger = new SecureLogger();

// Export convenience functions for common use cases
export const log = {
  error: (message: string, context?: LogContext) => logger.error(message, context),
  warn: (message: string, context?: LogContext) => logger.warn(message, context),
  info: (message: string, context?: LogContext) => logger.info(message, context),
  debug: (message: string, context?: LogContext) => logger.debug(message, context),
  success: (message: string, context?: LogContext) => logger.success(message, context),
  startup: (message: string, context?: LogContext) => logger.startup(message, context),
  security: (message: string, context?: LogContext) => logger.security(message, context),
  api: (message: string, context?: LogContext) => logger.api(message, context),
  database: (message: string, context?: LogContext) => logger.database(message, context),
};

// Export logger class for advanced usage
export { SecureLogger };

// Development helper - only available in development
export const devLog = process.env.NODE_ENV === 'development' 
  ? {
      trace: (obj: unknown, label?: string) => {
        const sanitized = logger['sanitizeContext'](obj);
        console.log(label ? `🔍 ${label}:` : '🔍 Trace:', sanitized);
      },
      time: (label: string) => console.time(label),
      timeEnd: (label: string) => console.timeEnd(label),
    }
  : {
      trace: () => {},
      time: () => {},
      timeEnd: () => {},
    }; 