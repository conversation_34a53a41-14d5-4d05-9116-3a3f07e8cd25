/**
 * Asset Management System for Mokhba Wallet
 * Handles cryptocurrency assets, tokens, and their metadata
 */

import { CryptoAsset, ChainType } from '@/types';

// Popular cryptocurrency assets with their metadata
export const POPULAR_ASSETS: Record<string, Omit<CryptoAsset, 'balance' | 'usdValue'>> = {
  // Ethereum Mainnet
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    decimals: 18,
    chainType: 'ethereum',
    logoUrl: '/ethereum-eth-logo.svg'
  },
  USDC: {
    symbol: 'USDC',
    name: 'USD Coin',
    address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    decimals: 6,
    chainType: 'ethereum',
    logoUrl: '/usd-coin-usdc-logo.svg'
  },
  USDT: {
    symbol: 'USDT',
    name: 'Tether USD',
    address: '******************************************',
    decimals: 6,
    chainType: 'ethereum',
    logoUrl: '/tether-usdt-logo.svg'
  },
  WBTC: {
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    address: '******************************************',
    decimals: 8,
    chainType: 'ethereum',
    logoUrl: '/wrapped_bitcoin_wbtc_logo_5318368b91.svg'
  },
  DAI: {
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    address: '******************************************',
    decimals: 18,
    chainType: 'ethereum',
    logoUrl: '/multi-collateral-dai-dai-logo.svg'
  },

  // Polygon
  MATIC: {
    symbol: 'MATIC',
    name: 'Polygon',
    decimals: 18,
    chainType: 'polygon',
    logoUrl: '/Polygon_Blockchain_Matic_Logo.svg'
  },

  // Solana
  SOL: {
    symbol: 'SOL',
    name: 'Solana',
    decimals: 9,
    chainType: 'solana',
    logoUrl: '/solana-sol-logo.svg'
  }
};

// Chain-specific native assets
export const NATIVE_ASSETS: Record<ChainType, string> = {
  ethereum: 'ETH',
  polygon: 'MATIC',
  solana: 'SOL',
  bsc: 'BNB'
};

// Chain metadata
export const CHAIN_INFO: Record<ChainType, { name: string; logoUrl: string; chainId?: number }> = {
  ethereum: {
    name: 'Ethereum',
    logoUrl: '/ethereum-eth-logo.svg',
    chainId: 1
  },
  polygon: {
    name: 'Polygon',
    logoUrl: '/Polygon_Blockchain_Matic_Logo.svg',
    chainId: 137
  },
  solana: {
    name: 'Solana',
    logoUrl: '/solana-sol-logo.svg'
  },
  bsc: {
    name: 'BNB Smart Chain',
    logoUrl: '/bnb-bnb-logo.svg',
    chainId: 56
  }
};

/**
 * Get asset metadata by symbol
 */
export function getAssetMetadata(symbol: string): Omit<CryptoAsset, 'balance' | 'usdValue'> | null {
  return POPULAR_ASSETS[symbol.toUpperCase()] || null;
}

/**
 * Get native asset for a chain
 */
export function getNativeAsset(chainType: ChainType): string {
  return NATIVE_ASSETS[chainType];
}

/**
 * Get chain information
 */
export function getChainInfo(chainType: ChainType): { name: string; logoUrl: string; chainId?: number } {
  return CHAIN_INFO[chainType];
}

/**
 * Get assets for a specific chain
 */
export function getAssetsForChain(chainType: ChainType): Array<Omit<CryptoAsset, 'balance' | 'usdValue'>> {
  return Object.values(POPULAR_ASSETS).filter(asset => asset.chainType === chainType);
}

/**
 * Format asset balance for display
 */
export function formatAssetBalance(balance: string, decimals: number, maxDecimals: number = 6): string {
  const num = parseFloat(balance);
  if (num === 0) return '0';
  
  // For very small amounts, show more decimals
  if (num < 0.001) {
    return num.toFixed(8);
  }
  
  // For normal amounts, limit decimals
  return num.toFixed(Math.min(maxDecimals, decimals));
}

/**
 * Format USD value for display
 */
export function formatUSDValue(usdValue: number): string {
  if (usdValue < 0.01) return '<$0.01';
  if (usdValue < 1) return `$${usdValue.toFixed(3)}`;
  if (usdValue < 1000) return `$${usdValue.toFixed(2)}`;
  if (usdValue < 1000000) return `$${(usdValue / 1000).toFixed(1)}K`;
  return `$${(usdValue / 1000000).toFixed(1)}M`;
}

/**
 * Create a complete asset object with balance and USD value
 */
export function createAsset(
  symbol: string,
  balance: string,
  usdValue?: number,
  customMetadata?: Partial<CryptoAsset>
): CryptoAsset {
  const metadata = getAssetMetadata(symbol);
  
  if (!metadata && !customMetadata) {
    throw new Error(`Unknown asset: ${symbol}`);
  }

  return {
    ...metadata,
    ...customMetadata,
    symbol: symbol.toUpperCase(),
    balance,
    usdValue
  } as CryptoAsset;
}

/**
 * Validate if an asset is supported
 */
export function isAssetSupported(symbol: string): boolean {
  return symbol.toUpperCase() in POPULAR_ASSETS;
}

/**
 * Get fallback logo URL for unknown assets
 */
export function getFallbackLogoUrl(symbol: string): string {
  return `https://via.placeholder.com/32x32/6366f1/ffffff?text=${symbol.charAt(0)}`;
}

/**
 * Safe get asset logo URL with fallback
 */
export function getAssetLogoUrl(asset: CryptoAsset): string {
  return asset.logoUrl || getFallbackLogoUrl(asset.symbol);
}

/**
 * Get optimized chain logo URL with fallback
 */
export function getChainLogoUrl(chainType: ChainType): string {
  const chainInfo = getChainInfo(chainType);
  return chainInfo.logoUrl;
}

/**
 * Get optimized asset logo with local file preference
 */
export function getOptimizedAssetLogo(symbol: string): string {
  const asset = getAssetMetadata(symbol);
  if (!asset) {
    return getFallbackLogoUrl(symbol);
  }
  return asset.logoUrl || getFallbackLogoUrl(symbol);
}

/**
 * Sort assets by USD value (descending) and then by symbol
 */
export function sortAssets(assets: CryptoAsset[]): CryptoAsset[] {
  return assets.sort((a, b) => {
    // First sort by USD value (if available)
    if (a.usdValue && b.usdValue) {
      return b.usdValue - a.usdValue;
    }
    if (a.usdValue && !b.usdValue) return -1;
    if (!a.usdValue && b.usdValue) return 1;
    
    // Then sort by balance (numeric)
    const balanceA = parseFloat(a.balance) || 0;
    const balanceB = parseFloat(b.balance) || 0;
    if (balanceA !== balanceB) {
      return balanceB - balanceA;
    }
    
    // Finally sort by symbol alphabetically
    return a.symbol.localeCompare(b.symbol);
  });
}
