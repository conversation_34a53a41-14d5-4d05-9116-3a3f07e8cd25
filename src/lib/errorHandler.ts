/**
 * Centralized Error Handling System for Mokhba Wallet
 * Provides consistent error handling, logging, user notifications, retry logic, and graceful degradation
 */

import { log } from './logger';
import toast from 'react-hot-toast';

export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  API = 'API',
  WALLET = 'WALLET',
  BLOCKCHAIN = 'BLOCKCHAIN',
  UI = 'UI',
  DATABASE = 'DATABASE',
  RATE_LIMIT = 'RATE_LIMIT',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // in milliseconds
  maxDelay: number;
  backoffFactor: number;
  retryableErrorCodes?: string[];
  retryableErrorTypes?: ErrorType[];
}

export interface CircuitBreakerState {
  failureCount: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  threshold: number;
  timeout: number; // ms before attempting to close circuit
}

export interface ErrorContext {
  userId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, unknown>;
  userAgent?: string;
  url?: string;
  timestamp?: number;
  retryAttempt?: number;
  circuitBreakerKey?: string;
}

export interface HandledError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userFriendlyMessage: string;
  originalError?: Error;
  context?: ErrorContext;
  shouldNotifyUser: boolean;
  shouldLog: boolean;
  isRetryable: boolean;
  retryConfig?: RetryConfig;
  timestamp: number;
}

export interface ErrorRecoveryStrategy {
  fallbackValue?: unknown;
  fallbackFunction?: () => Promise<unknown> | unknown;
  gracefulDegradation?: boolean;
  userNotification?: string;
}

// Default retry configurations for different error types
const DEFAULT_RETRY_CONFIGS: Record<ErrorType, RetryConfig> = {
  [ErrorType.NETWORK]: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableErrorCodes: ['ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND', 'EAI_AGAIN']
  },
  [ErrorType.API]: {
    maxAttempts: 3,
    baseDelay: 500,
    maxDelay: 5000,
    backoffFactor: 1.5,
    retryableErrorCodes: ['500', '502', '503', '504', '408', '429']
  },
  [ErrorType.DATABASE]: {
    maxAttempts: 2,
    baseDelay: 1000,
    maxDelay: 3000,
    backoffFactor: 2,
    retryableErrorCodes: ['40001', 'serialization_failure', 'deadlock_detected']
  },
  [ErrorType.WALLET]: {
    maxAttempts: 2,
    baseDelay: 2000,
    maxDelay: 5000,
    backoffFactor: 1.5
  },
  [ErrorType.BLOCKCHAIN]: {
    maxAttempts: 3,
    baseDelay: 3000,
    maxDelay: 15000,
    backoffFactor: 2
  },
  [ErrorType.RATE_LIMIT]: {
    maxAttempts: 1, // Don't retry rate limit errors
    baseDelay: 0,
    maxDelay: 0,
    backoffFactor: 1
  },
  [ErrorType.AUTHENTICATION]: {
    maxAttempts: 1, // Don't retry auth errors
    baseDelay: 0,
    maxDelay: 0,
    backoffFactor: 1
  },
  [ErrorType.AUTHORIZATION]: {
    maxAttempts: 1, // Don't retry authorization errors
    baseDelay: 0,
    maxDelay: 0,
    backoffFactor: 1
  },
  [ErrorType.VALIDATION]: {
    maxAttempts: 1, // Don't retry validation errors
    baseDelay: 0,
    maxDelay: 0,
    backoffFactor: 1
  },
  [ErrorType.UI]: {
    maxAttempts: 2,
    baseDelay: 500,
    maxDelay: 2000,
    backoffFactor: 2
  },
  [ErrorType.UNKNOWN]: {
    maxAttempts: 1,
    baseDelay: 1000,
    maxDelay: 1000,
    backoffFactor: 1
  }
};

class ErrorHandler {
  private static instance: ErrorHandler;
  private errorHistory: HandledError[] = [];
  private maxHistorySize = 100;
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private retryQueues: Map<string, Promise<unknown>> = new Map();

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle different types of errors with retry logic and fallback strategies
   */
  async handleErrorWithRetry<T>(
    operation: () => Promise<T>,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: ErrorContext,
    retryConfig?: Partial<RetryConfig>,
    recoveryStrategy?: ErrorRecoveryStrategy
  ): Promise<T> {
    const finalRetryConfig = { ...DEFAULT_RETRY_CONFIGS[type], ...retryConfig };
    const circuitBreakerKey = context?.circuitBreakerKey || `${type}_${context?.component || 'default'}`;

    // Check circuit breaker
    if (this.isCircuitOpen(circuitBreakerKey)) {
      return this.handleCircuitBreakerOpen(type, severity, context, recoveryStrategy);
    }

    for (let attempt = 1; attempt <= finalRetryConfig.maxAttempts; attempt++) {
      try {
        const result = await operation();
        
        // Reset circuit breaker on success
        this.resetCircuitBreaker(circuitBreakerKey);
        
        return result;
      } catch (error) {
        const handledError = this.handleError(
          error as Error,
          type,
          severity,
          { ...context, retryAttempt: attempt },
          attempt === finalRetryConfig.maxAttempts // Only notify on final attempt
        );

        // Update circuit breaker
        this.updateCircuitBreaker(circuitBreakerKey, handledError);

        // Check if error is retryable
        if (!this.isRetryableError(handledError, finalRetryConfig) || attempt === finalRetryConfig.maxAttempts) {
          // Apply recovery strategy if available
          if (recoveryStrategy) {
            return this.applyRecoveryStrategy(recoveryStrategy, handledError);
          }
          throw error;
        }

        // Calculate delay for next attempt
        const delay = Math.min(
          finalRetryConfig.baseDelay * Math.pow(finalRetryConfig.backoffFactor, attempt - 1),
          finalRetryConfig.maxDelay
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + Math.random() * delay * 0.1;

        log.info('Retrying operation after error', {
          errorId: handledError.id,
          attempt,
          maxAttempts: finalRetryConfig.maxAttempts,
          delay: jitteredDelay,
          errorType: type,
          context: context?.component
        });

        await this.delay(jitteredDelay);
      }
    }

    throw new Error('Max retry attempts exceeded');
  }

  /**
   * Standard error handling without retry logic
   */
  handleError(
    error: Error | string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: ErrorContext,
    shouldNotifyUser: boolean = true
  ): HandledError {
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = Date.now();

    const handledError: HandledError = {
      id: errorId,
      type,
      severity,
      message: typeof error === 'string' ? error : error.message,
      userFriendlyMessage: this.getUserFriendlyMessage(type, typeof error === 'string' ? error : error.message),
      originalError: typeof error === 'string' ? undefined : error,
      context: {
        ...context,
        timestamp,
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server'
      },
      shouldNotifyUser,
      shouldLog: true,
      isRetryable: this.isRetryableError({ type } as HandledError, DEFAULT_RETRY_CONFIGS[type]),
      retryConfig: DEFAULT_RETRY_CONFIGS[type],
      timestamp
    };

    // Log the error using our secure logger
    this.logError(handledError);

    // Show user notification if required
    if (shouldNotifyUser && typeof window !== 'undefined') {
      this.showUserNotification(handledError);
    }

    // Store in history for debugging
    this.addToHistory(handledError);

    // Report to external services in production
    if (process.env.NODE_ENV === 'production') {
      this.reportToExternalService(handledError);
    }

    return handledError;
  }

  /**
   * Circuit breaker implementation
   */
  private isCircuitOpen(key: string): boolean {
    const breaker = this.circuitBreakers.get(key);
    if (!breaker) return false;

    if (breaker.state === 'OPEN') {
      // Check if timeout has passed to attempt half-open
      if (Date.now() - breaker.lastFailureTime > breaker.timeout) {
        breaker.state = 'HALF_OPEN';
        this.circuitBreakers.set(key, breaker);
        return false;
      }
      return true;
    }

    return false;
  }

  private updateCircuitBreaker(key: string, error: HandledError): void {
    let breaker = this.circuitBreakers.get(key) || {
      failureCount: 0,
      lastFailureTime: 0,
      state: 'CLOSED',
      threshold: 5,
      timeout: 60000 // 1 minute
    };

    breaker.failureCount++;
    breaker.lastFailureTime = Date.now();

    if (breaker.failureCount >= breaker.threshold) {
      breaker.state = 'OPEN';
      log.warn('Circuit breaker opened', {
        key,
        failureCount: breaker.failureCount,
        threshold: breaker.threshold,
        errorType: error.type
      });
    }

    this.circuitBreakers.set(key, breaker);
  }

  private resetCircuitBreaker(key: string): void {
    const breaker = this.circuitBreakers.get(key);
    if (breaker) {
      breaker.failureCount = 0;
      breaker.state = 'CLOSED';
      this.circuitBreakers.set(key, breaker);
    }
  }

  private async handleCircuitBreakerOpen<T>(
    type: ErrorType,
    severity: ErrorSeverity,
    context?: ErrorContext,
    recoveryStrategy?: ErrorRecoveryStrategy
  ): Promise<T> {
    const error = new Error('Service temporarily unavailable - circuit breaker open');
    const handledError = this.handleError(error, type, severity, context, false);

    if (recoveryStrategy) {
      return this.applyRecoveryStrategy(recoveryStrategy, handledError);
    }

    throw error;
  }

  /**
   * Recovery strategy implementation
   */
  private async applyRecoveryStrategy<T>(
    strategy: ErrorRecoveryStrategy,
    error: HandledError
  ): Promise<T> {
    log.info('Applying recovery strategy', {
      errorId: error.id,
      errorType: error.type,
      hasFunction: !!strategy.fallbackFunction,
      hasValue: strategy.fallbackValue !== undefined,
      gracefulDegradation: strategy.gracefulDegradation
    });

    // Show custom user notification if provided
    if (strategy.userNotification && typeof window !== 'undefined') {
      toast.error(strategy.userNotification, {
        duration: 5000,
        icon: '⚠️'
      });
    }

    // Try fallback function first
    if (strategy.fallbackFunction) {
      try {
        const result = await strategy.fallbackFunction();
        return result as T;
      } catch (fallbackError) {
        log.warn('Fallback function failed', {
          originalErrorId: error.id,
          fallbackError: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
        });
      }
    }

    // Use fallback value if provided
    if (strategy.fallbackValue !== undefined) {
      return strategy.fallbackValue as T;
    }

    // If graceful degradation is enabled, return a safe default
    if (strategy.gracefulDegradation) {
      log.info('Graceful degradation applied', { errorId: error.id });
      return null as T;
    }

    // No recovery possible, re-throw
    throw error.originalError || new Error(error.message);
  }

  /**
   * Check if an error is retryable based on configuration
   */
  private isRetryableError(error: Pick<HandledError, 'type' | 'originalError'>, config: RetryConfig): boolean {
    // Check if error type is retryable
    if (config.retryableErrorTypes?.includes(error.type)) {
      return true;
    }

    // Check specific error codes
    if (config.retryableErrorCodes && error.originalError) {
      const errorCode = this.extractErrorCode(error.originalError);
      return config.retryableErrorCodes.includes(errorCode);
    }

    // Default retryable check based on error type
    return config.maxAttempts > 1;
  }

  /**
   * Extract error code from various error formats
   */
  private extractErrorCode(error: Error): string {
    // Handle different error formats
    if ('code' in error) {
      return String((error as Error & { code: string }).code);
    }
    
    if ('status' in error) {
      return String((error as Error & { status: number }).status);
    }

    // Check for network error codes in message
    const networkErrorCodes = ['ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND', 'EAI_AGAIN'];
    for (const code of networkErrorCodes) {
      if (error.message.includes(code)) {
        return code;
      }
    }

    return 'UNKNOWN';
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log error with appropriate level based on severity
   */
  private logError(handledError: HandledError) {
    const logData = {
      errorId: handledError.id,
      type: handledError.type,
      severity: handledError.severity,
      message: handledError.message,
      stack: handledError.originalError?.stack,
      context: handledError.context
    };

    switch (handledError.severity) {
      case ErrorSeverity.CRITICAL:
        log.error('Critical error occurred', logData);
        break;
      case ErrorSeverity.HIGH:
        log.error('High severity error occurred', logData);
        break;
      case ErrorSeverity.MEDIUM:
        log.warn('Medium severity error occurred', logData);
        break;
      case ErrorSeverity.LOW:
        log.info('Low severity error occurred', logData);
        break;
    }
  }

  /**
   * Show appropriate user notification based on error type and severity
   */
  private showUserNotification(handledError: HandledError) {
    const userMessage = this.getUserFriendlyMessage(handledError.type, handledError.message);
    
    switch (handledError.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        toast.error(userMessage, {
          duration: 8000,
          icon: '🚨'
        });
        break;
      case ErrorSeverity.MEDIUM:
        toast.error(userMessage, {
          duration: 5000
        });
        break;
      case ErrorSeverity.LOW:
        toast(userMessage, {
          duration: 3000,
          icon: '⚠️'
        });
        break;
    }
  }

  /**
   * Convert technical errors to user-friendly messages
   */
  private getUserFriendlyMessage(type: ErrorType, message: string): string {
    switch (type) {
      case ErrorType.NETWORK:
        return 'Network connection issue. Please check your internet connection and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Authentication failed. Please log in again.';
      case ErrorType.AUTHORIZATION:
        return 'You do not have permission to perform this action.';
      case ErrorType.VALIDATION:
        return message; // Validation messages are usually user-friendly
      case ErrorType.WALLET:
        return 'Wallet error occurred. Please check your wallet connection.';
      case ErrorType.BLOCKCHAIN:
        return 'Blockchain transaction failed. Please try again.';
      case ErrorType.API:
        return 'Service temporarily unavailable. Please try again in a moment.';
      case ErrorType.UI:
        return 'Interface error occurred. Please refresh the page.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Add error to history for debugging and analysis
   */
  private addToHistory(handledError: HandledError) {
    this.errorHistory.unshift(handledError);
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Report to external error tracking service
   */
  private reportToExternalService(handledError: HandledError) {
    try {
      // Placeholder for external error reporting service
      // This could integrate with Sentry, LogRocket, Bugsnag, etc.
      log.info('Error reported to external service', {
        errorId: handledError.id,
        service: 'placeholder'
      });
    } catch (reportingError) {
      log.error('Failed to report error to external service', {
        errorId: handledError.id,
        reportingError: reportingError instanceof Error ? reportingError.message : String(reportingError)
      });
    }
  }

  /**
   * Get error history for debugging
   */
  getErrorHistory(): HandledError[] {
    return [...this.errorHistory];
  }

  /**
   * Clear error history
   */
  clearHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const total = this.errorHistory.length;
    const byType = this.errorHistory.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const bySeverity = this.errorHistory.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return { total, byType, bySeverity };
  }
}

// Create singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Convenience functions for common error types
export const handleNetworkError = (error: Error | string, context?: ErrorContext) =>
  errorHandler.handleError(error, ErrorType.NETWORK, ErrorSeverity.MEDIUM, context);

export const handleAuthError = (error: Error | string, context?: ErrorContext) =>
  errorHandler.handleError(error, ErrorType.AUTHENTICATION, ErrorSeverity.HIGH, context);

export const handleWalletError = (error: Error | string, context?: ErrorContext) =>
  errorHandler.handleError(error, ErrorType.WALLET, ErrorSeverity.HIGH, context);

export const handleAPIError = (error: Error | string, context?: ErrorContext) =>
  errorHandler.handleError(error, ErrorType.API, ErrorSeverity.MEDIUM, context);

export const handleValidationError = (error: Error | string, context?: ErrorContext) =>
  errorHandler.handleError(error, ErrorType.VALIDATION, ErrorSeverity.LOW, context, false);

export const handleCriticalError = (error: Error | string, context?: ErrorContext) =>
  errorHandler.handleError(error, ErrorType.UNKNOWN, ErrorSeverity.CRITICAL, context);

// Enhanced convenience functions with retry support
export const handleNetworkErrorWithRetry = async <T>(
  operation: () => Promise<T>,
  context?: ErrorContext,
  retryConfig?: Partial<RetryConfig>,
  recoveryStrategy?: ErrorRecoveryStrategy
): Promise<T> => {
  return errorHandler.handleErrorWithRetry(
    operation,
    ErrorType.NETWORK,
    ErrorSeverity.MEDIUM,
    context,
    retryConfig,
    recoveryStrategy
  );
};

export const handleAPIErrorWithRetry = async <T>(
  operation: () => Promise<T>,
  context?: ErrorContext,
  retryConfig?: Partial<RetryConfig>,
  recoveryStrategy?: ErrorRecoveryStrategy
): Promise<T> => {
  return errorHandler.handleErrorWithRetry(
    operation,
    ErrorType.API,
    ErrorSeverity.MEDIUM,
    context,
    retryConfig,
    recoveryStrategy
  );
};

export const handleDatabaseErrorWithRetry = async <T>(
  operation: () => Promise<T>,
  context?: ErrorContext,
  retryConfig?: Partial<RetryConfig>,
  recoveryStrategy?: ErrorRecoveryStrategy
): Promise<T> => {
  return errorHandler.handleErrorWithRetry(
    operation,
    ErrorType.DATABASE,
    ErrorSeverity.HIGH,
    context,
    retryConfig,
    recoveryStrategy
  );
};

// React hook for error handling with retry support
export const useErrorHandler = () => {
  return {
    handleError: errorHandler.handleError.bind(errorHandler),
    handleErrorWithRetry: errorHandler.handleErrorWithRetry.bind(errorHandler),
    handleNetworkError,
    handleAuthError,
    handleWalletError,
    handleAPIError,
    handleValidationError,
    handleCriticalError,
    handleNetworkErrorWithRetry,
    handleAPIErrorWithRetry,
    handleDatabaseErrorWithRetry,
    getErrorHistory: errorHandler.getErrorHistory.bind(errorHandler),
    getErrorStats: errorHandler.getErrorStats.bind(errorHandler)
  };
}; 