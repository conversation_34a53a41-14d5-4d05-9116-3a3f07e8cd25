/**
 * Wallet Management System for Mokhba Wallet
 * Handles multiple wallets, addresses, and their metadata
 */

import { WalletConnection, WalletProvider, ChainType } from '@/types';

export interface ManagedWallet {
  id: string;
  name: string;
  address: string;
  provider: WalletProvider;
  chainType: ChainType;
  chainId?: number;
  isConnected: boolean;
  balance?: string;
  isDefault?: boolean;
  createdAt: Date;
  lastUsed?: Date;
}

export interface WalletGroup {
  chainType: ChainType;
  wallets: ManagedWallet[];
}

// Local storage keys
const STORAGE_KEYS = {
  WALLETS: 'mokhba_wallets',
  DEFAULT_WALLET: 'mokhba_default_wallet'
};

/**
 * Wallet Manager Class
 * Handles wallet storage, retrieval, and management
 */
export class WalletManager {
  private static instance: WalletManager;
  private wallets: ManagedWallet[] = [];

  private constructor() {
    this.loadWallets();
  }

  static getInstance(): WalletManager {
    if (!WalletManager.instance) {
      WalletManager.instance = new WalletManager();
    }
    return WalletManager.instance;
  }

  /**
   * Load wallets from localStorage
   */
  private loadWallets(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem(STORAGE_KEYS.WALLETS);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.wallets = parsed.map((w: any) => ({
          ...w,
          createdAt: new Date(w.createdAt),
          lastUsed: w.lastUsed ? new Date(w.lastUsed) : undefined
        }));
      }
    } catch (error) {
      console.error('Failed to load wallets from storage:', error);
      this.wallets = [];
    }
  }

  /**
   * Save wallets to localStorage
   */
  private saveWallets(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEYS.WALLETS, JSON.stringify(this.wallets));
    } catch (error) {
      console.error('Failed to save wallets to storage:', error);
    }
  }

  /**
   * Add a new wallet
   */
  addWallet(wallet: Omit<ManagedWallet, 'id' | 'createdAt'>): ManagedWallet {
    const newWallet: ManagedWallet = {
      ...wallet,
      id: this.generateWalletId(),
      createdAt: new Date(),
      lastUsed: new Date()
    };

    // Check if this is the first wallet of its type
    const existingWallets = this.getWalletsByChain(wallet.chainType);
    if (existingWallets.length === 0) {
      newWallet.isDefault = true;
    }

    this.wallets.push(newWallet);
    this.saveWallets();
    return newWallet;
  }

  /**
   * Update wallet information
   */
  updateWallet(id: string, updates: Partial<ManagedWallet>): ManagedWallet | null {
    const index = this.wallets.findIndex(w => w.id === id);
    if (index === -1) return null;

    this.wallets[index] = {
      ...this.wallets[index],
      ...updates,
      lastUsed: new Date()
    };

    this.saveWallets();
    return this.wallets[index];
  }

  /**
   * Remove a wallet
   */
  removeWallet(id: string): boolean {
    const index = this.wallets.findIndex(w => w.id === id);
    if (index === -1) return false;

    const removedWallet = this.wallets[index];
    this.wallets.splice(index, 1);

    // If this was the default wallet, set another as default
    if (removedWallet.isDefault) {
      const sameChainWallets = this.getWalletsByChain(removedWallet.chainType);
      if (sameChainWallets.length > 0) {
        this.setDefaultWallet(sameChainWallets[0].id);
      }
    }

    this.saveWallets();
    return true;
  }

  /**
   * Get all wallets
   */
  getAllWallets(): ManagedWallet[] {
    return [...this.wallets];
  }

  /**
   * Get wallets by chain type
   */
  getWalletsByChain(chainType: ChainType): ManagedWallet[] {
    return this.wallets.filter(w => w.chainType === chainType);
  }

  /**
   * Get wallet by ID
   */
  getWallet(id: string): ManagedWallet | null {
    return this.wallets.find(w => w.id === id) || null;
  }

  /**
   * Get wallet by address
   */
  getWalletByAddress(address: string): ManagedWallet | null {
    return this.wallets.find(w => w.address.toLowerCase() === address.toLowerCase()) || null;
  }

  /**
   * Get default wallet for a chain
   */
  getDefaultWallet(chainType: ChainType): ManagedWallet | null {
    return this.wallets.find(w => w.chainType === chainType && w.isDefault) || null;
  }

  /**
   * Set default wallet
   */
  setDefaultWallet(id: string): boolean {
    const wallet = this.getWallet(id);
    if (!wallet) return false;

    // Remove default flag from other wallets of the same chain
    this.wallets.forEach(w => {
      if (w.chainType === wallet.chainType) {
        w.isDefault = false;
      }
    });

    // Set new default
    wallet.isDefault = true;
    wallet.lastUsed = new Date();

    this.saveWallets();
    return true;
  }

  /**
   * Get wallets grouped by chain
   */
  getWalletsGrouped(): WalletGroup[] {
    const groups: Record<ChainType, ManagedWallet[]> = {
      ethereum: [],
      polygon: [],
      solana: [],
      bsc: []
    };

    this.wallets.forEach(wallet => {
      groups[wallet.chainType].push(wallet);
    });

    return Object.entries(groups)
      .filter(([_, wallets]) => wallets.length > 0)
      .map(([chainType, wallets]) => ({
        chainType: chainType as ChainType,
        wallets: wallets.sort((a, b) => {
          // Default wallet first, then by last used
          if (a.isDefault && !b.isDefault) return -1;
          if (!a.isDefault && b.isDefault) return 1;
          
          const aTime = a.lastUsed?.getTime() || 0;
          const bTime = b.lastUsed?.getTime() || 0;
          return bTime - aTime;
        })
      }));
  }

  /**
   * Generate unique wallet ID
   */
  private generateWalletId(): string {
    return `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate wallet name
   */
  generateWalletName(chainType: ChainType, provider: WalletProvider): string {
    const existingCount = this.getWalletsByChain(chainType).length;
    const providerName = provider.charAt(0).toUpperCase() + provider.slice(1);
    
    if (existingCount === 0) {
      return `${providerName} Wallet`;
    }
    
    return `${providerName} Wallet ${existingCount + 1}`;
  }

  /**
   * Import wallet from connection
   */
  importFromConnection(connection: WalletConnection, customName?: string): ManagedWallet {
    // Check if wallet already exists
    const existing = this.getWalletByAddress(connection.address);
    if (existing) {
      // Update existing wallet
      return this.updateWallet(existing.id, {
        isConnected: connection.isConnected,
        balance: connection.balance
      })!;
    }

    // Create new wallet
    const name = customName || this.generateWalletName(connection.chainType, connection.provider);
    
    return this.addWallet({
      name,
      address: connection.address,
      provider: connection.provider,
      chainType: connection.chainType,
      chainId: connection.chainId,
      isConnected: connection.isConnected,
      balance: connection.balance
    });
  }

  /**
   * Clear all wallets (for testing/reset)
   */
  clearAllWallets(): void {
    this.wallets = [];
    this.saveWallets();
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEYS.DEFAULT_WALLET);
    }
  }

  /**
   * Export wallets for backup
   */
  exportWallets(): string {
    return JSON.stringify(this.wallets, null, 2);
  }

  /**
   * Import wallets from backup
   */
  importWallets(data: string): boolean {
    try {
      const imported = JSON.parse(data);
      if (Array.isArray(imported)) {
        this.wallets = imported.map((w: any) => ({
          ...w,
          createdAt: new Date(w.createdAt),
          lastUsed: w.lastUsed ? new Date(w.lastUsed) : undefined
        }));
        this.saveWallets();
        return true;
      }
    } catch (error) {
      console.error('Failed to import wallets:', error);
    }
    return false;
  }
}

// Export singleton instance
export const walletManager = WalletManager.getInstance();
