import NodeCache from 'node-cache';
import { NextRequest, NextResponse } from 'next/server';
import { log } from './logger';

// Create cache instances for different rate limiting strategies
const requestCache = new NodeCache({ stdTTL: 60 }); // 1 minute default TTL
const ipBanCache = new NodeCache({ stdTTL: 3600 }); // 1 hour ban TTL

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (request: NextRequest) => string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  message?: string;
}

/**
 * Default key generator - uses IP address
 */
function defaultKeyGenerator(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwarded?.split(',')[0] || realIp || 'unknown';
  return `rate_limit:${ip}`;
}

/**
 * Check if an IP is banned
 */
function isIpBanned(ip: string): boolean {
  return ipBanCache.has(`ban:${ip}`);
}

/**
 * Ban an IP address temporarily
 */
function banIp(ip: string, durationMs: number = 3600000): void {
  ipBanCache.set(`ban:${ip}`, true, durationMs / 1000);
  log.security('IP address banned due to rate limit violations', {
    ip,
    duration: durationMs,
    context: 'rate_limiting'
  });
}

/**
 * Rate limiting implementation
 */
export function rateLimit(config: RateLimitConfig) {
  return (request: NextRequest): RateLimitResult => {
    const keyGenerator = config.keyGenerator || defaultKeyGenerator;
    const key = keyGenerator(request);
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // Check if IP is banned
    if (isIpBanned(ip)) {
      log.security('Blocked request from banned IP', {
        ip,
        endpoint: request.url,
        context: 'rate_limiting'
      });
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + 3600000, // 1 hour
        message: 'IP address temporarily banned due to rate limit violations'
      };
    }

    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Get existing requests for this key
    const requests: number[] = requestCache.get(key) || [];
    
    // Filter out requests outside the current window
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if rate limit is exceeded
    if (validRequests.length >= config.maxRequests) {
      // Log rate limit violation
      log.security('Rate limit exceeded', {
        ip,
        endpoint: request.url,
        requestCount: validRequests.length,
        maxRequests: config.maxRequests,
        windowMs: config.windowMs,
        context: 'rate_limiting'
      });

      // Ban IP if they exceed limits multiple times
      const banKey = `violations:${ip}`;
      const violations = (requestCache.get(banKey) as number) || 0;
      
      if (violations >= 5) { // Ban after 5 violations
        banIp(ip);
        requestCache.del(banKey);
      } else {
        requestCache.set(banKey, violations + 1, 300); // 5 minutes
      }
      
      const resetTime = validRequests[0] + config.windowMs;
      
      return {
        allowed: false,
        remaining: 0,
        resetTime,
        message: config.message || `Rate limit exceeded. Try again in ${Math.ceil((resetTime - now) / 1000)} seconds.`
      };
    }
    
    // Add current request timestamp
    validRequests.push(now);
    requestCache.set(key, validRequests, config.windowMs / 1000);
    
    const remaining = config.maxRequests - validRequests.length;
    const resetTime = validRequests[0] + config.windowMs;
    
    return {
      allowed: true,
      remaining,
      resetTime
    };
  };
}

/**
 * Predefined rate limit configurations for different endpoint types
 */
export const rateLimitConfigs = {
  // Strict limits for authentication endpoints
  auth: {
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    message: 'Too many authentication attempts. Please try again in 15 minutes.'
  },
  
  // Moderate limits for form submissions
  forms: {
    maxRequests: 10,
    windowMs: 10 * 60 * 1000, // 10 minutes
    message: 'Too many form submissions. Please try again in 10 minutes.'
  },
  
  // Generous limits for general API access
  api: {
    maxRequests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
    message: 'Too many API requests. Please try again in 15 minutes.'
  },
  
  // Very strict for high-risk operations
  sensitive: {
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
    message: 'Too many attempts for sensitive operation. Please try again in 1 hour.'
  }
};

/**
 * Middleware wrapper for Next.js API routes
 */
export function withRateLimit(
  config: RateLimitConfig,
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const limiter = rateLimit(config);
    const result = limiter(request);
    
    if (!result.allowed) {
      return NextResponse.json(
        { 
          error: result.message,
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        },
        { 
          status: 429,
          headers: {
            'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
          }
        }
      );
    }
    
    // Add rate limit headers to successful responses
    const response = await handler(request);
    
    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
    
    return response;
  };
}

/**
 * Clear rate limit data for an IP (admin function)
 */
export function clearRateLimit(ip: string): void {
  const key = `rate_limit:${ip}`;
  requestCache.del(key);
  requestCache.del(`violations:${ip}`);
  ipBanCache.del(`ban:${ip}`);
  
  log.security('Rate limit cleared for IP', {
    ip,
    context: 'rate_limiting'
  });
}

/**
 * Get rate limit statistics
 */
export function getRateLimitStats(): {
  totalKeys: number;
  bannedIps: number;
  cacheStats: any;
} {
  const keys = requestCache.keys();
  const banKeys = ipBanCache.keys();
  
  return {
    totalKeys: keys.length,
    bannedIps: banKeys.filter(key => key.startsWith('ban:')).length,
    cacheStats: {
      requests: requestCache.getStats(),
      bans: ipBanCache.getStats()
    }
  };
} 