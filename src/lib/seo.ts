/**
 * SEO Metadata System for Mokhba Wallet
 * Provides centralized metadata management for all pages with Open Graph, Twitter Card, and structured data support
 */

import type { Metadata } from 'next';

// Base URL and site configuration
const SITE_CONFIG = {
  name: '<PERSON><PERSON><PERSON>',
  title: 'Mokhba - Arabic Cryptocurrency Wallet',
  description: 'The First Arabic Wallet that empowers users to securely store and effortlessly manage their crypto assets.',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com',
  ogImage: '/images/social/og-image.svg',
  twitterHandle: '@mokhbawallet',
  locale: 'en_US',
  type: 'website',
  keywords: [
    'cryptocurrency',
    'wallet',
    'arabic',
    'blockchain',
    'bitcoin',
    'ethereum',
    'crypto',
    'defi',
    'digital assets',
    'middle east',
    'مخبأ',
    'محفظة العملات الرقمية',
    'العربية'
  ]
};

// Page-specific metadata configuration
export interface PageSEOData {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  ogType?: 'website' | 'article' | 'profile';
  article?: {
    publishedTime?: string;
    modifiedTime?: string;
    author?: string;
    section?: string;
    tags?: string[];
  };
  noIndex?: boolean;
  canonical?: string;
  structuredData?: Record<string, any>;
}

// Generate canonical URL
function getCanonicalUrl(path: string, locale?: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  const localePrefix = locale && locale !== 'en' ? `/${locale}` : '';
  return `${SITE_CONFIG.url}${localePrefix}${cleanPath}`;
}

// Generate structured data for Organization
function getOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: SITE_CONFIG.name,
    alternateName: 'مخبأ',
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}/logo.svg`,
    description: SITE_CONFIG.description,
    foundingDate: '2024',
    industry: 'Financial Technology',
    knowsAbout: [
      'Cryptocurrency',
      'Blockchain Technology',
      'Digital Wallets',
      'Financial Technology',
      'Arabic Language Services'
    ],
    areaServed: {
      '@type': 'GeoRegion',
      name: 'Middle East and North Africa'
    },
    sameAs: [
      'https://twitter.com/mokhbawallet',
      'https://github.com/mokhba',
      'https://linkedin.com/company/mokhba'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      availableLanguage: ['English', 'Arabic']
    }
  };
}

// Generate structured data for WebSite
function getWebSiteStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: SITE_CONFIG.name,
    alternateName: 'مخبأ',
    url: SITE_CONFIG.url,
    description: SITE_CONFIG.description,
    inLanguage: ['en', 'ar'],
    publisher: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      logo: `${SITE_CONFIG.url}/logo.svg`
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `${SITE_CONFIG.url}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string'
    }
  };
}

// Generate structured data for SoftwareApplication
function getSoftwareApplicationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: SITE_CONFIG.name,
    alternateName: 'مخبأ',
    description: SITE_CONFIG.description,
    url: SITE_CONFIG.url,
    applicationCategory: 'FinanceApplication',
    operatingSystem: ['Web Browser', 'iOS', 'Android'],
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD'
    },
    featureList: [
      'Cryptocurrency Storage',
      'Multi-language Support',
      'Arabic Interface',
      'Secure Transactions',
      'Portfolio Management',
      'DeFi Integration'
    ],
    inLanguage: ['en', 'ar'],
    provider: {
      '@type': 'Organization',
      name: SITE_CONFIG.name
    }
  };
}

// Generate structured data for Article
function getArticleStructuredData(article: NonNullable<PageSEOData['article']>, title: string, description: string, url: string) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    url: url,
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    author: {
      '@type': 'Person',
      name: article.author || 'Mokhba Team'
    },
    publisher: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      logo: {
        '@type': 'ImageObject',
        url: `${SITE_CONFIG.url}/logo.svg`
      }
    },
    articleSection: article.section,
    keywords: article.tags?.join(', '),
    inLanguage: 'en',
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url
    }
  };
}

// Generate FAQ structured data
function getFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}

// Generate breadcrumb structured data
function getBreadcrumbStructuredData(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };
}

// Main function to generate metadata
export function generateMetadata(
  pageSEO: PageSEOData,
  locale: string = 'en',
  path: string = '/'
): Metadata {
  const canonicalUrl = pageSEO.canonical || getCanonicalUrl(path, locale);
  const ogImage = pageSEO.ogImage || SITE_CONFIG.ogImage;
  const fullTitle = pageSEO.title === SITE_CONFIG.title 
    ? pageSEO.title 
    : `${pageSEO.title} | ${SITE_CONFIG.name}`;
  
  const keywords = [
    ...SITE_CONFIG.keywords,
    ...(pageSEO.keywords || [])
  ].join(', ');

  const metadata: Metadata = {
    title: fullTitle,
    description: pageSEO.description,
    keywords,
    authors: [{ name: 'Mokhba Team' }],
    creator: 'Mokhba',
    publisher: 'Mokhba',
    metadataBase: new URL(SITE_CONFIG.url),
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': locale === 'ar' ? canonicalUrl.replace('/ar/', '/') : canonicalUrl,
        'ar': locale === 'en' ? canonicalUrl.replace(SITE_CONFIG.url, `${SITE_CONFIG.url}/ar`) : canonicalUrl,
        'x-default': canonicalUrl.replace('/ar/', '/')
      }
    },
    openGraph: {
      type: pageSEO.ogType || 'website',
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      url: canonicalUrl,
      title: fullTitle,
      description: pageSEO.description,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: pageSEO.title,
        }
      ],
      ...(pageSEO.article && {
        publishedTime: pageSEO.article.publishedTime,
        modifiedTime: pageSEO.article.modifiedTime,
        authors: pageSEO.article.author ? [pageSEO.article.author] : undefined,
        section: pageSEO.article.section,
        tags: pageSEO.article.tags
      })
    },
    twitter: {
      card: 'summary_large_image',
      site: SITE_CONFIG.twitterHandle,
      creator: SITE_CONFIG.twitterHandle,
      title: fullTitle,
      description: pageSEO.description,
      images: [ogImage]
    },
    robots: {
      index: !pageSEO.noIndex,
      follow: !pageSEO.noIndex,
      googleBot: {
        index: !pageSEO.noIndex,
        follow: !pageSEO.noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
      other: {
        'facebook-domain-verification': process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION || ''
      }
    }
  };

  // Remove robots if not set
  if (pageSEO.noIndex === undefined) {
    delete metadata.robots;
  }

  return metadata;
}

// Generate structured data script
export function generateStructuredData(
  type: 'organization' | 'website' | 'application' | 'article' | 'faq' | 'breadcrumb',
  data?: any
): string {
  let structuredData;

  switch (type) {
    case 'organization':
      structuredData = getOrganizationStructuredData();
      break;
    case 'website':
      structuredData = getWebSiteStructuredData();
      break;
    case 'application':
      structuredData = getSoftwareApplicationStructuredData();
      break;
    case 'article':
      structuredData = getArticleStructuredData(data.article, data.title, data.description, data.url);
      break;
    case 'faq':
      structuredData = getFAQStructuredData(data.faqs);
      break;
    case 'breadcrumb':
      structuredData = getBreadcrumbStructuredData(data.breadcrumbs);
      break;
    default:
      return '';
  }

  return JSON.stringify(structuredData);
}

// Page-specific SEO configurations
export const PAGE_SEO_CONFIG: Record<string, PageSEOData> = {
  // Home page
  home: {
    title: 'Mokhba - Arabic Cryptocurrency Wallet',
    description: 'The first Arabic cryptocurrency wallet that empowers users to securely store and effortlessly manage their crypto assets. Supporting Bitcoin, Ethereum, and more.',
    keywords: ['arabic crypto wallet', 'محفظة العملات الرقمية', 'bitcoin wallet arabic', 'ethereum wallet'],
    ogType: 'website'
  },

  // About page
  about: {
    title: 'About Mokhba - Our Mission & Vision',
    description: 'Learn about Mokhba\'s mission to democratize cryptocurrency access in the Arabic-speaking world through secure, user-friendly wallet solutions.',
    keywords: ['about mokhba', 'crypto wallet mission', 'arabic blockchain', 'financial inclusion'],
    ogType: 'website'
  },

  // App pages
  app: {
    title: 'Mokhba Wallet App - Secure Crypto Management',
    description: 'Access your cryptocurrency portfolio with Mokhba\'s secure wallet application. Manage Bitcoin, Ethereum, and other digital assets safely.',
    keywords: ['crypto wallet app', 'bitcoin management', 'ethereum wallet', 'portfolio tracker'],
    noIndex: false
  },

  // Blog
  blog: {
    title: 'Mokhba Blog - Cryptocurrency News & Education',
    description: 'Stay informed with the latest cryptocurrency news, educational content, and insights from the Mokhba team. Learn about blockchain technology in Arabic.',
    keywords: ['crypto blog', 'blockchain education', 'cryptocurrency news arabic', 'bitcoin education'],
    ogType: 'website'
  },

  // Documentation
  docs: {
    title: 'Mokhba Documentation - User Guides & API',
    description: 'Complete documentation for Mokhba wallet users and developers. Find user guides, API documentation, and technical resources.',
    keywords: ['wallet documentation', 'crypto wallet guide', 'api documentation', 'user manual'],
    ogType: 'website'
  },

  // Privacy Policy
  privacy: {
    title: 'Privacy Policy - Mokhba Wallet',
    description: 'Learn how Mokhba protects your privacy and handles your personal data. Read our comprehensive privacy policy and data protection measures.',
    keywords: ['privacy policy', 'data protection', 'user privacy', 'gdpr compliance'],
    noIndex: false
  },

  // Terms of Service
  terms: {
    title: 'Terms of Service - Mokhba Wallet',
    description: 'Read Mokhba\'s terms of service, user agreement, and legal policies for using our cryptocurrency wallet platform.',
    keywords: ['terms of service', 'user agreement', 'legal terms', 'wallet terms'],
    noIndex: false
  },

  // Status page
  status: {
    title: 'System Status - Mokhba Wallet',
    description: 'Check the current status of Mokhba wallet services, API endpoints, and platform availability in real-time.',
    keywords: ['system status', 'service status', 'uptime', 'api status'],
    ogType: 'website'
  },

  // Support
  support: {
    title: 'Support Center - Mokhba Wallet Help',
    description: 'Get help with your Mokhba wallet. Access our support center, submit tickets, and find answers to frequently asked questions.',
    keywords: ['wallet support', 'customer help', 'troubleshooting', 'contact support'],
    ogType: 'website'
  },

  // Security
  security: {
    title: 'Security Features - Mokhba Wallet Protection',
    description: 'Learn about Mokhba\'s advanced security features, encryption protocols, and how we protect your cryptocurrency assets.',
    keywords: ['wallet security', 'crypto protection', 'encryption', 'secure storage'],
    ogType: 'website'
  },

  // Explore/Discover
  explore: {
    title: 'Explore Crypto - Mokhba Discovery',
    description: 'Discover new cryptocurrencies, DeFi projects, and blockchain innovations with Mokhba\'s exploration tools and market insights.',
    keywords: ['crypto discovery', 'defi exploration', 'new cryptocurrencies', 'market analysis'],
    ogType: 'website'
  },

  // Learn
  learn: {
    title: 'Learn Crypto - Mokhba Education Center',
    description: 'Educational resources about cryptocurrency, blockchain technology, and digital assets. Learn crypto basics in Arabic and English.',
    keywords: ['crypto education', 'blockchain learning', 'bitcoin tutorial', 'ethereum guide'],
    ogType: 'website'
  }
};

// Generate robots.txt content
export function generateRobotsTxt(): string {
  const baseUrl = SITE_CONFIG.url;
  
  return `# Robots.txt for Mokhba Wallet
User-agent: *
Allow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/sitemap-en.xml
Sitemap: ${baseUrl}/sitemap-ar.xml

# Block sensitive areas
Disallow: /api/
Disallow: /admin/
Disallow: /_next/
Disallow: /.*\\?*

# Allow specific API endpoints for crawling
Allow: /api/health
Allow: /api/status

# Crawl delay (optional)
Crawl-delay: 1
`;
}

// Export utilities
export { SITE_CONFIG }; 