/**
 * Startup validation for environment variables
 * This module ensures the application fails fast if environment variables are misconfigured
 */

import { validateEnvironment } from './env-validation';
import { log } from './logger';

/**
 * Validates environment variables at application startup
 * Should be called early in the application lifecycle
 */
export function validateStartup() {
  try {
    // Validate environment variables
    validateEnvironment();
    
    // Additional startup validations can be added here
    validateSupabaseConnection();
    
    log.startup('Startup validation completed successfully');
  } catch (error) {
    log.error('Startup validation failed', {
      error: error instanceof Error ? error.message : String(error),
      context: 'application_startup'
    });
    
    // In development, provide helpful guidance
    if (process.env.NODE_ENV === 'development') {
      log.error('Application startup guidance required', {
        setupSteps: [
          'Copy .env.example to .env.local',
          'Get your Supabase credentials from https://supabase.com',
          'Fill in the environment variables in .env.local',
          'Restart your development server'
        ]
      });
    }
    
    throw error;
  }
}

/**
 * Basic validation that Supabase URLs are reachable
 */
function validateSupabaseConnection() {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    
    if (supabaseUrl) {
      // Basic URL format validation
      new URL(supabaseUrl);
      
      // Check if it's a valid Supabase URL format
      if (!supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('localhost')) {
        log.warn('Supabase URL format looks unusual', { 
          url: supabaseUrl,
          suggestion: 'Please verify the URL is correct'
        });
      }
    }
  } catch (error) {
    log.error('Invalid Supabase URL format', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error('NEXT_PUBLIC_SUPABASE_URL must be a valid URL');
  }
}

/**
 * Health check for critical services
 * Can be expanded to check database connectivity, external APIs, etc.
 */
export async function healthCheck() {
  const checks = {
    environment: false,
    supabase: false
  };
  
  try {
    // Environment validation
    validateEnvironment();
    checks.environment = true;
    
    // Supabase connectivity (basic check)
    validateSupabaseConnection();
    checks.supabase = true;
    
    return {
      status: 'healthy',
      checks,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      checks,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
} 