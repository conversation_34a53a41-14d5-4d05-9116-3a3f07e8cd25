/**
 * Image Optimization Utilities for Mokhba Wallet
 * Provides centralized image handling with performance optimizations
 */

import { ImageProps } from 'next/image';

// Image quality presets for different use cases
export const IMAGE_QUALITY = {
  LOW: 50,        // For thumbnails, small images
  MEDIUM: 75,     // For regular content images
  HIGH: 85,       // For hero images, important visuals
  LOSSLESS: 100   // For logos, critical brand assets
} as const;

// Common image sizes for responsive design
export const IMAGE_SIZES = {
  THUMBNAIL: { width: 64, height: 64 },
  SMALL: { width: 128, height: 128 },
  MEDIUM: { width: 256, height: 256 },
  LARGE: { width: 512, height: 512 },
  HERO: { width: 1200, height: 630 },
  FULLSCREEN: { width: 1920, height: 1080 }
} as const;

// Responsive image breakpoints
export const RESPONSIVE_SIZES = {
  MOBILE: '(max-width: 640px) 100vw',
  TABLET: '(max-width: 1024px) 50vw',
  DESKTOP: '33vw',
  FULL_WIDTH: '100vw',
  HALF_WIDTH: '50vw',
  CARD: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
  HERO: '100vw'
} as const;

// Base64 blur placeholders for different aspect ratios
export const BLUR_PLACEHOLDERS = {
  SQUARE: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  LANDSCAPE: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAIDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  PORTRAIT: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
} as const;

// Image optimization configurations for different use cases
export interface OptimizedImageConfig {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  className?: string;
  style?: React.CSSProperties;
  loading?: 'lazy' | 'eager';
  unoptimized?: boolean;
}

// Generate optimized image props
export function getOptimizedImageProps(config: OptimizedImageConfig): Partial<ImageProps> {
  const {
    src,
    alt,
    width,
    height,
    quality = IMAGE_QUALITY.HIGH,
    priority = false,
    placeholder = 'blur',
    blurDataURL,
    sizes = RESPONSIVE_SIZES.CARD,
    className = '',
    style = {},
    loading = 'lazy',
    unoptimized = false
  } = config;

  // Determine appropriate blur placeholder
  let finalBlurDataURL = blurDataURL;
  if (placeholder === 'blur' && !blurDataURL) {
    if (width && height) {
      const aspectRatio = width / height;
      if (aspectRatio > 1.5) {
        finalBlurDataURL = BLUR_PLACEHOLDERS.LANDSCAPE;
      } else if (aspectRatio < 0.75) {
        finalBlurDataURL = BLUR_PLACEHOLDERS.PORTRAIT;
      } else {
        finalBlurDataURL = BLUR_PLACEHOLDERS.SQUARE;
      }
    } else {
      finalBlurDataURL = BLUR_PLACEHOLDERS.SQUARE;
    }
  }

  const props: Partial<ImageProps> = {
    src,
    alt,
    quality,
    priority,
    placeholder,
    sizes,
    className,
    style: {
      ...style,
      objectFit: style.objectFit || 'cover',
    },
    loading: priority ? 'eager' : loading,
    unoptimized
  };

  // Add dimensions if provided
  if (width) props.width = width;
  if (height) props.height = height;

  // Add blur placeholder if needed
  if (placeholder === 'blur' && finalBlurDataURL) {
    props.blurDataURL = finalBlurDataURL;
  }

  return props;
}

// Predefined configurations for common image types
export const IMAGE_CONFIGS = {
  LOGO: (src: string, alt: string): OptimizedImageConfig => ({
    src,
    alt,
    width: 80,
    height: 80,
    quality: IMAGE_QUALITY.LOSSLESS,
    priority: true,
    placeholder: 'empty',
    loading: 'eager',
    unoptimized: true, // SVG logos don't need optimization
    className: 'object-contain'
  }),

  HERO_IMAGE: (src: string, alt: string): OptimizedImageConfig => ({
    src,
    alt,
    width: 1200,
    height: 630,
    quality: IMAGE_QUALITY.HIGH,
    priority: true,
    sizes: RESPONSIVE_SIZES.HERO,
    className: 'object-cover w-full h-full'
  }),

  CARD_IMAGE: (src: string, alt: string): OptimizedImageConfig => ({
    src,
    alt,
    width: 400,
    height: 300,
    quality: IMAGE_QUALITY.MEDIUM,
    sizes: RESPONSIVE_SIZES.CARD,
    className: 'object-cover w-full h-full rounded-lg'
  }),

  AVATAR: (src: string, alt: string): OptimizedImageConfig => ({
    src,
    alt,
    width: 64,
    height: 64,
    quality: IMAGE_QUALITY.MEDIUM,
    sizes: '64px',
    className: 'object-cover rounded-full'
  }),

  ICON: (src: string, alt: string, size: number = 24): OptimizedImageConfig => ({
    src,
    alt,
    width: size,
    height: size,
    quality: IMAGE_QUALITY.HIGH,
    sizes: `${size}px`,
    className: 'object-contain'
  }),

  CRYPTO_LOGO: (src: string, alt: string): OptimizedImageConfig => ({
    src,
    alt,
    width: 32,
    height: 32,
    quality: IMAGE_QUALITY.HIGH,
    sizes: '32px',
    className: 'object-contain rounded-full',
    placeholder: 'blur'
  }),

  SOCIAL_PREVIEW: (src: string, alt: string): OptimizedImageConfig => ({
    src,
    alt,
    width: 1200,
    height: 630,
    quality: IMAGE_QUALITY.HIGH,
    priority: true,
    placeholder: 'empty',
    className: 'object-cover'
  })
} as const;

// Generate srcset for responsive images
export function generateSrcSet(src: string, sizes: number[]): string {
  return sizes
    .map(size => `${src}?w=${size}&q=${IMAGE_QUALITY.HIGH} ${size}w`)
    .join(', ');
}

// Utility to create image URLs with optimization parameters
export function createOptimizedImageUrl(
  src: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg' | 'png';
  } = {}
): string {
  const { width, height, quality = IMAGE_QUALITY.HIGH, format } = options;
  
  // For external URLs, return as-is (handled by Next.js Image component)
  if (src.startsWith('http')) {
    return src;
  }

  // For local images, add optimization parameters
  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  if (height) params.set('h', height.toString());
  if (quality) params.set('q', quality.toString());
  if (format) params.set('f', format);

  const queryString = params.toString();
  return queryString ? `${src}?${queryString}` : src;
}

// Performance monitoring for images
export function trackImagePerformance(src: string, startTime: number) {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const loadTime = performance.now() - startTime;
    
    // Log slow loading images in development
    if (process.env.NODE_ENV === 'development' && loadTime > 1000) {
      console.warn(`Slow image loading detected: ${src} took ${loadTime.toFixed(2)}ms`);
    }

    // Track in analytics (implement your analytics tracking here)
    // analytics.track('image_load_time', { src, loadTime });
  }
}

// Preload critical images
export function preloadImage(src: string, priority: boolean = false): void {
  if (typeof window !== 'undefined') {
    const link = document.createElement('link');
    link.rel = priority ? 'preload' : 'prefetch';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  }
}

// Image lazy loading intersection observer
export function createImageObserver(
  callback: (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver | null {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null;
  }

  return new IntersectionObserver(callback, {
    rootMargin: '50px 0px',
    threshold: 0.1,
    ...options
  });
}

// Fallback image handler
export function handleImageError(
  event: React.SyntheticEvent<HTMLImageElement>,
  fallbackSrc?: string
): void {
  const img = event.currentTarget;
  
  if (fallbackSrc && img.src !== fallbackSrc) {
    img.src = fallbackSrc;
  } else {
    // Set a generic fallback
    img.src = '/logo.svg';
    img.alt = 'Image not available';
  }
}

// Generate responsive image sizes string
export function generateResponsiveSizes(breakpoints: {
  mobile?: string;
  tablet?: string;
  desktop?: string;
  default?: string;
}): string {
  const { mobile = '100vw', tablet = '50vw', desktop = '33vw', default: defaultSize = '100vw' } = breakpoints;
  
  return [
    `(max-width: 640px) ${mobile}`,
    `(max-width: 1024px) ${tablet}`,
    `(min-width: 1025px) ${desktop}`,
    defaultSize
  ].join(', ');
}

// Image format detection and optimization
export function getBestImageFormat(): 'avif' | 'webp' | 'jpeg' {
  if (typeof window === 'undefined') return 'jpeg';

  // Check for AVIF support
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  
  if (canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
    return 'avif';
  }
  
  // Check for WebP support
  if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
    return 'webp';
  }
  
  return 'jpeg';
}

// CDN URL generator (for future CDN integration)
export function generateCDNUrl(
  src: string,
  transformations: {
    width?: number;
    height?: number;
    quality?: number;
    format?: string;
    crop?: 'fill' | 'fit' | 'scale';
  } = {}
): string {
  // This is a placeholder for CDN integration
  // Replace with your CDN's URL generation logic
  const { width, height, quality, format, crop } = transformations;
  
  // For now, return the original src
  // In production, you might use Cloudinary, ImageKit, or similar
  return src;
}

export default {
  getOptimizedImageProps,
  IMAGE_CONFIGS,
  IMAGE_QUALITY,
  IMAGE_SIZES,
  RESPONSIVE_SIZES,
  BLUR_PLACEHOLDERS,
  generateSrcSet,
  createOptimizedImageUrl,
  trackImagePerformance,
  preloadImage,
  createImageObserver,
  handleImageError,
  generateResponsiveSizes,
  getBestImageFormat,
  generateCDNUrl
}; 