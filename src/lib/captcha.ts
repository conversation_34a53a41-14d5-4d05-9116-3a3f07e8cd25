import { log } from './logger';
import { NextRequest, NextResponse } from 'next/server';

/**
 * CAPTCHA verification result
 */
export interface CaptchaVerificationResult {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

/**
 * Verify reCAP<PERSON>HA token on the server side
 */
export async function verifyCaptcha(
  token: string,
  remoteIp?: string
): Promise<CaptchaVerificationResult> {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;
  
  if (!secretKey) {
    log.error('RECAPTCHA_SECRET_KEY environment variable not configured');
    return {
      success: false,
      'error-codes': ['missing-secret-key']
    };
  }

  if (!token) {
    log.warn('CAPTCHA verification attempted without token');
    return {
      success: false,
      'error-codes': ['missing-input-response']
    };
  }

  try {
    const verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
    const formData = new URLSearchParams();
    formData.append('secret', secretKey);
    formData.append('response', token);
    
    if (remoteIp) {
      formData.append('remoteip', remoteIp);
    }

    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      log.error('CAPTCHA verification API request failed', {
        status: response.status,
        statusText: response.statusText
      });
      return {
        success: false,
        'error-codes': ['api-request-failed']
      };
    }

    const result = await response.json() as CaptchaVerificationResult;
    
    // Log verification attempts for security monitoring
    log.security('CAPTCHA verification attempted', {
      success: result.success,
      score: result.score,
      action: result.action,
      hostname: result.hostname,
      hasErrors: result['error-codes'] && result['error-codes'].length > 0,
      remoteIp: remoteIp ? '[PROVIDED]' : '[NOT_PROVIDED]',
      context: 'captcha_verification'
    });

    return result;
  } catch (error) {
    log.error('CAPTCHA verification failed', {
      error: error instanceof Error ? error.message : String(error),
      context: 'captcha_verification'
    });
    
    return {
      success: false,
      'error-codes': ['verification-failed']
    };
  }
}

/**
 * Validate reCAPTCHA v3 score threshold
 */
export function isValidCaptchaScore(
  result: CaptchaVerificationResult,
  minimumScore: number = 0.5
): boolean {
  if (!result.success) {
    return false;
  }

  // reCAPTCHA v3 returns a score between 0.0 and 1.0
  if (typeof result.score === 'number') {
    return result.score >= minimumScore;
  }

  // For reCAPTCHA v2, no score is provided, just success
  return true;
}

/**
 * CAPTCHA middleware for Next.js API routes
 */
export function withCaptcha(
  handler: (request: Request) => Promise<Response>,
  options: {
    minimumScore?: number;
    requiredAction?: string;
    skipInDevelopment?: boolean;
  } = {}
) {
  return async (request: Request): Promise<Response> => {
    const {
      minimumScore = 0.5,
      requiredAction,
      skipInDevelopment = true
    } = options;

    // Skip CAPTCHA in development if configured
    if (skipInDevelopment && process.env.NODE_ENV === 'development') {
      log.debug('Skipping CAPTCHA verification in development mode');
      return handler(request);
    }

    // Extract CAPTCHA token from request
    const body = await request.json().catch(() => ({}));
    const token = body?.captchaToken || request.headers.get('x-captcha-token');
    
    if (!token) {
      log.warn('Request rejected: missing CAPTCHA token', {
        endpoint: request.url,
        method: request.method,
        context: 'captcha_verification'
      });
      
      return new Response(JSON.stringify({
        error: 'CAPTCHA verification required',
        code: 'CAPTCHA_MISSING'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get client IP for verification
    const clientIp = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                     request.headers.get('x-real-ip');

    // Verify CAPTCHA
    const verificationResult = await verifyCaptcha(token, clientIp || undefined);

    if (!verificationResult.success) {
      log.security('Request rejected: CAPTCHA verification failed', {
        endpoint: request.url,
        method: request.method,
        errors: verificationResult['error-codes'],
        clientIp: clientIp ? '[PROVIDED]' : '[NOT_PROVIDED]',
        context: 'captcha_verification'
      });

      return new Response(JSON.stringify({
        error: 'CAPTCHA verification failed',
        code: 'CAPTCHA_INVALID',
        details: verificationResult['error-codes']
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check score for reCAPTCHA v3
    if (!isValidCaptchaScore(verificationResult, minimumScore)) {
      log.security('Request rejected: CAPTCHA score too low', {
        endpoint: request.url,
        method: request.method,
        score: verificationResult.score,
        minimumScore,
        clientIp: clientIp ? '[PROVIDED]' : '[NOT_PROVIDED]',
        context: 'captcha_verification'
      });

      return new Response(JSON.stringify({
        error: 'CAPTCHA verification failed: suspicious activity detected',
        code: 'CAPTCHA_SCORE_LOW'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check action for reCAPTCHA v3
    if (requiredAction && verificationResult.action !== requiredAction) {
      log.security('Request rejected: CAPTCHA action mismatch', {
        endpoint: request.url,
        method: request.method,
        expectedAction: requiredAction,
        actualAction: verificationResult.action,
        context: 'captcha_verification'
      });

      return new Response(JSON.stringify({
        error: 'CAPTCHA verification failed: action mismatch',
        code: 'CAPTCHA_ACTION_MISMATCH'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // CAPTCHA verification successful, proceed with request
    return handler(request);
  };
}

/**
 * CAPTCHA configuration for different form types
 */
export const captchaConfigs = {
  // Contact forms and general submissions
  forms: {
    minimumScore: 0.5,
    skipInDevelopment: true
  },
  
  // Authentication and sensitive operations
  auth: {
    minimumScore: 0.7,
    skipInDevelopment: false
  },
  
  // High-risk operations
  sensitive: {
    minimumScore: 0.8,
    skipInDevelopment: false
  }
};

/**
 * Get CAPTCHA site key for frontend
 */
export function getCaptchaSiteKey(): string | null {
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || null;
  console.log('getCaptchaSiteKey:', siteKey);
  return siteKey;
}