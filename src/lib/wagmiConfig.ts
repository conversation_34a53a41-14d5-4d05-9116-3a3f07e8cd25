'use client';

import { createConfig, http, fallback } from 'wagmi';
import { mainnet, polygon, arbitrum, optimism, base } from 'wagmi/chains';
import { injected, metaMask } from 'wagmi/connectors';

// Get the site URL for API proxy
const getSiteUrl = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
};

const getMainnetRPC = () => {
  const alchemyKey = process.env.NEXT_PUBLIC_ALCHEMY_API_KEY;
  const infuraKey = process.env.NEXT_PUBLIC_INFURA_API_KEY;
  const customRPC = process.env.NEXT_PUBLIC_ETHEREUM_RPC_URL;

  const rpcs = [];

  // Add API keys first (these usually have CORS enabled)
  if (alchemyKey) {
    rpcs.push(http(`https://eth-mainnet.g.alchemy.com/v2/${alchemyKey}`));
  }

  if (infuraKey) {
    rpcs.push(http(`https://mainnet.infura.io/v3/${infuraKey}`));
  }

  if (customRPC) {
    rpcs.push(http(customRPC));
  }

  // Add working public endpoints (removed local API proxy to prevent loops)
  rpcs.push(
    http('https://ethereum.publicnode.com'),
    http('https://rpc.flashbots.net'),
    http('https://cloudflare-eth.com')
  );

  return rpcs.length > 1 ? fallback(rpcs) : rpcs[0] || http('https://ethereum.publicnode.com');
};

const getPolygonRPC = () => {
  const alchemyKey = process.env.NEXT_PUBLIC_ALCHEMY_API_KEY;
  const infuraKey = process.env.NEXT_PUBLIC_INFURA_API_KEY;

  const rpcs = [];

  if (alchemyKey) {
    rpcs.push(http(`https://polygon-mainnet.g.alchemy.com/v2/${alchemyKey}`));
  }

  if (infuraKey) {
    rpcs.push(http(`https://polygon-mainnet.infura.io/v3/${infuraKey}`));
  }

  // Add public endpoints with CORS support
  rpcs.push(
    http('https://polygon.rpc.blxrbdn.com'),
    http('https://polygon.llamarpc.com')
  );

  return rpcs.length > 1 ? fallback(rpcs) : rpcs[0] || http('https://polygon.rpc.blxrbdn.com');
};

const getArbitrumRPC = () => {
  const alchemyKey = process.env.NEXT_PUBLIC_ALCHEMY_API_KEY;
  const infuraKey = process.env.NEXT_PUBLIC_INFURA_API_KEY;

  const rpcs = [];

  if (alchemyKey) {
    rpcs.push(http(`https://arb-mainnet.g.alchemy.com/v2/${alchemyKey}`));
  }

  if (infuraKey) {
    rpcs.push(http(`https://arbitrum-mainnet.infura.io/v3/${infuraKey}`));
  }

  rpcs.push(
    http('https://arbitrum.rpc.blxrbdn.com'),
    http('https://arbitrum.llamarpc.com')
  );

  return rpcs.length > 1 ? fallback(rpcs) : rpcs[0] || http('https://arbitrum.rpc.blxrbdn.com');
};

const getOptimismRPC = () => {
  const alchemyKey = process.env.NEXT_PUBLIC_ALCHEMY_API_KEY;
  const infuraKey = process.env.NEXT_PUBLIC_INFURA_API_KEY;

  const rpcs = [];

  if (alchemyKey) {
    rpcs.push(http(`https://opt-mainnet.g.alchemy.com/v2/${alchemyKey}`));
  }

  if (infuraKey) {
    rpcs.push(http(`https://optimism-mainnet.infura.io/v3/${infuraKey}`));
  }

  rpcs.push(
    http('https://optimism.rpc.blxrbdn.com'),
    http('https://optimism.llamarpc.com')
  );

  return rpcs.length > 1 ? fallback(rpcs) : rpcs[0] || http('https://optimism.rpc.blxrbdn.com');
};

const getBaseRPC = () => {
  const alchemyKey = process.env.NEXT_PUBLIC_ALCHEMY_API_KEY;

  const rpcs = [];

  if (alchemyKey) {
    rpcs.push(http(`https://base-mainnet.g.alchemy.com/v2/${alchemyKey}`));
  }

  rpcs.push(
    http('https://base.rpc.blxrbdn.com'),
    http('https://base.llamarpc.com')
  );

  return rpcs.length > 1 ? fallback(rpcs) : rpcs[0] || http('https://base.rpc.blxrbdn.com');
};

// Only create config on client side
export const config = typeof window !== 'undefined' ? createConfig({
  chains: [mainnet, polygon, arbitrum, optimism, base],
  connectors: [
    metaMask({
      dappMetadata: {
        name: 'Mokhba',
        url: process.env.NEXT_PUBLIC_SITE_URL || 'https://mokhba.com',
      },
    }),
    injected({
      target: 'metaMask',
    }),
  ],
  transports: {
    [mainnet.id]: getMainnetRPC(),
    [polygon.id]: getPolygonRPC(),
    [arbitrum.id]: getArbitrumRPC(),
    [optimism.id]: getOptimismRPC(),
    [base.id]: getBaseRPC(),
  },
  ssr: false, // Disable SSR to prevent vendor chunk issues
}) : null as any;
