/**
 * Enhanced API Client with <PERSON>rror Handling and Retry Logic
 * Provides centralized API communication with automatic retry, circuit breaking, and graceful degradation
 */

import { 
  ErrorType, 
  ErrorSeverity, 
  Error<PERSON>ontext, 
  RetryConfig, 
  ErrorRecoveryStrategy,
  handleAPIErrorWithRetry,
  errorHandler
} from './errorHandler';
import { log } from './logger';

export interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: unknown;
  timeout?: number;
  retryConfig?: Partial<RetryConfig>;
  recoveryStrategy?: ErrorRecoveryStrategy;
  cache?: boolean;
  validateResponse?: (response: unknown) => boolean;
}

export interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  headers: Headers;
  success: boolean;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: unknown;
  response?: Response;
}

class ApiClient {
  private static instance: ApiClient;
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private requestCache: Map<string, { data: unknown; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(baseURL: string = '') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  static getInstance(baseURL?: string): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient(baseURL);
    }
    return ApiClient.instance;
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove authentication token
   */
  clearAuthToken(): void {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Main request method with comprehensive error handling
   */
  async request<T = unknown>(
    endpoint: string,
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const context: ErrorContext = {
      component: 'ApiClient',
      action: `${config.method || 'GET'} ${endpoint}`,
      metadata: {
        endpoint,
        method: config.method || 'GET',
        hasBody: !!config.body
      }
    };

    // Check cache for GET requests
    if (config.cache && config.method === 'GET') {
      const cached = this.getCachedResponse<T>(endpoint);
      if (cached) {
        log.info('API request served from cache', {
          endpoint,
          cacheAge: Date.now() - cached.timestamp
        });
        return {
          data: cached.data as T,
          status: 200,
          headers: new Headers(),
          success: true
        };
      }
    }

    const operation = async (): Promise<ApiResponse<T>> => {
      const url = this.buildURL(endpoint);
      const requestConfig = this.buildRequestConfig(config);

      log.info('API request initiated', {
        url,
        method: config.method || 'GET',
        hasAuth: !!this.defaultHeaders['Authorization']
      });

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Request timeout after ${config.timeout || 30000}ms`));
        }, config.timeout || 30000);
      });

      try {
        const response = await Promise.race([
          fetch(url, requestConfig),
          timeoutPromise
        ]);

        const result = await this.handleResponse<T>(response, endpoint, config);

        // Cache successful GET responses
        if (config.cache && config.method === 'GET' && result.success) {
          this.setCachedResponse(endpoint, result.data);
        }

        return result;
      } catch (error) {
        throw this.enhanceError(error as Error, endpoint, config);
      }
    };

    return handleAPIErrorWithRetry(
      operation,
      context,
      config.retryConfig,
      config.recoveryStrategy
    );
  }

  /**
   * Convenience methods for different HTTP verbs
   */
  async get<T = unknown>(
    endpoint: string,
    config: Omit<ApiRequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T = unknown>(
    endpoint: string,
    body?: unknown,
    config: Omit<ApiRequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body });
  }

  async put<T = unknown>(
    endpoint: string,
    body?: unknown,
    config: Omit<ApiRequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body });
  }

  async patch<T = unknown>(
    endpoint: string,
    body?: unknown,
    config: Omit<ApiRequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body });
  }

  async delete<T = unknown>(
    endpoint: string,
    config: Omit<ApiRequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  /**
   * Safe request wrapper that never throws
   */
  async safeRequest<T = unknown>(
    endpoint: string,
    config: ApiRequestConfig = {},
    fallbackValue?: T
  ): Promise<{ data: T | null; error: ApiError | null; success: boolean }> {
    try {
      const response = await this.request<T>(endpoint, {
        ...config,
        recoveryStrategy: {
          fallbackValue,
          gracefulDegradation: true,
          userNotification: 'Service temporarily unavailable. Using cached data.'
        }
      });
      return { data: response.data, error: null, success: true };
    } catch (error) {
      return { 
        data: fallbackValue || null, 
        error: error as ApiError, 
        success: false 
      };
    }
  }

  /**
   * Batch requests with error isolation
   */
  async batchRequest<T = unknown>(
    requests: Array<{ endpoint: string; config?: ApiRequestConfig }>,
    options: {
      failFast?: boolean;
      maxConcurrent?: number;
    } = {}
  ): Promise<Array<{ data: T | null; error: ApiError | null; success: boolean }>> {
    const { failFast = false, maxConcurrent = 5 } = options;
    const results: Array<{ data: T | null; error: ApiError | null; success: boolean }> = [];

    // Process requests in batches
    for (let i = 0; i < requests.length; i += maxConcurrent) {
      const batch = requests.slice(i, i + maxConcurrent);
      
      const batchPromises = batch.map(async ({ endpoint, config }) => {
        const result = await this.safeRequest<T>(endpoint, config);
        
        if (failFast && !result.success) {
          throw result.error;
        }
        
        return result;
      });

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        if (failFast) {
          throw error;
        }
        // Continue with remaining batches
      }
    }

    return results;
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.requestCache.clear();
    log.info('API cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.requestCache.size,
      keys: Array.from(this.requestCache.keys())
    };
  }

  private buildURL(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    const cleanBaseURL = this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL;
    return cleanBaseURL ? `${cleanBaseURL}/${cleanEndpoint}` : `/${cleanEndpoint}`;
  }

  private buildRequestConfig(config: ApiRequestConfig): RequestInit {
    const headers = { ...this.defaultHeaders, ...config.headers };
    
    const requestConfig: RequestInit = {
      method: config.method || 'GET',
      headers
    };

    if (config.body && config.method !== 'GET') {
      if (typeof config.body === 'string') {
        requestConfig.body = config.body;
      } else {
        requestConfig.body = JSON.stringify(config.body);
      }
    }

    return requestConfig;
  }

  private async handleResponse<T>(
    response: Response,
    endpoint: string,
    config: ApiRequestConfig
  ): Promise<ApiResponse<T>> {
    const isJson = response.headers.get('content-type')?.includes('application/json');
    
    let data: T;
    try {
      if (isJson) {
        data = await response.json();
      } else {
        data = (await response.text()) as unknown as T;
      }
    } catch (parseError) {
      log.error('Failed to parse response', {
        endpoint,
        status: response.status,
        contentType: response.headers.get('content-type'),
        parseError: parseError instanceof Error ? parseError.message : String(parseError)
      });
      throw new Error('Failed to parse response data');
    }

    const result: ApiResponse<T> = {
      data,
      status: response.status,
      headers: response.headers,
      success: response.ok
    };

    if (!response.ok) {
      const apiError: ApiError = new Error(`API request failed: ${response.status} ${response.statusText}`);
      apiError.status = response.status;
      apiError.response = response;
      apiError.details = data;
      
      // Try to extract error code from response
      if (typeof data === 'object' && data !== null && 'code' in data) {
        apiError.code = String((data as { code: unknown }).code);
      }

      throw apiError;
    }

    // Validate response if validator provided
    if (config.validateResponse && !config.validateResponse(data)) {
      throw new Error('Response validation failed');
    }

    log.info('API request completed successfully', {
      endpoint,
      status: response.status,
      responseSize: JSON.stringify(data).length
    });

    return result;
  }

  private enhanceError(error: Error, endpoint: string, config: ApiRequestConfig): ApiError {
    const apiError = error as ApiError;
    
    // Add context to error
    apiError.message = `API Error on ${config.method || 'GET'} ${endpoint}: ${error.message}`;
    
    // Determine error type for better handling
    if (error.message.includes('timeout')) {
      apiError.code = 'TIMEOUT';
    } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
      apiError.code = 'NETWORK_ERROR';
    }

    return apiError;
  }

  private getCachedResponse<T>(endpoint: string): { data: T; timestamp: number } | null {
    const cached = this.requestCache.get(endpoint);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached as { data: T; timestamp: number };
    }
    
    // Remove expired cache entry
    if (cached) {
      this.requestCache.delete(endpoint);
    }
    
    return null;
  }

  private setCachedResponse(endpoint: string, data: unknown): void {
    this.requestCache.set(endpoint, {
      data,
      timestamp: Date.now()
    });

    // Clean up old cache entries
    if (this.requestCache.size > 100) {
      const oldestKeys = Array.from(this.requestCache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)
        .slice(0, 20)
        .map(([key]) => key);
      
      oldestKeys.forEach(key => this.requestCache.delete(key));
    }
  }
}

// Create and export singleton instance
export const apiClient = ApiClient.getInstance();

// Export type-safe API methods for common use cases
export const api = {
  // Health check with fallback
  health: () => apiClient.safeRequest<{ status: string }>('/api/health', {
    cache: true,
    recoveryStrategy: {
      fallbackValue: { status: 'unknown' },
      userNotification: 'Unable to check system health'
    }
  }),

  // Form submissions with retry
  submitCardWaitlist: (data: unknown) => apiClient.post('/api/card-waitlist', data, {
    retryConfig: { maxAttempts: 2 },
    recoveryStrategy: {
      userNotification: 'Failed to submit. Please try again or contact support.'
    }
  }),

  submitSupportTicket: (data: unknown) => apiClient.post('/api/support-tickets', data, {
    retryConfig: { maxAttempts: 2 },
    recoveryStrategy: {
      userNotification: 'Failed to submit support ticket. Please try again.'
    }
  }),

  submitFeatureRequest: (data: unknown) => apiClient.post('/api/feature-requests', data, {
    retryConfig: { maxAttempts: 2 },
    recoveryStrategy: {
      userNotification: 'Failed to submit feature request. Please try again.'
    }
  }),

  // Admin operations with specific error handling
  getAdminData: <T>(endpoint: string) => apiClient.get<T>(endpoint, {
    retryConfig: { maxAttempts: 1 }, // Don't retry auth errors
    recoveryStrategy: {
      userNotification: 'Admin access required. Please log in again.'
    }
  })
};

export default apiClient; 