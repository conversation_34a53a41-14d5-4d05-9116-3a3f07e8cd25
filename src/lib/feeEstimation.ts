/**
 * Fee Estimation System for Mokhba Wallet
 * Handles real-time network fee calculation for different chains
 */

import { ChainType } from '@/types';

export interface FeeEstimate {
  gasPrice: string;
  gasLimit: string;
  totalFee: string;
  totalFeeUSD?: number;
  feeInWei: bigint;
  speed: 'slow' | 'standard' | 'fast';
  estimatedTime: string;
}

export interface FeeOptions {
  slow: FeeEstimate;
  standard: FeeEstimate;
  fast: FeeEstimate;
}

/**
 * Fee Estimation Service
 */
export class FeeEstimationService {
  private static instance: FeeEstimationService;
  private cache: Map<string, { data: FeeOptions; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 seconds

  static getInstance(): FeeEstimationService {
    if (!FeeEstimationService.instance) {
      FeeEstimationService.instance = new FeeEstimationService();
    }
    return FeeEstimationService.instance;
  }

  /**
   * Get fee estimates for a transaction
   */
  async getFeeEstimates(
    chainType: ChainType,
    to: string,
    value: string = '0',
    data: string = '0x'
  ): Promise<FeeOptions> {
    const cacheKey = `${chainType}-${to}-${value}-${data}`;
    const cached = this.cache.get(cacheKey);

    // Return cached data if still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      let feeOptions: FeeOptions;

      switch (chainType) {
        case 'ethereum':
          feeOptions = await this.getEthereumFees(to, value, data);
          break;
        case 'polygon':
          feeOptions = await this.getPolygonFees(to, value, data);
          break;
        case 'solana':
          feeOptions = await this.getSolanaFees();
          break;
        case 'bsc':
          feeOptions = await this.getBSCFees(to, value, data);
          break;
        default:
          throw new Error(`Unsupported chain: ${chainType}`);
      }

      // Cache the result
      this.cache.set(cacheKey, {
        data: feeOptions,
        timestamp: Date.now()
      });

      return feeOptions;
    } catch (error) {
      console.error('Fee estimation failed:', error);
      // Return fallback estimates
      return this.getFallbackFees(chainType);
    }
  }

  /**
   * Get Ethereum fee estimates
   */
  private async getEthereumFees(to: string, value: string, data: string): Promise<FeeOptions> {
    try {
      // Use our RPC proxy for fee estimation
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
      
      // Get gas price
      const gasPriceResponse = await fetch(`${siteUrl}/api/rpc`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_gasPrice',
          params: [],
          id: 1
        })
      });
      
      const gasPriceData = await gasPriceResponse.json();
      const baseGasPrice = BigInt(gasPriceData.result);

      // Estimate gas limit
      const gasEstimateResponse = await fetch(`${siteUrl}/api/rpc`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_estimateGas',
          params: [{
            to,
            value: value === '0' ? undefined : `0x${BigInt(value).toString(16)}`,
            data: data === '0x' ? undefined : data
          }],
          id: 2
        })
      });

      const gasEstimateData = await gasEstimateResponse.json();
      const gasLimit = BigInt(gasEstimateData.result || '0x5208'); // Default 21000 for simple transfer

      return this.createFeeOptions(baseGasPrice, gasLimit, 'ethereum');
    } catch (error) {
      console.error('Ethereum fee estimation failed:', error);
      return this.getFallbackFees('ethereum');
    }
  }

  /**
   * Get Polygon fee estimates
   */
  private async getPolygonFees(to: string, value: string, data: string): Promise<FeeOptions> {
    // Similar to Ethereum but with different base fees
    try {
      const baseGasPrice = BigInt('30000000000'); // 30 gwei base
      const gasLimit = BigInt('21000');
      return this.createFeeOptions(baseGasPrice, gasLimit, 'polygon');
    } catch (error) {
      return this.getFallbackFees('polygon');
    }
  }

  /**
   * Get Solana fee estimates
   */
  private async getSolanaFees(): Promise<FeeOptions> {
    // Solana has fixed fees
    const baseFee = BigInt('5000'); // 0.000005 SOL in lamports
    const gasLimit = BigInt('1');

    return {
      slow: {
        gasPrice: baseFee.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatSolanaFee(baseFee),
        feeInWei: baseFee,
        speed: 'slow',
        estimatedTime: '~30s'
      },
      standard: {
        gasPrice: baseFee.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatSolanaFee(baseFee),
        feeInWei: baseFee,
        speed: 'standard',
        estimatedTime: '~15s'
      },
      fast: {
        gasPrice: baseFee.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatSolanaFee(baseFee),
        feeInWei: baseFee,
        speed: 'fast',
        estimatedTime: '~10s'
      }
    };
  }

  /**
   * Get BSC fee estimates
   */
  private async getBSCFees(to: string, value: string, data: string): Promise<FeeOptions> {
    const baseGasPrice = BigInt('5000000000'); // 5 gwei base
    const gasLimit = BigInt('21000');
    return this.createFeeOptions(baseGasPrice, gasLimit, 'bsc');
  }

  /**
   * Create fee options with different speeds
   */
  private createFeeOptions(baseGasPrice: bigint, gasLimit: bigint, chainType: ChainType): FeeOptions {
    const slowGasPrice = baseGasPrice;
    const standardGasPrice = baseGasPrice * BigInt(120) / BigInt(100); // +20%
    const fastGasPrice = baseGasPrice * BigInt(150) / BigInt(100); // +50%

    return {
      slow: {
        gasPrice: slowGasPrice.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatFee(slowGasPrice * gasLimit, chainType),
        feeInWei: slowGasPrice * gasLimit,
        speed: 'slow',
        estimatedTime: '~5 min'
      },
      standard: {
        gasPrice: standardGasPrice.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatFee(standardGasPrice * gasLimit, chainType),
        feeInWei: standardGasPrice * gasLimit,
        speed: 'standard',
        estimatedTime: '~2 min'
      },
      fast: {
        gasPrice: fastGasPrice.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatFee(fastGasPrice * gasLimit, chainType),
        feeInWei: fastGasPrice * gasLimit,
        speed: 'fast',
        estimatedTime: '~30s'
      }
    };
  }

  /**
   * Format fee for display
   */
  private formatFee(feeInWei: bigint, chainType: ChainType): string {
    const decimals = this.getChainDecimals(chainType);
    const fee = Number(feeInWei) / Math.pow(10, decimals);
    
    if (fee < 0.000001) {
      return fee.toFixed(8);
    } else if (fee < 0.001) {
      return fee.toFixed(6);
    } else {
      return fee.toFixed(4);
    }
  }

  /**
   * Format Solana fee
   */
  private formatSolanaFee(feeInLamports: bigint): string {
    const fee = Number(feeInLamports) / 1000000000; // Convert lamports to SOL
    return fee.toFixed(6);
  }

  /**
   * Get chain decimals
   */
  private getChainDecimals(chainType: ChainType): number {
    switch (chainType) {
      case 'ethereum':
      case 'polygon':
      case 'bsc':
        return 18;
      case 'solana':
        return 9;
      default:
        return 18;
    }
  }

  /**
   * Get fallback fees when estimation fails
   */
  private getFallbackFees(chainType: ChainType): FeeOptions {
    switch (chainType) {
      case 'ethereum':
        return this.createFeeOptions(BigInt('20000000000'), BigInt('21000'), chainType); // 20 gwei
      case 'polygon':
        return this.createFeeOptions(BigInt('30000000000'), BigInt('21000'), chainType); // 30 gwei
      case 'bsc':
        return this.createFeeOptions(BigInt('5000000000'), BigInt('21000'), chainType); // 5 gwei
      case 'solana':
        return this.getSolanaFallbackFees();
      default:
        return this.createFeeOptions(BigInt('20000000000'), BigInt('21000'), 'ethereum');
    }
  }

  /**
   * Get Solana fallback fees (synchronous version)
   */
  private getSolanaFallbackFees(): FeeOptions {
    const baseFee = BigInt('5000'); // 0.000005 SOL in lamports
    const gasLimit = BigInt('1');

    return {
      slow: {
        gasPrice: baseFee.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatSolanaFee(baseFee),
        feeInWei: baseFee,
        speed: 'slow',
        estimatedTime: '~30s'
      },
      standard: {
        gasPrice: baseFee.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatSolanaFee(baseFee),
        feeInWei: baseFee,
        speed: 'standard',
        estimatedTime: '~15s'
      },
      fast: {
        gasPrice: baseFee.toString(),
        gasLimit: gasLimit.toString(),
        totalFee: this.formatSolanaFee(baseFee),
        feeInWei: baseFee,
        speed: 'fast',
        estimatedTime: '~10s'
      }
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cached fee estimates if available
   */
  getCachedFees(chainType: ChainType, to: string, value: string = '0', data: string = '0x'): FeeOptions | null {
    const cacheKey = `${chainType}-${to}-${value}-${data}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    
    return null;
  }
}

// Export singleton instance
export const feeEstimationService = FeeEstimationService.getInstance();

/**
 * Hook for easy fee estimation in components
 */
export function useFeeEstimation() {
  return {
    getFeeEstimates: feeEstimationService.getFeeEstimates.bind(feeEstimationService),
    getCachedFees: feeEstimationService.getCachedFees.bind(feeEstimationService),
    clearCache: feeEstimationService.clearCache.bind(feeEstimationService)
  };
}
