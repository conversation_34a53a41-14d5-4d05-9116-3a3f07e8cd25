import { supabase } from './supabase';
import type { User as SupabaseUser } from '@supabase/auth-js';
import type { User, AdminAuthResult, EmailAddress } from '@/types';

export async function isUserAdmin(email: EmailAddress): Promise<boolean> {
  try {
    const { data: user } = await supabase
      .from('users')
      .select('is_admin')
      .eq('email', email)
      .single();

    return !!(user && user.is_admin);
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

function mapSupabaseUserToUser(supabaseUser: SupabaseUser): User {
  return {
    id: supabaseUser.id,
    email: supabaseUser.email || '',
    is_admin: false, // Will be set by the calling function
    created_at: supabaseUser.created_at || new Date().toISOString(),
    updated_at: supabaseUser.updated_at || new Date().toISOString(),
    last_login: supabaseUser.last_sign_in_at || undefined,
    email_verified: supabaseUser.email_confirmed_at !== null,
  };
}

export async function getCurrentUserAdminStatus(): Promise<AdminAuthResult> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return { isAdmin: false, user: null };
    }

    const isAdmin = await isUserAdmin(user.email || '');
    const mappedUser = mapSupabaseUserToUser(user);
    mappedUser.is_admin = isAdmin;
    
    return { isAdmin, user: mappedUser };
  } catch (error) {
    console.error('Error getting current user admin status:', error);
    return { isAdmin: false, user: null };
  }
}

export async function requireAdminAuth(): Promise<User> {
  const { isAdmin, user } = await getCurrentUserAdminStatus();
  
  if (!isAdmin || !user) {
    throw new Error('Admin access required');
  }
  
  return user;
} 