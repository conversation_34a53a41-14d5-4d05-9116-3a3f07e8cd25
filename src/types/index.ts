// =============================================================================
// MOKHBA WALLET - TYPE DEFINITIONS
// =============================================================================
// This file contains all TypeScript type definitions for the Mokhba Wallet
// application, ensuring type safety across the entire codebase.

// =============================================================================
// COMMON TYPES
// =============================================================================

export type UUID = string;
export type Timestamp = string; // ISO 8601 format
export type EmailAddress = string;
export type WalletAddress = string;
export type PhoneNumber = string;

// =============================================================================
// USER TYPES
// =============================================================================

export interface User {
  id: UUID;
  email: EmailAddress;
  is_admin: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
  last_login?: Timestamp;
  email_verified: boolean;
  profile?: UserProfile;
}

export interface UserProfile {
  id: UUID;
  user_id: UUID;
  full_name?: string;
  avatar_url?: string;
  preferred_language: SupportedLanguage;
  timezone?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export type SupportedLanguage = 'en' | 'ar';

// =============================================================================
// AUTHENTICATION TYPES
// =============================================================================

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}

export interface MagicLinkRequest {
  email: EmailAddress;
  captchaToken?: string;
}

export interface MagicLinkResponse {
  message: string;
  success: boolean;
}

export interface AdminAuthResult {
  isAdmin: boolean;
  user: User | null;
}

// =============================================================================
// CARD WAITLIST TYPES
// =============================================================================

export type CardInterest = 
  | 'online_purchases'
  | 'travel_spending'
  | 'fiat_withdrawal'
  | 'crypto_cashback'
  | 'defi_access'
  | 'other';

export type CardType = 'virtual' | 'physical' | 'both';
export type WaitlistStatus = 'waiting' | 'contacted' | 'approved' | 'declined';

export interface CardWaitlistEntry {
  id: UUID;
  full_name: string;
  email: EmailAddress;
  phone_number: PhoneNumber;
  country: string;
  interest?: CardInterest;
  card_type?: CardType;
  wallet_address?: WalletAddress;
  status: WaitlistStatus;
  position?: number;
  created_at: Timestamp;
  updated_at: Timestamp;
  contacted_at?: Timestamp;
  notes?: string;
}

export interface CardWaitlistRequest {
  full_name: string;
  email: EmailAddress;
  phone_number: PhoneNumber;
  country: string;
  interest?: CardInterest;
  card_type?: CardType;
  wallet_address?: WalletAddress;
  captchaToken?: string;
}

export interface CardWaitlistResponse {
  message: string;
  success: boolean;
}

export interface CardWaitlistListResponse {
  card_waitlist: CardWaitlistEntry[];
  total_count?: number;
  pagination?: PaginationInfo;
}

// =============================================================================
// SUPPORT TICKET TYPES
// =============================================================================

export type TicketStatus = 'open' | 'in_progress' | 'resolved' | 'closed';
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface SupportTicket {
  id: UUID;
  name: string;
  email: EmailAddress;
  subject: string;
  message: string;
  status: TicketStatus;
  priority: TicketPriority;
  wallet_address?: WalletAddress;
  assigned_to?: UUID;
  admin_notes?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
  resolved_at?: Timestamp;
}

export interface SupportTicketRequest {
  name: string;
  email: EmailAddress;
  subject: string;
  message: string;
  wallet_address?: WalletAddress;
  captchaToken?: string;
}

export interface SupportTicketResponse {
  message: string;
  success: boolean;
  ticket_id?: UUID;
}

export interface SupportTicketListResponse {
  support_tickets: SupportTicket[];
  total_count?: number;
  pagination?: PaginationInfo;
}

export interface SupportTicketUpdateRequest {
  status?: TicketStatus;
  priority?: TicketPriority;
  admin_notes?: string;
  assigned_to?: UUID;
}

// =============================================================================
// FEATURE REQUEST TYPES
// =============================================================================

export type FeatureCategory = 
  | 'feature'
  | 'collaboration'
  | 'feedback'
  | 'bug'
  | 'investment'
  | 'other';

export type FeatureStatus = 'submitted' | 'reviewing' | 'approved' | 'in_development' | 'completed' | 'rejected';
export type FeaturePriority = 'low' | 'medium' | 'high' | 'critical';

export interface FeatureRequest {
  id: UUID;
  name: string;
  email: EmailAddress;
  phone?: PhoneNumber;
  category: FeatureCategory;
  title: string;
  description: string;
  status: FeatureStatus;
  priority: FeaturePriority;
  wallet_address?: WalletAddress;
  admin_notes?: string;
  votes?: number;
  created_at: Timestamp;
  updated_at: Timestamp;
  completed_at?: Timestamp;
}

export interface FeatureRequestRequest {
  name: string;
  email: EmailAddress;
  phone?: PhoneNumber;
  category: FeatureCategory;
  title: string;
  description: string;
  wallet_address?: WalletAddress;
  captchaToken?: string;
}

export interface FeatureRequestResponse {
  message: string;
  success: boolean;
  request_id?: UUID;
}

export interface FeatureRequestListResponse {
  feature_requests: FeatureRequest[];
  total_count?: number;
  pagination?: PaginationInfo;
}

// =============================================================================
// STATUS SUBSCRIPTION TYPES
// =============================================================================

export interface StatusSubscription {
  id: UUID;
  email: EmailAddress;
  wallet_address?: WalletAddress;
  is_active: boolean;
  unsubscribe_token: string;
  created_at: Timestamp;
  updated_at: Timestamp;
  last_notification_sent?: Timestamp;
}

export interface StatusSubscriptionRequest {
  email: EmailAddress;
  wallet_address?: WalletAddress;
  captchaToken?: string;
}

export interface StatusSubscriptionResponse {
  message: string;
  success: boolean;
}

export interface StatusSubscriptionListResponse {
  status_subscriptions: StatusSubscription[];
  total_count?: number;
  pagination?: PaginationInfo;
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

export interface ApiSuccessResponse<T = unknown> {
  success: true;
  data: T;
  message?: string;
  timestamp: Timestamp;
}

export interface ApiErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: string;
  timestamp: Timestamp;
}

export type ApiResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse;

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// =============================================================================
// HEALTH CHECK TYPES
// =============================================================================

export interface HealthCheck {
  status: 'healthy' | 'unhealthy';
  timestamp: Timestamp;
  version?: string;
  environment?: string;
  checks: {
    database: boolean;
    environment: boolean;
    dependencies: boolean;
  };
  errors?: string[];
}

export interface DetailedHealthCheck extends HealthCheck {
  uptime: number;
  memory: NodeJS.MemoryUsage;
  envChecks: {
    supabaseUrlConfigured: boolean;
    supabaseAnonKeyConfigured: boolean;
    supabaseServiceKeyConfigured: boolean;
    nodeEnv: string;
  };
}

// =============================================================================
// RATE LIMITING TYPES
// =============================================================================

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

export interface RateLimitResponse {
  error: string;
  rateLimit: RateLimitInfo;
}

// =============================================================================
// CAPTCHA TYPES
// =============================================================================

export interface CaptchaVerificationRequest {
  token: string;
  ip?: string;
}

export interface CaptchaVerificationResponse {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

// =============================================================================
// WALLET TYPES
// =============================================================================

export type WalletProvider = 'metamask' | 'walletconnect' | 'coinbase';
export type ChainType = 'ethereum' | 'solana' | 'polygon' | 'bsc';

export interface WalletConnection {
  address: WalletAddress;
  provider: WalletProvider;
  chainType: ChainType;
  chainId?: number;
  isConnected: boolean;
  balance?: string;
}

export interface CryptoAsset {
  symbol: string;
  name: string;
  address?: string;
  decimals: number;
  balance: string;
  usdValue?: number;
  chainType: ChainType;
  logoUrl?: string;
}

// =============================================================================
// UI COMPONENT TYPES
// =============================================================================

export interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

export interface ComponentError {
  name: string;
  message: string;
  stack?: string;
  componentStack?: string;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// =============================================================================
// FORM TYPES
// =============================================================================

export interface FormFieldError {
  field: string;
  message: string;
  code?: string;
}

export interface FormState<T = Record<string, unknown>> {
  data: T;
  errors: FormFieldError[];
  isSubmitting: boolean;
  isValid: boolean;
  touched: Record<keyof T, boolean>;
}

// =============================================================================
// ADMIN DASHBOARD TYPES
// =============================================================================

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalTickets: number;
  openTickets: number;
  totalFeatureRequests: number;
  pendingFeatureRequests: number;
  totalWaitlistEntries: number;
  totalSubscriptions: number;
  lastUpdated: Timestamp;
}

export interface DashboardChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}

// =============================================================================
// SEARCH AND FILTER TYPES
// =============================================================================

export interface SearchFilters {
  query?: string;
  status?: string;
  category?: string;
  priority?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  offset?: number;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

// =============================================================================
// TYPE GUARDS
// =============================================================================

export function isApiErrorResponse(response: ApiResponse): response is ApiErrorResponse {
  return !response.success;
}

export function isApiSuccessResponse<T>(response: ApiResponse<T>): response is ApiSuccessResponse<T> {
  return response.success;
}

export function isValidEmail(email: string): email is EmailAddress {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidWalletAddress(address: string, chainType: ChainType): address is WalletAddress {
  switch (chainType) {
    case 'ethereum':
    case 'polygon':
    case 'bsc':
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    case 'solana':
      return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
    default:
      return false;
  }
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

// Database table names for type safety
export type TableName = 
  | 'users'
  | 'user_profiles'
  | 'card_waitlist'
  | 'support_tickets'
  | 'feature_requests'
  | 'status_subscriptions';

// Environment types
export type NodeEnvironment = 'development' | 'production' | 'test';

// =============================================================================
// CONSTANTS
// =============================================================================

export const VALID_CARD_INTERESTS: readonly CardInterest[] = [
  'online_purchases',
  'travel_spending',
  'fiat_withdrawal',
  'crypto_cashback',
  'defi_access',
  'other'
] as const;

export const VALID_CARD_TYPES: readonly CardType[] = [
  'virtual',
  'physical',
  'both'
] as const;

export const VALID_TICKET_STATUSES: readonly TicketStatus[] = [
  'open',
  'in_progress',
  'resolved',
  'closed'
] as const;

export const VALID_TICKET_PRIORITIES: readonly TicketPriority[] = [
  'low',
  'medium',
  'high',
  'urgent'
] as const;

export const VALID_FEATURE_CATEGORIES: readonly FeatureCategory[] = [
  'feature',
  'collaboration',
  'feedback',
  'bug',
  'investment',
  'other'
] as const;

export const SUPPORTED_LANGUAGES: readonly SupportedLanguage[] = ['en', 'ar'] as const; 