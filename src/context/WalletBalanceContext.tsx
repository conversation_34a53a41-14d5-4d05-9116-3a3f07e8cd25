'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';

interface BalanceData {
  balance: string;
  symbol: string;
  decimals: number;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
}

interface WalletBalanceContextType {
  getBalance: (address: string, tokenAddress?: string) => BalanceData | null;
  refreshBalance: (address: string, tokenAddress?: string) => void;
  isRefreshing: boolean;
}

const WalletBalanceContext = createContext<WalletBalanceContextType | undefined>(undefined);

// Cache duration: 30 seconds
const CACHE_DURATION = 30000;

// Request deduplication
const pendingRequests = new Map<string, Promise<any>>();

interface WalletBalanceProviderProps {
  children: React.ReactNode;
}

export function WalletBalanceProvider({ children }: WalletBalanceProviderProps) {
  const [balanceCache, setBalanceCache] = useState<Map<string, BalanceData>>(new Map());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Only access wagmi hooks on client side
  let account = { address: null as string | null, chainId: 1, isConnected: false };
  
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && typeof window !== 'undefined') {
      // Dynamically import and use wagmi hooks only on client side
      const initializeAccount = async () => {
        try {
          const { useAccount } = await import('wagmi');
          // This will only work in a React component, so we'll handle it differently
        } catch (error) {
          console.warn('Wagmi not available:', error);
        }
      };
      initializeAccount();
    }
  }, [mounted]);

  // Generate cache key for balance requests
  const getCacheKey = useCallback((address: string, tokenAddress?: string): string => {
    return `${address}-${tokenAddress || 'native'}`;
  }, []);

  // Check if cached data is still valid
  const isCacheValid = useCallback((data: BalanceData): boolean => {
    return Date.now() - data.lastUpdated < CACHE_DURATION;
  }, []);

  // Get balance from cache or return null if not available/expired
  const getBalance = useCallback((address: string, tokenAddress?: string): BalanceData | null => {
    const key = getCacheKey(address, tokenAddress);
    const cached = balanceCache.get(key);
    
    if (cached && isCacheValid(cached)) {
      return cached;
    }
    
    return null;
  }, [balanceCache, getCacheKey, isCacheValid]);

  // Refresh balance for specific address
  const refreshBalance = useCallback(async (address: string, tokenAddress?: string) => {
    if (!mounted || typeof window === 'undefined') return;
    
    const key = getCacheKey(address, tokenAddress);
    
    // Prevent duplicate requests
    if (pendingRequests.has(key)) {
      return pendingRequests.get(key);
    }

    setIsRefreshing(true);

    const request = (async () => {
      try {
        // Set loading state
        setBalanceCache(prev => new Map(prev).set(key, {
          balance: prev.get(key)?.balance || '0',
          symbol: prev.get(key)?.symbol || 'ETH',
          decimals: prev.get(key)?.decimals || 18,
          isLoading: true,
          error: null,
          lastUpdated: Date.now()
        }));

        // Use RPC proxy for all addresses
        const response = await fetch('/api/rpc', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            method: 'eth_getBalance',
            params: [address, 'latest'],
            chainId: account.chainId || 1
          })
        });

        if (!response.ok) {
          throw new Error(`RPC request failed: ${response.status}`);
        }

        const data = await response.json();
        if (data.error) {
          throw new Error(data.error.message);
        }

        // Convert wei to ether
        const balance = (parseInt(data.result, 16) / 1e18).toString();
        
        const balanceData = {
          balance,
          symbol: 'ETH',
          decimals: 18,
          isLoading: false,
          error: null,
          lastUpdated: Date.now()
        };

        setBalanceCache(prev => new Map(prev).set(key, balanceData));

      } catch (error) {
        const errorData: BalanceData = {
          balance: '0',
          symbol: 'ETH',
          decimals: 18,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to fetch balance',
          lastUpdated: Date.now()
        };
        
        setBalanceCache(prev => new Map(prev).set(key, errorData));
      } finally {
        pendingRequests.delete(key);
        setIsRefreshing(false);
      }
    })();

    pendingRequests.set(key, request);
    return request;
  }, [mounted, account.chainId, getCacheKey]);

  // Cleanup old cache entries periodically
  useEffect(() => {
    if (!mounted) return;
    
    const cleanup = setInterval(() => {
      setBalanceCache(prev => {
        const now = Date.now();
        const filtered = new Map();
        
        Array.from(prev.entries()).forEach(([key, data]) => {
          if (now - data.lastUpdated < CACHE_DURATION * 2) {
            filtered.set(key, data);
          }
        });
        
        return filtered;
      });
    }, CACHE_DURATION);

    return () => clearInterval(cleanup);
  }, [mounted]);

  const contextValue = useMemo(() => ({
    getBalance,
    refreshBalance,
    isRefreshing
  }), [getBalance, refreshBalance, isRefreshing]);

  // Don't render until mounted to prevent SSR issues
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <WalletBalanceContext.Provider value={contextValue}>
      {children}
    </WalletBalanceContext.Provider>
  );
}

export function useWalletBalance() {
  const context = useContext(WalletBalanceContext);
  if (context === undefined) {
    throw new Error('useWalletBalance must be used within a WalletBalanceProvider');
  }
  return context;
}

// Hook for getting balance with automatic refresh and caching
export function useBalanceOptimized(address?: string, tokenAddress?: string) {
  const { getBalance, refreshBalance } = useWalletBalance();
  const [balanceData, setBalanceData] = useState<BalanceData | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !address) {
      setBalanceData(null);
      return;
    }

    // Try to get from cache first
    const cached = getBalance(address, tokenAddress);
    if (cached) {
      setBalanceData(cached);
    } else {
      // If not in cache or expired, refresh
      refreshBalance(address, tokenAddress);
    }
  }, [mounted, address, tokenAddress, getBalance, refreshBalance]);

  // Listen for cache updates
  useEffect(() => {
    if (!mounted || !address) return;

    const interval = setInterval(() => {
      const updated = getBalance(address, tokenAddress);
      if (updated && updated.lastUpdated > (balanceData?.lastUpdated || 0)) {
        setBalanceData(updated);
      }
    }, 1000); // Check for updates every second

    return () => clearInterval(interval);
  }, [mounted, address, tokenAddress, getBalance, balanceData?.lastUpdated]);

  return balanceData;
} 