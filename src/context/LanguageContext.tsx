'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type Language = 'en' | 'ar';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
  t: (key: string) => string;
}

const translations = {
  en: {
    'nav.security': 'Security',
    'nav.learn': 'Learn',
    'nav.explore': 'Explore',
    'nav.support': 'Support',
    'nav.launch': 'Launch Mokhba',
    'hero.title': 'One wallet for all your digital assets',
    'hero.subtitle': 'The First Arabic Wallet that empowers users to securely store and effortlessly manage their crypto assets.',
    'hero.cta': 'Be part of it',
    'arabic.title': 'First Arabic Cryptocurrency Wallet',
    'arabic.description': 'Mokhba is designed with Arab users in mind, providing a seamless experience with full Arabic language support and Arabic UI.',
    'arabic.learnMore': 'Learn More',
    'arabic.support': 'Arabic Language Support',
    'storage.title': 'Self-Custody Made Easy',
    'feature.secure.title': 'Secure by Design',
    'feature.secure.description': 'Your private keys always stay on your device. We use industry-standard encryption to protect your digital assets.',
    'feature.recovery.title': 'Easy Recovery',
    'feature.recovery.description': 'Backup your wallet with a simple recovery phrase. Access your assets from any device, anytime.',
    'feature.multichain.title': 'Multi-Chain Support',
    'feature.multichain.description': 'Manage your assets across multiple blockchains from one place. Support for Ethereum, Binance Smart Chain, and more.',
    'footer.description': 'The First Arabic Cryptocurrency Wallet.',
    'footer.company': 'Company',
    'footer.about': 'About',
    'footer.terms': 'Terms',
    'footer.privacy': 'Privacy',
    'footer.status': 'Status',
    'footer.products': 'Products',
    'footer.download': 'Download',
    'footer.security': 'Security',
    'footer.support': 'Support',
    'footer.feature': 'Got a feature idea or want to collaborate',
    'footer.resources': 'Resources',
    'footer.explore': 'Explore',
    'footer.learn': 'Learn',
    'footer.blog': 'Blog',
    'footer.docs': 'Docs',

    'app.getStarted': 'Get Started with Mokhba',
    'app.description': 'View and manage your digital assets securely in one place.',
    'app.watchAddress': 'Watch Address',
    'app.wallet': 'Mokhba Wallet',
    'app.moveCrypto': 'Move Crypto',
    'app.upgrade': 'Upgrade',
    'app.upgradeDescription': 'Unlock premium features',
    'app.upgradeCta': 'Upgrade Now',

    // Receive Page Legacy (kept for compatibility)
    'receive.yourAddress': 'Your Address',
    'receive.showQR': 'Show QR Code',
    'receive.warning': 'Only send Ethereum (ETH) and ERC-20 tokens to this address. Sending other tokens may result in permanent loss.',

    // Common
    'common.connectWallet': 'Connect Wallet',
    'common.ethereum': 'Ethereum',
    'common.expand': 'Expand',
    'common.collapse': 'Collapse',
    'common.comingSoon': 'Coming Soon',

    // Error Boundary
    'error.title': 'Something went wrong',
    'error.description': 'An error occurred while loading this page.',
    'error.refresh': 'Refresh Page',

    // Support Page
    'support.title': 'Support Center',
    'support.subtitle': 'Get help with Mokhba wallet and find answers to frequently asked questions.',
    'support.categories.gettingStarted': 'Getting Started',
    'support.categories.account': 'Account',
    'support.categories.transactions': 'Transactions',
    'support.categories.security': 'Security',
    'support.categories.troubleshooting': 'Troubleshooting',
    'support.categories.contactUs': 'Contact Us',
    'support.faq.title': 'Frequently Asked Questions',
    'support.faq.createWallet.question': 'How do I create a wallet?',
    'support.faq.createWallet.answer': 'Creating a wallet is simple. Download our app, tap "Create new wallet", and follow the setup process. Make sure to securely store your recovery phrase.',
    'support.faq.safety.question': 'Is my cryptocurrency safe with Mokhba?',
    'support.faq.safety.answer': 'Yes, your cryptocurrency is safe. Mokhba is a non-custodial wallet, meaning your private keys never leave your device. You have complete control over your assets.',
    'support.faq.supported.question': 'What cryptocurrencies are supported?',
    'support.faq.supported.answer': 'Mokhba supports major cryptocurrencies including Bitcoin, Ethereum, and many ERC-20 tokens. We continuously add support for more assets.',
    'support.faq.recover.question': 'How do I recover my wallet?',
    'support.faq.recover.answer': 'You can recover your wallet using the 12-24 word recovery phrase you saved when creating your wallet. Simply choose "Import wallet" and enter your phrase.',
    'support.needHelp.title': 'Still need help?',
    'support.needHelp.description': "Can't find what you're looking for? Our support team is here to help.",
    'support.contactSupport': 'Contact Support',
    
    // Support Ticket Form
    'support.ticket.title': 'Submit Support Ticket',
    'support.ticket.description': 'Please provide detailed information about your issue so we can assist you better.',
    'support.ticket.name': 'Full Name',
    'support.ticket.namePlaceholder': 'Enter your full name',
    'support.ticket.email': 'Email Address',
    'support.ticket.emailPlaceholder': 'Enter your email address',
    'support.ticket.subject': 'Subject',
    'support.ticket.subjectPlaceholder': 'Brief description of your issue',
    'support.ticket.message': 'Message',
    'support.ticket.messagePlaceholder': 'Please describe your issue in detail...',
    'support.ticket.priority': 'Priority',
    'support.ticket.walletAddress': 'Wallet Address (Optional)',
    'support.ticket.walletPlaceholder': 'Your wallet address if relevant',
    'support.ticket.submit': 'Submit Ticket',
    'support.ticket.submitting': 'Submitting...',
    'support.ticket.cancel': 'Cancel',
    'support.ticket.submitSuccess': 'Support ticket submitted successfully! We\'ll get back to you soon.',
    'support.ticket.submitError': 'Failed to submit support ticket',
    'support.ticket.networkError': 'Network error. Please try again.',
    
    // Priority levels
    'support.priority.low': 'Low',
    'support.priority.medium': 'Medium',
    'support.priority.high': 'High',
    'support.priority.urgent': 'Urgent',

    // Feature Request Page
    'featureRequest.title': 'Be Part of Our Journey',
    'featureRequest.subtitle': 'Join us in shaping the future of Mokhba through collaboration, suggestions, and partnership opportunities.',
    'featureRequest.thankYou': 'Welcome aboard!',
    'featureRequest.submitted': 'Thank you for wanting to be part of our journey! Our team will review your submission and get back to you soon.',
    'featureRequest.submitTitle': 'Share Your Ideas & Join Our Mission',
    'featureRequest.name': 'Full Name',
    'featureRequest.namePlaceholder': 'Enter your full name',
    'featureRequest.email': 'Email Address',
    'featureRequest.emailPlaceholder': 'Enter your email address',
    'featureRequest.phone': 'Phone Number (Optional)',
    'featureRequest.phonePlaceholder': 'Enter your phone number',
    'featureRequest.category': 'Category',
    'featureRequest.categoryPlaceholder': 'Select a category',
    'featureRequest.categories.feature': 'Suggest a Feature',
    'featureRequest.categories.collaboration': 'Explore a Collaboration',
    'featureRequest.categories.feedback': 'Share Feedback',
    'featureRequest.categories.bug': 'Report a Bug',
    'featureRequest.categories.investment': 'Investment or Partnership Inquiry',
    'featureRequest.categories.other': 'Other',
    'featureRequest.featureTitle': 'Your Idea or Proposal',
    'featureRequest.titlePlaceholder': 'Brief title for your idea, suggestion, or proposal',
    'featureRequest.description': 'Details',
    'featureRequest.descriptionPlaceholder': 'Share your thoughts, ideas, or how you would like to contribute to Mokhba',
    'featureRequest.walletAddress': 'Wallet Address (Optional)',
    'featureRequest.walletAddressPlaceholder': 'Enter your wallet address if relevant',
    'featureRequest.submit': 'Join Our Journey',
    'featureRequest.submitting': 'Submitting...',
    'featureRequest.popular': 'Community Contributions & Ideas',
    'featureRequest.process.title': 'How We Work Together',
    'featureRequest.process.step1': '1. Share Ideas',
    'featureRequest.process.step1Description': 'Community members share ideas, suggestions, and collaboration opportunities',
    'featureRequest.process.step2': '2. Connect & Discuss', 
    'featureRequest.process.step2Description': 'We review submissions and connect with contributors to explore possibilities',
    'featureRequest.process.step3': '3. Collaborate',
    'featureRequest.process.step3Description': 'We work together on implementations, partnerships, or feature development',
    'featureRequest.process.step4': '4. Launch & Celebrate',
    'featureRequest.process.step4Description': 'Successful collaborations are launched and contributors are recognized',

    // Learn Page
    'learn.title': 'Learn About Crypto',
    'learn.subtitle': 'Expand your knowledge about cryptocurrency, blockchain technology, and digital finance.',
    'learn.resources.cryptoBasics': 'Crypto Basics',
    'learn.resources.cryptoBasicsDesc': 'Learn the fundamentals of cryptocurrency, blockchain technology, and digital wallets.',
    'learn.resources.advancedTrading': 'Advanced Trading',
    'learn.resources.advancedTradingDesc': 'Discover strategies for trading cryptocurrencies and managing your portfolio.',
    'learn.resources.securityPractices': 'Security Best Practices',
    'learn.resources.securityPracticesDesc': 'Protect your assets with essential security tips and best practices.',
    'learn.resources.defiExplained': 'DeFi Explained',
    'learn.resources.defiExplainedDesc': 'Understand decentralized finance and how to participate in DeFi protocols.',
    'learn.resources.nftOwnership': 'NFTs & Digital Ownership',
    'learn.resources.nftOwnershipDesc': 'Explore the world of non-fungible tokens and digital collectibles.',
    'learn.resources.web3Development': 'Web3 Development',
    'learn.resources.web3DevelopmentDesc': 'Get started with building decentralized applications on blockchain.',
    'learn.tags.beginner': 'Beginner',
    'learn.tags.advanced': 'Advanced',
    'learn.tags.essential': 'Essential',
    'learn.tags.intermediate': 'Intermediate',
    'learn.tags.trending': 'Trending',
    'learn.tags.technical': 'Technical',
    'learn.learnMore': 'Learn more',
    'learn.readyToDive': 'Ready to dive deeper?',
    'learn.docsDescription': 'Check out our comprehensive documentation and tutorials to become a crypto expert.',
    'learn.exploreDocs': 'Explore Documentation',

    // Security Page
    'security.title': 'Security at Mokhba',
    'security.subtitle': 'Learn how we protect your assets with industry-leading security features.',
    'security.features.nonCustodial.title': 'Non-Custodial Security',
    'security.features.nonCustodial.description': 'Your private keys are stored locally on your device. We never have access to your funds.',
    'security.features.encryption.title': 'End-to-End Encryption',
    'security.features.encryption.description': 'All data transmission is encrypted using industry-standard protocols.',
    'security.features.recovery.title': 'Recovery Phrase Protection',
    'security.features.recovery.description': 'Secure backup and recovery system to protect your assets.',
    'security.features.mfa.title': 'Multi-Factor Authentication',
    'security.features.mfa.description': 'Additional security layers to protect your account access.',
    'security.features.nonCustodial.details.1': 'Private keys never leave your device',
    'security.features.nonCustodial.details.2': 'You maintain full control of your assets',
    'security.features.nonCustodial.details.3': 'Zero-knowledge architecture',
    'security.features.nonCustodial.details.4': 'Decentralized key management',
    'security.features.encryption.details.1': 'AES-256 encryption standard',
    'security.features.encryption.details.2': 'TLS 1.3 for data transmission',
    'security.features.encryption.details.3': 'Encrypted local storage',
    'security.features.encryption.details.4': 'Hardware security modules (HSM)',
    'security.features.recovery.details.1': 'BIP-39 compliant seed phrases',
    'security.features.recovery.details.2': 'Secure backup options',
    'security.features.recovery.details.3': 'Social recovery methods',
    'security.features.recovery.details.4': 'Hardware wallet integration',
    'security.features.mfa.details.1': 'Biometric authentication',
    'security.features.mfa.details.2': 'Hardware token support',
    'security.features.mfa.details.3': 'SMS and email verification',
    'security.features.mfa.details.4': 'App-based authenticators',
    'security.protocols.auditing.title': 'Smart Contract Auditing',
    'security.protocols.auditing.description': 'Regular third-party security audits of all smart contracts',
    'security.protocols.bugBounty.title': 'Bug Bounty Program',
    'security.protocols.bugBounty.description': 'Incentivizing security researchers to find vulnerabilities',
    'security.protocols.penetration.title': 'Penetration Testing',
    'security.protocols.penetration.description': 'Regular security assessments by certified ethical hackers',
    'security.protocols.compliance.title': 'Compliance Monitoring',
    'security.protocols.compliance.description': 'Adherence to international security standards and regulations',
    'security.metrics.securityScore': 'Security Score',
    'security.metrics.uptime': 'Uptime',
    'security.metrics.incidents': 'Incidents',
    'security.metrics.responseTime': 'Response Time',
    'security.tabs.features': 'Security Features',
    'security.tabs.protocols': 'Security Protocols',
    'security.tabs.metrics': 'Security Metrics',
    'security.tabs.features.description': 'Core security mechanisms',
    'security.tabs.protocols.description': 'Security processes & audits',
    'security.tabs.metrics.description': 'Real-time monitoring data',
    'security.overview': 'Security Overview',
    'security.advancedFeatures': 'Advanced Security Features',
    'security.advancedFeaturesDescription': 'Our multi-layered security architecture ensures your digital assets remain protected at all times',
    'security.protocolsProcesses': 'Security Protocols & Processes',
    'security.protocolsDescription': 'Comprehensive security measures and ongoing monitoring to maintain the highest standards',
    'security.metricsMonitoring': 'Security Metrics & Monitoring',
    'security.metricsDescription': 'Real-time security status and comprehensive compliance certifications',
    'security.status.active': 'Active',
    'security.status.quarterly': 'Quarterly',
    'security.status.continuous': 'Continuous',
    'security.status.implementation': 'Implementation',
    'security.status.complete': 'Complete',
    'security.dashboard.title': 'Real-time Security Dashboard',
    'security.dashboard.systemIntegrity': 'System Integrity',
    'security.dashboard.activeMonitoring': 'Active Monitoring',
    'security.dashboard.encryptionLevel': 'Encryption Level',
    'security.certifications.title': 'Security Certifications & Standards',
    'security.responseTeam.title': 'Security Response Team',
    'security.responseTeam.description': 'Have a security concern? Our dedicated security team is available 24/7 to address any issues.',
    'security.responseTeam.reportVulnerability': 'Report Vulnerability',
    'security.commitment.title': 'Our Security Commitment',
    'security.commitment.description1': 'At Mokhba, security is our top priority. We employ multiple layers of protection to ensure your digital assets remain safe while maintaining a seamless user experience.',
    'security.commitment.description2': 'Our team of security experts continuously monitors for potential threats and vulnerabilities, implementing the latest security measures to stay ahead of emerging risks in the cryptocurrency space.',

    // Explore Page
    'explore.title': 'Explore the Crypto Ecosystem',
    'explore.subtitle': 'Discover new projects, tokens, and opportunities in the decentralized world.',
    'explore.comingSoon': 'Coming Soon',
    'explore.description': "We're building an amazing exploration experience for the crypto ecosystem. Stay tuned!",
    'explore.backToDashboard': 'Back to Dashboard',

    // Blog Page
    'blog.title': 'Blog',
    'blog.subtitle': 'Cryptocurrency news, tutorials, and insights from the Mokhba team',
    'blog.readMore': 'Read more',
    'blog.read': 'Read',
    'blog.newsletter.title': 'Subscribe to Our Newsletter',
    'blog.newsletter.description': 'Get the latest updates, tutorials, and insights about cryptocurrency and blockchain technology delivered straight to your inbox.',
    'blog.newsletter.placeholder': 'Your email address',
    'blog.newsletter.subscribe': 'Subscribe',
    'blog.categories.all': 'All',
    'blog.categories.announcements': 'Announcements',
    'blog.categories.security': 'Security',
    'blog.categories.education': 'Education',
    'blog.categories.tutorials': 'Tutorials',
    'blog.categories.marketInsights': 'Market Insights',
    'blog.posts.1.title': 'Introducing Mokhba: The First Arabic Cryptocurrency Wallet',
    'blog.posts.1.excerpt': 'Learn about our mission to create a secure, user-friendly wallet for the MENA region and beyond.',
    'blog.posts.1.author': 'Sarah Ahmed',
    'blog.posts.2.title': 'Security Best Practices for Crypto Wallets',
    'blog.posts.2.excerpt': 'Essential tips to keep your digital assets safe from common threats and vulnerabilities.',
    'blog.posts.2.author': 'Omar Farouk',
    'blog.posts.3.title': 'Understanding Layer 2 Solutions: Arbitrum, Optimism, and Base',
    'blog.posts.3.excerpt': 'A comprehensive guide to Ethereum scaling solutions and how they improve transaction speed and cost.',
    'blog.posts.3.author': 'Mohammed Khalid',
    'blog.posts.4.title': 'The Rise of DeFi in the MENA Region',
    'blog.posts.4.excerpt': 'Exploring the growing adoption of decentralized finance across the Middle East and North Africa.',
    'blog.posts.4.author': 'Layla Hassan',
    'blog.posts.5.title': 'How to Bridge Assets Between Different Blockchains',
    'blog.posts.5.excerpt': 'A step-by-step guide to moving your crypto assets across different networks using Mokhba.',
    'blog.posts.5.author': 'Mohammed Khalid',
    'blog.posts.6.title': 'Upcoming Features: What\'s Next for Mokhba',
    'blog.posts.6.excerpt': 'A sneak peek at the exciting new features and improvements coming to Mokhba in Q2 2024.',
    'blog.posts.6.author': 'Sarah Ahmed',

    // Discover Page
    'discover.title': 'Discover Crypto Markets',
    'discover.subtitle': 'Live prices, trends, and market insights at your fingertips',
    'discover.livePrices': 'Live Prices',
    'discover.topMovers': 'Top Movers',
    'discover.trending': 'Trending',
    'discover.globalStats': 'Global Market Stats',
    'discover.categories': 'Categories',
    'discover.marketCap': 'Market Cap',
    'discover.volume24h': 'Volume (24h)',
    'discover.change24h': 'Change (24h)',
    'discover.price': 'Price',
    'discover.totalMarketCap': 'Total Market Cap',
    'discover.totalVolume': 'Total Volume',
    'discover.btcDominance': 'BTC Dominance',
    'discover.activeCryptos': 'Active Cryptocurrencies',
    'discover.markets': 'Markets',
    'discover.exchanges': 'Exchanges',
    'discover.gainers': 'Top Gainers',
    'discover.losers': 'Top Losers',
    'discover.loadingMarketData': 'Loading market data...',
    'discover.error': 'Error loading data',
    'discover.retry': 'Retry',
    'discover.viewAll': 'View All',
    'discover.searchPlaceholder': 'Search cryptocurrencies...',
    'discover.noResults': 'No results found',
    'discover.rank': 'Rank',
    'discover.coin': 'Coin',
    'discover.lastUpdated': 'Last updated',
    'discover.timeAgo': 'ago',
    'discover.defi': 'DeFi',
    'discover.nft': 'NFT',
    'discover.gaming': 'Gaming',
    'discover.metaverse': 'Metaverse',
    'discover.layer1': 'Layer 1',
    'discover.layer2': 'Layer 2',
    'discover.memecoins': 'Meme Coins',
    'discover.stablecoins': 'Stablecoins',

    // Card Page
    'card.title': 'Mokhba Card',
    'card.subtitle': 'The Future of Digital Payments is Here',
    'card.description': 'Seamlessly bridge crypto and fiat with our revolutionary card. Coming soon.',
    'card.comingSoon': 'Coming Soon',
    'card.waitlistDescription': 'Join the waitlist for the Mokhba Card and spend your crypto anywhere.',
    'card.cardholder': 'Cardholder',
    'card.expires': 'Expires',
    'card.mockaUser': 'MOKHBA USER',
    'card.joinWaitlist': 'Join Waitlist',
    'card.contactless': 'Contactless',
    'card.contactlessDesc': 'Tap to Pay',
    'card.bankLevel': 'Bank-Level',
    'card.secure': 'Secure',
    'card.instant': 'Instant',
    'card.instantDesc': 'Transfers',
    'card.global': 'Global',
    'card.globalDesc': 'Acceptance',
    'card.peopleWaiting': '1,200+ people waiting',
    'card.features.title': 'Card Features',
    'card.features.digitalFirst': 'Digital First',
    'card.features.digitalFirstDesc': 'Built for the digital age with full app control',
    'card.features.cryptoSpend': 'Spend Crypto',
    'card.features.cryptoSpendDesc': 'Spend your cryptocurrency directly anywhere Visa is accepted',
    'card.features.realTime': 'Real-time Transactions',
    'card.features.realTimeDesc': 'Instant conversions and balance updates with every transaction',
    'card.features.security': 'Advanced Security',
    'card.features.securityDesc': 'Advanced fraud protection with bank-grade encryption',
    'card.form.title': 'Join the Waitlist',
    'card.form.description': 'Be among the first to get Mokhba Card when it launches',
    'card.form.fullName': 'Full Name',
    'card.form.email': 'Email Address',
    'card.form.phone': 'Phone Number',
    'card.form.country': 'Country',
    'card.form.interest': 'Primary Interest',
    'card.form.cardType': 'Preferred Card Type',
    'card.form.walletAddress': 'Wallet Address (Optional)',
    'card.form.onlinePurchases': 'Online Purchases',
    'card.form.travelSpending': 'Travel/Spending Abroad',
    'card.form.fiatWithdrawal': 'Fiat Withdrawal',
    'card.form.cryptoCashback': 'Earning Cashback in Crypto',
    'card.form.defiAccess': 'Access to DeFi or NFTs',
    'card.form.other': 'Other',
    'card.form.virtual': 'Virtual',
    'card.form.physical': 'Physical',
    'card.form.both': 'Both',
    'card.form.submit': 'Join Waitlist',
    'card.form.submitting': 'Joining...',
    'card.form.success': 'Successfully joined the waitlist! We\'ll contact you when the card becomes available.',
    'card.form.error': 'Failed to join waitlist',
    'card.form.alreadyJoined': 'You\'re already on the waitlist!',
    'card.form.networkError': 'Network error. Please try again.',
    'card.form.fullNamePlaceholder': 'Enter your full name',
    'card.form.emailPlaceholder': 'Enter your email address',
    'card.form.phonePlaceholder': 'Enter your phone number',
    'card.form.countryPlaceholder': 'Enter your country',
    'card.form.walletAddressPlaceholder': 'Enter your wallet address',
    'card.backToApp': 'Back to App',

    // About Page
    'about.title': 'About Mokhba',
    'about.subtitle': 'The first Arabic cryptocurrency wallet designed for the MENA region.',
    'about.ourStory': 'Our Story',
    'about.storyParagraph1': 'Mokhba was founded in 2023 with a clear mission: to create a secure, user-friendly cryptocurrency wallet that serves the unique needs of users in the Middle East and North Africa.',
    'about.storyParagraph2': 'We recognized that while cryptocurrency adoption was growing rapidly worldwide, there was a significant gap in solutions designed specifically for Arabic-speaking users. Language barriers, complex interfaces, and lack of regional support were preventing many from participating in the digital economy.',
    'about.storyParagraph3': 'Our team of blockchain experts, security specialists, and designers came together to build a wallet that not only provides best-in-class security but also offers a seamless experience in both Arabic and English, with features tailored to the region\'s specific needs.',
    'about.storyParagraph4': 'Today, Mokhba is growing rapidly, serving users across the MENA region and beyond, while maintaining our core commitment to security, accessibility, and innovation.',
    'about.ourValues': 'Our Values',
    'about.values.security': 'Security First',
    'about.values.empowerment': 'User Empowerment',
    'about.values.transparency': 'Transparency',
    'about.values.innovation': 'Innovation',
    'about.values.inclusivity': 'Inclusivity',
    'about.values.community': 'Community',
    'about.meetTeam': 'Meet Our Team',
    'about.team.sarah.name': 'Sarah Ahmed',
    'about.team.sarah.role': 'Founder & CEO',
    'about.team.sarah.bio': 'Blockchain enthusiast with 10+ years in fintech. Previously led product at major crypto exchanges.',
    'about.team.mohammed.name': 'Mohammed Khalid',
    'about.team.mohammed.role': 'CTO',
    'about.team.mohammed.bio': 'Experienced software architect specializing in secure, scalable blockchain applications.',
    'about.team.layla.name': 'Layla Hassan',
    'about.team.layla.role': 'Head of Design',
    'about.team.layla.bio': 'Award-winning UX designer focused on creating intuitive interfaces for complex technologies.',
    'about.team.omar.name': 'Omar Farouk',
    'about.team.omar.role': 'Head of Security',
    'about.team.omar.bio': 'Cybersecurity expert with background in cryptography and secure systems design.',
    'about.joinJourney': 'Join Our Journey',
    'about.joinDescription': 'We\'re on a mission to make cryptocurrency accessible to everyone in the MENA region. Whether you\'re a user, partner, or potential team member, we\'d love to connect with you.',
    'about.getInTouch': 'Get in Touch',

    // Terms Page
    'terms.title': 'Terms of Service',
    'terms.subtitle': 'Please read these terms carefully before using Mokhba wallet.',
    'terms.lastUpdated': 'Last updated: May 9, 2024',
    'terms.acceptance.title': 'Acceptance of Terms',
    'terms.acceptance.content': 'By accessing or using Mokhba wallet, you agree to be bound by these Terms of Service. If you do not agree to all the terms and conditions, you must not use our services.',
    'terms.services.title': 'Services Description',
    'terms.services.content': 'Mokhba provides a non-custodial cryptocurrency wallet that allows users to store, send, receive, and manage digital assets. We do not have access to your private keys or funds, and cannot recover them if lost.',
    'terms.responsibilities.title': 'User Responsibilities',
    'terms.responsibilities.content': 'You are responsible for maintaining the confidentiality of your recovery phrase and private keys. You must not share these with anyone. You are solely responsible for all activities that occur under your account.',
    'terms.risks.title': 'Risks',
    'terms.risks.content': 'Cryptocurrency investments involve significant risk. Prices can fluctuate significantly, and past performance is not indicative of future results. You should carefully consider your investment objectives and risk tolerance before using our services.',
    'terms.prohibited.title': 'Prohibited Activities',
    'terms.prohibited.content': 'You agree not to use our services for any illegal purposes, including but not limited to money laundering, terrorist financing, or fraudulent activities. We reserve the right to report suspicious activities to relevant authorities.',
    'terms.intellectual.title': 'Intellectual Property',
    'terms.intellectual.content': 'All content, design, graphics, compilation, digital conversion, and other matters related to the Mokhba wallet are protected by copyright, trademark, and other intellectual property laws.',
    'terms.liability.title': 'Limitation of Liability',
    'terms.liability.content': 'To the maximum extent permitted by law, Mokhba and its affiliates shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including loss of profits, data, or goodwill.',
    'terms.modifications.title': 'Modifications to Terms',
    'terms.modifications.content': 'We reserve the right to modify these terms at any time. We will provide notice of significant changes by updating the date at the top of these terms and by maintaining a current version on our website.',
    'terms.governing.title': 'Governing Law',
    'terms.governing.content': 'These terms shall be governed by and construed in accordance with the laws of [Jurisdiction], without regard to its conflict of law provisions.',
    'terms.contact.title': 'Contact Information',
    'terms.contact.content': 'If you have any questions about these Terms, please contact <NAME_EMAIL>.',
    'terms.agreement': 'By using Mokhba wallet, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.',

    // Privacy Page
    'privacy.title': 'Privacy Policy',
    'privacy.subtitle': 'Learn how we collect, use, and protect your information.',
    'privacy.lastUpdated': 'Last updated: May 9, 2024',
    'privacy.overview.title': 'Overview',
    'privacy.overview.content': 'At Mokhba, we are committed to protecting your privacy and being transparent about how we handle your data. This Privacy Policy explains what information we collect, how we use it, and what choices you have.',
    'privacy.dataCollection.title': 'Information We Collect',
    'privacy.dataCollection.content': 'We collect minimal information necessary to provide our services, including device information, usage analytics, and any information you voluntarily provide when contacting support.',
    'privacy.nonCustodial.title': 'Non-Custodial Nature',
    'privacy.nonCustodial.content': 'As a non-custodial wallet, we do not have access to your private keys, seed phrases, or funds. Your sensitive information remains on your device at all times.',
    'privacy.dataUse.title': 'How We Use Your Information',
    'privacy.dataUse.content': 'We use collected information to improve our services, provide customer support, ensure security, and comply with legal obligations. We never sell your personal information to third parties.',
    'privacy.dataSharing.title': 'Information Sharing',
    'privacy.dataSharing.content': 'We do not share your personal information with third parties except as required by law or to protect our rights and the safety of our users.',
    'privacy.security.title': 'Data Security',
    'privacy.security.content': 'We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
    'privacy.retention.title': 'Data Retention',
    'privacy.retention.content': 'We retain your information only for as long as necessary to provide our services and comply with legal obligations. You can request deletion of your account data at any time.',
    'privacy.rights.title': 'Your Rights',
    'privacy.rights.content': 'You have the right to access, update, or delete your personal information. You can also opt out of certain data collection practices through your device settings.',
    'privacy.cookies.title': 'Cookies and Tracking',
    'privacy.cookies.content': 'We use cookies and similar technologies to improve your experience and analyze usage patterns. You can control cookie settings through your browser.',
    'privacy.changes.title': 'Changes to This Policy',
    'privacy.changes.content': 'We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy on our website and updating the effective date.',
    'privacy.contact.title': 'Contact Us',
    'privacy.contact.content': 'If you have any questions about this Privacy Policy, please contact our privacy <NAME_EMAIL>.',

    // Status Page  
    'status.title': 'System Status',
    'status.subtitle': 'Check the current status of Mokhba services and network connections.',
    'status.currentStatus': 'Current System Status',
    'status.allOperational': 'All Systems Operational',
    'status.services.api': 'API Services',
    'status.services.wallet': 'Wallet Services', 
    'status.services.support': 'Customer Support',
    'status.services.website': 'Website',
    'status.networkConnections': 'Network Connections',
    'status.networks.ethereum': 'Ethereum Network',
    'status.networks.bsc': 'Binance Smart Chain',
    'status.networks.polygon': 'Polygon Network',
    'status.performance.responseTime': 'Average Response Time',
    'status.performance.uptime': 'Uptime (30 days)',
    'status.recentIncidents': 'Recent Incidents',
    'status.noIncidents': 'No recent incidents reported.',
    'status.operational': 'Operational',
    'status.degraded': 'Degraded Performance',
    'status.maintenance': 'Under Maintenance',
    'status.outage': 'Service Outage',
    'status.excellent': 'Excellent',
    'status.good': 'Good',
    'status.fair': 'Fair',
    'status.poor': 'Poor',
    'status.subscribe': 'Subscribe to Updates',
    'status.subscribeDescription': 'Get notified about system status changes and maintenance schedules.',
    'status.emailPlaceholder': '<EMAIL>',
    'status.walletPlaceholder': 'Wallet address (optional)',
    'status.subscribeButton': 'Subscribe',
    'status.subscribing': 'Subscribing...',
    'status.subscribeSuccess': 'Successfully subscribed to status updates! You\'ll receive notifications about service status changes.',
    'status.subscribeError': 'Failed to subscribe to status updates',
    'status.alreadySubscribed': 'You\'re already subscribed to status updates!',
    'status.networkError': 'Network error. Please try again.',

    // Docs Page
    'docs.title': 'Documentation',
    'docs.subtitle': 'Comprehensive guides and resources to help you get the most out of Mokhba wallet.',
    'docs.searchPlaceholder': 'Search documentation...',
    'docs.gettingStarted': 'Getting Started',
    'docs.quickStart': 'Quick Start Guide',
    'docs.installation': 'Installation',
    'docs.firstWallet': 'Creating Your First Wallet',
    'docs.backup': 'Backup & Recovery',
    'docs.walletManagement': 'Wallet Management',
    'docs.importWallet': 'Import Existing Wallet',
    'docs.multipleWallets': 'Managing Multiple Wallets',
    'docs.walletSecurity': 'Wallet Security',
    'docs.transactions': 'Transactions',
    'docs.sendReceive': 'Send & Receive',
    'docs.swapTokens': 'Swap Tokens',
    'docs.transactionHistory': 'Transaction History',
    'docs.networkFees': 'Understanding Network Fees',
    'docs.security': 'Security',
    'docs.bestPractices': 'Security Best Practices',
    'docs.recoveryPhrase': 'Recovery Phrase Security',
    'docs.twoFactor': 'Two-Factor Authentication',
    'docs.phishingProtection': 'Phishing Protection',
    'docs.advanced': 'Advanced Features',
    'docs.customNetworks': 'Custom Networks',
    'docs.dappBrowser': 'DApp Browser',
    'docs.hardwareWallets': 'Hardware Wallet Integration',
    'docs.troubleshooting': 'Troubleshooting',
    'docs.commonIssues': 'Common Issues',
    'docs.syncProblems': 'Sync Problems',
    'docs.transactionFailed': 'Failed Transactions',
    'docs.contactSupport': 'Contact Support',
    'docs.apiReference': 'API Reference',
    'docs.authentication': 'Authentication',
    'docs.endpoints': 'API Endpoints',
    'docs.rateLimit': 'Rate Limiting',
    'docs.sdks': 'SDKs & Libraries',

    'footer.rights': '',

    // iPhone Mockup Features
    'mockup.secureStorage.title': 'Secure Storage',
    'mockup.secureStorage.description': 'Your crypto assets are protected with military-grade encryption',
    'mockup.multiLanguage.title': 'Multi-Language',
    'mockup.multiLanguage.description': 'Full Arabic and English support for MENA region',
    'mockup.easyTrading.title': 'Easy Trading',
    'mockup.easyTrading.description': 'Buy, sell, and swap cryptocurrencies with ease',
    'mockup.multiChain.title': 'Multi-Chain',
    'mockup.multiChain.description': 'Support for multiple blockchain networks',
    'mockup.livePrices': 'Live Prices',
    'mockup.realTimeUpdates': 'Real-time Updates',

    // Send Page
    'send.connectWallet': 'Connect Wallet',
    'send.connectWalletDescription': 'Please connect your wallet to send crypto',
    'send.availableBalance': 'Available Balance',
    'send.to': 'Send to',
    'send.amount': 'Amount',
    'send.max': 'MAX',
    'send.send': 'Send',
    'send.sending': 'Sending...',
    'send.confirming': 'Confirming...',
    'send.recipientAddress': 'Recipient Address',
    'send.sendTransaction': 'Send Transaction',
    'send.error.invalidAddress': 'Invalid Ethereum address',
    'send.error.insufficientBalance': 'Insufficient balance to cover amount and gas fees',
    'send.error.invalidAmount': 'Invalid amount',
    'send.error.transaction': 'Transaction failed. Please try again.',
    'send.success.transaction': 'Transaction sent successfully!',
    'send.addressPlaceholder': 'Enter recipient address (0x...)',

    // Receive Page
    'receive.connectWallet': 'Connect Wallet',
    'receive.connectWalletDescription': 'Connect your wallet to receive cryptocurrency',
    'receive.selectWallet': 'Select Wallet',
    'receive.selectWalletPlaceholder': 'Choose wallet to receive to',
    'receive.selectWalletTitle': 'Select a Wallet',
    'receive.selectWalletDescription': 'Choose which wallet you want to receive cryptocurrency to',
    'receive.network': 'Network',
    'receive.walletAddress': 'Wallet Address',
    'receive.copyAddress': 'Copy Address',
    'receive.copied': 'Copied!',
    'receive.copyFailed': 'Copy failed',
    'receive.linkCopied': 'Link copied!',
    'receive.advancedOptions': 'Advanced Options',
    'receive.requestAmount': 'Request Amount (Optional)',
    'receive.requestMessage': 'Message (Optional)',
    'receive.requestMessagePlaceholder': 'Payment for...',
    'receive.qrDescription': 'Scan this QR code to send crypto to this wallet',
    'receive.qrPaymentDescription': 'Scan this QR code to send the requested payment',
    'receive.shareQR': 'Share QR Code',
    'receive.shareAddress': 'Share Address',
    'receive.saveQR': 'Save QR Code',
    'receive.shareOptions': 'Share Options',
    'receive.shareNative': 'Share via Apps',
    'receive.copyLink': 'Copy Link',
    'receive.downloadQR': 'Download QR Code',
    'receive.shareTitle': 'Mokhba Wallet Address',
    'receive.shareText': 'Send crypto to my wallet',
    'receive.warning': 'Only send crypto on the same network to this address',
    'receive.warningMessage': 'Only send Ethereum (ETH) and ERC-20 tokens to this address. Sending other tokens may result in permanent loss.',

    // Transfer Page
    'transfer.connectWallet': 'Connect Wallet',
    'transfer.connectWalletDescription': 'Connect your wallet to transfer between wallets',
    'transfer.from': 'Transfer from',
    'transfer.to': 'Transfer to',
    'transfer.currentWallet': 'Current Wallet',
    'transfer.selectDestination': 'Select Destination Wallet',
    'transfer.selectFromWallet': 'Select source wallet',
    'transfer.selectToWallet': 'Select destination wallet',
    'transfer.selectWallet': 'Select Wallet',
    'transfer.selectAsset': 'Select Asset',
    'transfer.searchAssets': 'Search assets...',
    'transfer.noAssetsFound': 'No assets found',
    'transfer.noAssets': 'No assets available',
    'transfer.addAsset': 'Add Asset',
    'transfer.addWallet': 'Add new wallet',
    'transfer.addNewWallet': 'Add New Wallet',
    'transfer.noWallets': 'No wallets available',
    'transfer.default': 'Default',
    'transfer.asset': 'Asset',
    'transfer.amount': 'Amount',
    'transfer.balance': 'Balance',
    'transfer.max': 'MAX',
    'transfer.networkFee': 'Network Fee',
    'transfer.loadingFees': 'Loading fees...',
    'transfer.fee.slow': 'Slow',
    'transfer.fee.standard': 'Standard',
    'transfer.fee.fast': 'Fast',
    'transfer.confirm': 'Transfer',
    'transfer.processing': 'Processing...',
    'transfer.transferButton': 'Transfer',
    'transfer.error.invalidAmount': 'Invalid amount',
    'transfer.error.insufficientBalance': 'Insufficient balance',
    'transfer.error.invalidAddress': 'Invalid wallet address',
    'transfer.error.transaction': 'Transaction failed',
    'transfer.success.transaction': 'Transfer completed successfully!',

    // Add Wallet Modal
    'addWallet.title': 'Add New Wallet',
    'addWallet.name': 'Wallet Name',
    'addWallet.namePlaceholder': 'Enter wallet name',
    'addWallet.autoFill': 'Auto-fill',
    'addWallet.network': 'Blockchain Network',
    'addWallet.address': 'Wallet Address',
    'addWallet.addressPlaceholderEVM': 'Enter wallet address (0x...)',
    'addWallet.addressPlaceholderSolana': 'Enter Solana wallet address',
    'addWallet.addressHintEVM': 'Ethereum-compatible address starting with 0x',
    'addWallet.addressHintSolana': 'Base58 encoded Solana address',
    'addWallet.provider': 'Wallet Provider',
    'addWallet.setAsDefault': 'Set as default wallet for this network',
    'addWallet.cancel': 'Cancel',
    'addWallet.add': 'Add Wallet',
    'addWallet.adding': 'Adding...',
    'addWallet.error.nameRequired': 'Wallet name is required',
    'addWallet.error.nameTooShort': 'Wallet name must be at least 2 characters',
    'addWallet.error.addressRequired': 'Wallet address is required',
    'addWallet.error.invalidAddress': 'Invalid wallet address format',
    'addWallet.error.walletExists': 'This wallet address already exists',
    'addWallet.error.failed': 'Failed to add wallet. Please try again.',

    // Buy Page
    'buy.comingSoonDesc': 'This feature will be available soon',

    // Sell Page
    'sell.comingSoonDesc': 'This feature will be available soon',

    // Swap Page
    'swap.comingSoonDesc': 'This feature will be available soon',

    // Stake Page
    'stake.comingSoonDesc': 'This feature will be available soon',

    // Wallet Connection
    'wallet.selectWallet': 'Select Wallet',
    'wallet.selectWalletDescription': 'Choose your preferred wallet to connect to Mokhba',
    'wallet.ethereum': 'Ethereum Network',
    'wallet.solana': 'Solana Network',
    'wallet.secureConnection': 'Your connection is secure and encrypted',

    // App Navigation
    'app.dashboard': 'Dashboard',
    'app.move-crypto': 'Move Crypto',
    'app.discover': 'Discover',
    'app.card': 'Card',
    'app.buy': 'Buy',
    'app.sell': 'Sell',
    'app.swap': 'Swap',
    'app.send': 'Send',
    'app.transfer': 'Transfer',
    'app.receive': 'Receive',
    'app.stake': 'Stake',
    'app.connectWallet': 'Connect Wallet',
    'app.badge.pilot': 'PILOT',

    // Buy/Sell Common
    'trade.selectWallet': 'Select Wallet',
    'trade.connectWallet': 'Connect Wallet',
    'trade.recommendedQuote': 'Recommended Quote',
    'trade.seeMoreQuotes': 'See more quotes',
    'trade.youllReceiveApproximately': 'You\'ll receive approximately',
    'trade.mostReliable': 'MOST RELIABLE',
    'trade.debitOrCredit': 'Debit or Credit',
    'trade.bankTransfer': 'Bank Transfer',
    'trade.saudiRiyal': 'Saudi Riyal',
    'trade.ethereum': 'Ethereum',
    'trade.ethereumMainnet': 'Ethereum Mainnet',
  },
  ar: {
    'nav.security': 'الأمان',
    'nav.learn': 'تعلم',
    'nav.explore': 'استكشف',
    'nav.support': 'الدعم',
    'nav.launch': 'تشغيل مخبة',
    'hero.title': 'محفظة واحدة لجميع أصولك الرقمية',
    'hero.subtitle': 'نحن أول محفظة عربية تمكن المستخدمين من تخزين وإدارة أصولهم الرقمية بأمان وسهولة.',
    'hero.cta': 'كن جزءاً منها',
    'arabic.title': 'أول محفظة عربية للعملات الرقمية',
    'arabic.description': 'تم تصميم مخبة مع وضع المستخدمين العرب في الاعتبار، مما يوفر تجربة سلسة مع دعم كامل للغة العربية وواجهة مستخدم باللغة العربية.',
    'arabic.learnMore': 'اعرف المزيد',
    'arabic.support': 'دعم اللغة العربية',
    'storage.title': 'تخزين ذاتي سهل',
    'feature.secure.title': 'آمن من الأساس',
    'feature.secure.description': 'مفاتيحك الخاصة تبقى على جهازك دائمًا. نستخدم تقنيات تشفير بمعايير عالمية لحماية أصولك الرقمية.',
    'feature.recovery.title': 'استعادة سهلة',
    'feature.recovery.description': 'انسخ محفظتك احتياطيًا باستخدام عبارة استرداد بسيطة. يمكنك الوصول إلى أصولك من أي جهاز وفي أي وقت.',
    'feature.multichain.title': 'دعم متعدد الشبكات',
    'feature.multichain.description': 'إدارة أصولك عبر عدة شبكات بلوكتشين من مكان واحد مثل إيثريوم، باينانس سمارت تشين والمزيد.',
    'footer.description': 'أول محفظة عربية للأصول الرقمية.',
    'footer.company': 'الشركة',
    'footer.about': 'عن الشركة',
    'footer.terms': 'الشروط',
    'footer.privacy': 'الخصوصية',
    'footer.status': 'الحالة',
    'footer.products': 'المنتجات',
    'footer.download': 'تحميل',
    'footer.security': 'الأمان',
    'footer.support': 'الدعم',
    'footer.feature': 'هل لديك فكرة ميزة أو تريد التعاون',
    'footer.resources': 'الموارد',
    'footer.explore': 'استكشف',
    'footer.learn': 'تعلم',
    'footer.blog': 'المدونة',
    'footer.docs': 'الوثائق',

    'app.getStarted': 'ابدأ مع مخبة',
    'app.description': 'اعرض وأدر أصولك الرقمية بأمان في مكان واحد.',
    'app.watchAddress': 'مراقبة عنوان',
    'app.wallet': 'محفظة مخبة',
    'app.upgrade': 'ترقية',
    'app.upgradeDescription': 'افتح الميزات المتقدمة',
    'app.upgradeCta': 'ترقية الآن',
    'app.connectWallet': 'ربط المحفظة',
    'app.dashboard': 'لوحة التحكم',
    'app.move-crypto': 'نقل العملات',
    'app.discover': 'استكشاف',
    'app.card': 'البطاقة',
    'app.badge.pilot': 'تجريبي',
    'app.buy': 'شراء',
    'app.sell': 'بيع',
    'app.swap': 'تبديل',
    'app.send': 'إرسال',
    'app.transfer': 'تحويل',
    'app.receive': 'استقبال',
    'app.stake': 'استثمار',

    // Learn Page
    'learn.title': 'تعلم عن العملات الرقمية',
    'learn.subtitle': 'وسع معرفتك حول العملة الرقمية وتقنية البلوك تشين والتمويل الرقمي.',
    'learn.resources.cryptoBasics': 'أساسيات العملات الرقمية',
    'learn.resources.cryptoBasicsDesc': 'تعلم أساسيات العملة الرقمية وتقنية البلوك تشين والمحافظ الرقمية.',
    'learn.resources.advancedTrading': 'التداول المتقدم',
    'learn.resources.advancedTradingDesc': 'اكتشف استراتيجيات تداول العملات الرقمية وإدارة محفظتك.',
    'learn.resources.securityPractices': 'أفضل الممارسات الأمنية',
    'learn.resources.securityPracticesDesc': 'احم أصولك بنصائح الأمان الأساسية وأفضل الممارسات.',
    'learn.resources.defiExplained': 'شرح التمويل اللامركزي',
    'learn.resources.defiExplainedDesc': 'افهم التمويل اللامركزي وكيفية المشاركة في بروتوكولات DeFi.',
    'learn.resources.nftOwnership': 'الرموز غير القابلة للاستبدال والملكية الرقمية',
    'learn.resources.nftOwnershipDesc': 'استكشف عالم الرموز غير القابلة للاستبدال والمقتنيات الرقمية.',
    'learn.resources.web3Development': 'تطوير ويب 3',
    'learn.resources.web3DevelopmentDesc': 'ابدأ في بناء التطبيقات اللامركزية على البلوك تشين.',
    'learn.tags.beginner': 'مبتدئ',
    'learn.tags.advanced': 'متقدم',
    'learn.tags.essential': 'أساسي',
    'learn.tags.intermediate': 'متوسط',
    'learn.tags.trending': 'رائج',
    'learn.tags.technical': 'تقني',
    'learn.learnMore': 'اعرف المزيد',
    'learn.readyToDive': 'مستعد للغوص أعمق؟',
    'learn.docsDescription': 'اطلع على وثائقنا الشاملة والدروس لتصبح خبيراً في العملات الرقمية.',
    'learn.exploreDocs': 'استكشف الوثائق',

    // Explore Page
    'explore.title': 'استكشف النظام البيئي للعملات الرقمية',
    'explore.subtitle': 'اكتشف مشاريع وعملات وفرص جديدة في العالم اللامركزي.',
    'explore.comingSoon': 'قريباً',
    'explore.description': 'نحن نبني تجربة استكشاف مذهلة للنظام البيئي للعملات الرقمية. ترقبوا!',
    'explore.backToDashboard': 'العودة إلى لوحة التحكم',

    // Support Page
    'support.title': 'مركز الدعم',
    'support.subtitle': 'احصل على المساعدة مع محفظة مخبة وابحث عن إجابات للأسئلة الشائعة.',
    'support.categories.gettingStarted': 'البدء',
    'support.categories.account': 'الحساب',
    'support.categories.transactions': 'المعاملات',
    'support.categories.security': 'الأمان',
    'support.categories.troubleshooting': 'استكشاف الأخطاء',
    'support.categories.contactUs': 'اتصل بنا',
    'support.faq.title': 'الأسئلة الشائعة',
    'support.faq.createWallet.question': 'كيف أنشئ محفظة جديدة؟',
    'support.faq.createWallet.answer': 'لإنشاء محفظة جديدة، انقر على "إنشاء محفظة" في الصفحة الرئيسية واتبع التعليمات. ستحتاج إلى إنشاء كلمة مرور قوية وحفظ عبارة الاسترداد الخاصة بك بأمان.',
    'support.faq.safety.question': 'كيف أحافظ على أمان محفظتي؟',
    'support.faq.safety.answer': 'احتفظ بعبارة الاسترداد الخاصة بك في مكان آمن، لا تشاركها مع أحد، استخدم كلمة مرور قوية، وفعل المصادقة الثنائية إذا كانت متاحة.',
    'support.faq.supported.question': 'ما هي العملات الرقمية المدعومة؟',
    'support.faq.supported.answer': 'ندعم مجموعة واسعة من العملات الرقمية بما في ذلك Bitcoin و Ethereum و Polygon و Arbitrum و Optimism والعديد من الرموز المميزة الأخرى.',
    'support.faq.recover.question': 'كيف يمكنني استرداد محفظتي؟',
    'support.faq.recover.answer': 'يمكنك استرداد محفظتك باستخدام عبارة الاسترداد المكونة من 12 كلمة. انقر على "استيراد محفظة" وأدخل عبارة الاسترداد الخاصة بك.',
    'support.needHelp.title': 'هل تحتاج مساعدة إضافية؟',
    'support.needHelp.description': 'إذا لم تجد ما تبحث عنه، فريق الدعم لدينا هنا للمساعدة.',
    'support.contactSupport': 'اتصل بالدعم',

    // Privacy Page Extensions (hardcoded text that needs translation)
    'privacy.yourChoices.title': 'خياراتك للخصوصية',
    'privacy.yourChoices.description': 'في مخبة، نؤمن بمنحك السيطرة على بياناتك. يمكنك:',
    'privacy.yourChoices.access': 'الوصول إلى وتحميل بياناتك الشخصية',
    'privacy.yourChoices.correct': 'طلب تصحيح المعلومات غير الدقيقة',
    'privacy.yourChoices.delete': 'طلب حذف بياناتك (مع مراعاة المتطلبات القانونية)',
    'privacy.yourChoices.optOut': 'إلغاء الاشتراك في الاتصالات التسويقية',
    'privacy.yourChoices.cookies': 'تعديل تفضيلات ملفات تعريف الارتباط',

    // Status Page (hardcoded text that needs translation)
    'status.systemStatus': 'حالة النظام',
    'status.networkStatus': 'حالة الشبكة',
    'status.recentIncidents': 'الحوادث الأخيرة',
    'status.overall.partial': 'انقطاع جزئي في النظام',
    'status.overall.description': 'معظم الأنظمة تعمل بشكل طبيعي، لكننا نواجه حالياً مشاكل مع تكامل البورصة. فريقنا يعمل على حل هذه المشكلة في أسرع وقت ممكن.',

    // Docs Page (hardcoded text that needs translation)
    'docs.searchPlaceholder': 'البحث في الوثائق...',
    'docs.categories.gettingStarted': 'البدء',
    'docs.categories.usingMokhba': 'استخدام مخبة',
    'docs.categories.advancedFeatures': 'الميزات المتقدمة',
    'docs.categories.troubleshooting': 'استكشاف الأخطاء',
    'docs.popularDocumentation': 'الوثائق الشائعة',
    'docs.developerResources': 'موارد المطورين',
    'docs.viewAllIn': 'عرض الكل في',

    // Common translations
    'common.comingSoon': 'قريباً',

    // Mockup iPhone cards (Hero section)
    'mockup.secureStorage.title': 'تخزين آمن',
    'mockup.secureStorage.description': 'أصولك الرقمية محمية بتشفير عسكري الدرجة',
    'mockup.multiLanguage.title': 'متعدد اللغات',
    'mockup.multiLanguage.description': 'دعم كامل للعربية والإنجليزية لمنطقة الشرق الأوسط وشمال أفريقيا',
    'mockup.easyTrading.title': 'تداول سهل',
    'mockup.easyTrading.description': 'شراء وبيع وتبديل العملات الرقمية بسهولة',
    'mockup.multiChain.title': 'متعدد السلاسل',
    'mockup.multiChain.description': 'دعم لشبكات بلوك تشين متعددة',
    'mockup.livePrices': 'الأسعار المباشرة',
    'mockup.realTimeUpdates': 'التحديثات في الوقت الفعلي',

    // Buy/Sell/Swap specific
    'buy.comingSoonDesc': 'ستتوفر هذه الميزة قريباً',
    'sell.comingSoonDesc': 'ستتوفر هذه الميزة قريباً',
    'swap.comingSoonDesc': 'ستتوفر هذه الميزة قريباً',

    // Send page
    'send.connectWallet': 'ربط المحفظة',
    'send.connectWalletDescription': 'يرجى ربط محفظتك لإرسال العملة الرقمية',
    'send.availableBalance': 'الرصيد المتاح',
    'send.to': 'إلى',
    'send.recipientAddress': 'عنوان المستلم',
    'send.amount': 'المبلغ',
    'send.sendTransaction': 'إرسال المعاملة',
    'send.addressPlaceholder': 'عنوان المستلم',
    'send.error.invalidAddress': 'عنوان غير صالح',
    'send.error.insufficientBalance': 'رصيد غير كافي لتغطية المبلغ ورسوم الشبكة',
    'send.error.invalidAmount': 'مبلغ غير صالح',
    'send.error.transaction': 'فشل في إرسال المعاملة',
    'send.success.transaction': 'تم إرسال المعاملة بنجاح',

    // Transfer page
    'transfer.connectWallet': 'ربط المحفظة',
    'transfer.connectWalletDescription': 'يرجى ربط محفظتك لتحويل العملة الرقمية',
    'transfer.from': 'من',
    'transfer.to': 'إلى',
    'transfer.currentWallet': 'المحفظة الحالية',
    'transfer.selectDestination': 'اختر الوجهة',
    'transfer.selectFromWallet': 'اختر المحفظة المصدر',
    'transfer.selectToWallet': 'اختر المحفظة الوجهة',
    'transfer.selectWallet': 'اختر المحفظة',
    'transfer.selectAsset': 'اختر الأصل',
    'transfer.searchAssets': 'البحث في الأصول...',
    'transfer.noAssetsFound': 'لم يتم العثور على أصول',
    'transfer.noAssets': 'لا توجد أصول متاحة',
    'transfer.addAsset': 'إضافة أصل',
    'transfer.addWallet': 'إضافة محفظة',
    'transfer.addNewWallet': 'إضافة محفظة جديدة',
    'transfer.noWallets': 'لا توجد محافظ متاحة',
    'transfer.default': 'افتراضي',
    'transfer.asset': 'الأصل',
    'transfer.amount': 'المبلغ',
    'transfer.balance': 'الرصيد',
    'transfer.max': 'الحد الأقصى',
    'transfer.networkFee': 'رسوم الشبكة',
    'transfer.loadingFees': 'جاري تحميل الرسوم...',
    'transfer.fee.slow': 'بطيء',
    'transfer.fee.standard': 'عادي',
    'transfer.fee.fast': 'سريع',
    'transfer.confirm': 'تحويل',
    'transfer.processing': 'جاري المعالجة...',
    'transfer.transferButton': 'تحويل',
    'transfer.error.invalidAmount': 'مبلغ غير صالح',
    'transfer.error.insufficientBalance': 'رصيد غير كافي',
    'transfer.error.invalidAddress': 'عنوان محفظة غير صالح',
    'transfer.error.transaction': 'فشل في المعاملة',
    'transfer.success.transaction': 'تم التحويل بنجاح!',

    // Add Wallet Modal
    'addWallet.title': 'إضافة محفظة جديدة',
    'addWallet.name': 'اسم المحفظة',
    'addWallet.namePlaceholder': 'أدخل اسم المحفظة',
    'addWallet.autoFill': 'ملء تلقائي',
    'addWallet.network': 'شبكة البلوك تشين',
    'addWallet.address': 'عنوان المحفظة',
    'addWallet.addressPlaceholderEVM': 'أدخل عنوان المحفظة (0x...)',
    'addWallet.addressPlaceholderSolana': 'أدخل عنوان محفظة سولانا',
    'addWallet.addressHintEVM': 'عنوان متوافق مع إيثيريوم يبدأ بـ 0x',
    'addWallet.addressHintSolana': 'عنوان سولانا مُرمز بـ Base58',
    'addWallet.provider': 'مزود المحفظة',
    'addWallet.setAsDefault': 'تعيين كمحفظة افتراضية لهذه الشبكة',
    'addWallet.cancel': 'إلغاء',
    'addWallet.add': 'إضافة محفظة',
    'addWallet.adding': 'جاري الإضافة...',
    'addWallet.error.nameRequired': 'اسم المحفظة مطلوب',
    'addWallet.error.nameTooShort': 'يجب أن يكون اسم المحفظة حرفين على الأقل',
    'addWallet.error.addressRequired': 'عنوان المحفظة مطلوب',
    'addWallet.error.invalidAddress': 'تنسيق عنوان المحفظة غير صالح',
    'addWallet.error.walletExists': 'عنوان المحفظة هذا موجود بالفعل',
    'addWallet.error.failed': 'فشل في إضافة المحفظة. يرجى المحاولة مرة أخرى.',

    // Receive page
    'receive.connectWallet': 'ربط المحفظة',
    'receive.connectWalletDescription': 'يرجى ربط محفظتك لاستقبال العملة الرقمية',
    'receive.selectWallet': 'اختر المحفظة',
    'receive.selectWalletPlaceholder': 'اختر المحفظة للاستقبال',
    'receive.selectWalletTitle': 'اختر محفظة',
    'receive.selectWalletDescription': 'اختر المحفظة التي تريد استقبال العملة الرقمية عليها',
    'receive.network': 'الشبكة',
    'receive.walletAddress': 'عنوان المحفظة',
    'receive.copyAddress': 'نسخ العنوان',
    'receive.copied': 'تم النسخ!',
    'receive.copyFailed': 'فشل النسخ',
    'receive.linkCopied': 'تم نسخ الرابط!',
    'receive.advancedOptions': 'خيارات متقدمة',
    'receive.requestAmount': 'طلب مبلغ (اختياري)',
    'receive.requestMessage': 'رسالة (اختياري)',
    'receive.requestMessagePlaceholder': 'دفعة مقابل...',
    'receive.qrDescription': 'امسح رمز الاستجابة السريعة لإرسال العملة الرقمية لهذه المحفظة',
    'receive.qrPaymentDescription': 'امسح رمز الاستجابة السريعة لإرسال الدفعة المطلوبة',
    'receive.shareQR': 'مشاركة رمز الاستجابة السريعة',
    'receive.shareAddress': 'مشاركة العنوان',
    'receive.saveQR': 'حفظ رمز الاستجابة السريعة',
    'receive.shareOptions': 'خيارات المشاركة',
    'receive.shareNative': 'مشاركة عبر التطبيقات',
    'receive.copyLink': 'نسخ الرابط',
    'receive.downloadQR': 'تحميل رمز الاستجابة السريعة',
    'receive.shareTitle': 'عنوان محفظة مخبأ',
    'receive.shareText': 'أرسل العملة الرقمية لمحفظتي',
    'receive.warning': 'أرسل العملة الرقمية على نفس الشبكة فقط لهذا العنوان',

    // Stake page
    'stake.comingSoonDesc': 'ستتوفر هذه الميزة قريباً',

    // Discover Page
    'discover.title': 'اكتشف أسواق العملات الرقمية',
    'discover.subtitle': 'الأسعار المباشرة والاتجاهات ورؤى السوق في متناول يدك',
    'discover.livePrices': 'الأسعار المباشرة',
    'discover.topMovers': 'الأكثر تحركاً',
    'discover.trending': 'المتداولة',
    'discover.globalStats': 'إحصائيات السوق العالمية',
    'discover.categories': 'الفئات',
    'discover.marketCap': 'القيمة السوقية',
    'discover.volume24h': 'الحجم (24 ساعة)',
    'discover.change24h': 'التغيير (24 ساعة)',
    'discover.price': 'السعر',
    'discover.totalMarketCap': 'إجمالي القيمة السوقية',
    'discover.totalVolume': 'إجمالي الحجم',
    'discover.btcDominance': 'هيمنة البيتكوين',
    'discover.activeCryptos': 'العملات الرقمية النشطة',
    'discover.markets': 'الأسواق',
    'discover.exchanges': 'البورصات',
    'discover.gainers': 'الأكثر ارتفاعاً',
    'discover.losers': 'الأكثر انخفاضاً',
    'discover.loadingMarketData': 'جارٍ تحميل بيانات السوق...',
    'discover.error': 'خطأ في تحميل البيانات',
    'discover.retry': 'إعادة المحاولة',
    'discover.viewAll': 'عرض الكل',
    'discover.searchPlaceholder': 'البحث عن العملات الرقمية...',
    'discover.noResults': 'لم يتم العثور على نتائج',
    'discover.rank': 'الترتيب',
    'discover.coin': 'العملة',
    'discover.lastUpdated': 'آخر تحديث',
    'discover.timeAgo': 'مضى',
    'discover.defi': 'التمويل اللامركزي',
    'discover.nft': 'الرموز غير القابلة للاستبدال',
    'discover.gaming': 'الألعاب',
    'discover.metaverse': 'الميتافيرس',
    'discover.layer1': 'الطبقة الأولى',
    'discover.layer2': 'الطبقة الثانية',
    'discover.memecoins': 'عملات الميم',
    'discover.stablecoins': 'العملات المستقرة',

    // Feature Request Page


    // Security Page
    'security.title': 'الأمان في مخبة',
    'security.subtitle': 'تعلم كيف نحمي أصولك بميزات أمان رائدة في الصناعة.',
    'security.features.nonCustodial.title': 'الأمان غير الاحتجازي',
    'security.features.nonCustodial.description': 'مفاتيحك الخاصة تُحفظ محلياً على جهازك. لا يمكننا الوصول إلى أموالك أبداً.',
    'security.features.encryption.title': 'التشفير من طرف إلى طرف',
    'security.features.encryption.description': 'جميع عمليات نقل البيانات مشفرة باستخدام بروتوكولات معيارية في الصناعة.',
    'security.features.recovery.title': 'حماية عبارة الاسترداد',
    'security.features.recovery.description': 'نظام نسخ احتياطي واسترداد آمن لحماية أصولك.',
    'security.features.mfa.title': 'المصادقة متعددة العوامل',
    'security.features.mfa.description': 'طبقات أمان إضافية لحماية الوصول إلى حسابك.',
    
    // Security feature details in Arabic
    'security.features.nonCustodial.details.1': 'المفاتيح الخاصة لا تغادر جهازك أبداً',
    'security.features.nonCustodial.details.2': 'تحتفظ بالتحكم الكامل في أصولك',
    'security.features.nonCustodial.details.3': 'هندسة معرفة صفرية',
    'security.features.nonCustodial.details.4': 'إدارة مفاتيح لامركزية',
    
    'security.features.encryption.details.1': 'معيار تشفير AES-256',
    'security.features.encryption.details.2': 'TLS 1.3 لنقل البيانات',
    'security.features.encryption.details.3': 'تخزين محلي مشفر',
    'security.features.encryption.details.4': 'وحدات الأمان الصلبة (HSM)',
    
    'security.features.recovery.details.1': 'عبارات البذور متوافقة مع BIP-39',
    'security.features.recovery.details.2': 'خيارات نسخ احتياطي آمنة',
    'security.features.recovery.details.3': 'طرق استرداد اجتماعية',
    'security.features.recovery.details.4': 'تكامل المحافظ الصلبة',
    
    'security.features.mfa.details.1': 'مصادقة بيومترية',
    'security.features.mfa.details.2': 'دعم الرموز الصلبة',
    'security.features.mfa.details.3': 'التحقق عبر الرسائل والبريد الإلكتروني',
    'security.features.mfa.details.4': 'تطبيقات المصادقة',
    
    // Security protocols in Arabic
    'security.protocols.auditing.title': 'تدقيق العقود الذكية',
    'security.protocols.auditing.description': 'تدقيقات أمنية منتظمة من طرف ثالث لجميع العقود الذكية',
    'security.protocols.bugBounty.title': 'برنامج مكافآت الأخطاء',
    'security.protocols.bugBounty.description': 'تحفيز الباحثين الأمنيين للعثور على الثغرات',
    'security.protocols.penetration.title': 'اختبار الاختراق',
    'security.protocols.penetration.description': 'تقييمات أمنية منتظمة من قبل متسللين أخلاقيين معتمدين',
    'security.protocols.compliance.title': 'مراقبة الامتثال',
    'security.protocols.compliance.description': 'الالتزام بالمعايير والأنظمة الأمنية الدولية',
    
    // Security metrics in Arabic
    'security.metrics.securityScore': 'نقاط الأمان',
    'security.metrics.uptime': 'وقت التشغيل',
    'security.metrics.incidents': 'الحوادث',
    'security.metrics.responseTime': 'وقت الاستجابة',
    
    // Tab labels in Arabic
    'security.tabs.features': 'ميزات الأمان',
    'security.tabs.protocols': 'بروتوكولات الأمان',
    'security.tabs.metrics': 'مقاييس الأمان',
    'security.tabs.features.description': 'آليات الأمان الأساسية',
    'security.tabs.protocols.description': 'عمليات وتدقيقات الأمان',
    'security.tabs.metrics.description': 'بيانات المراقبة في الوقت الفعلي',
    
    // Section headers in Arabic
    'security.overview': 'نظرة عامة على الأمان',
    'security.advancedFeatures': 'ميزات الأمان المتقدمة',
    'security.advancedFeaturesDescription': 'هندسة الأمان متعددة الطبقات تضمن حماية أصولك الرقمية في جميع الأوقات',
    'security.protocolsProcesses': 'بروتوكولات وعمليات الأمان',
    'security.protocolsDescription': 'تدابير أمنية شاملة ومراقبة مستمرة للحفاظ على أعلى المعايير',
    'security.metricsMonitoring': 'مقاييس ومراقبة الأمان',
    'security.metricsDescription': 'حالة الأمان في الوقت الفعلي وشهادات الامتثال الشاملة',
    
    // Status labels in Arabic
    'security.status.active': 'نشط',
    'security.status.quarterly': 'ربع سنوي',
    'security.status.continuous': 'مستمر',
    'security.status.implementation': 'التنفيذ',
    'security.status.complete': 'مكتمل',
    
    // Dashboard metrics in Arabic
    'security.dashboard.title': 'لوحة تحكم الأمان المباشرة',
    'security.dashboard.systemIntegrity': 'سلامة النظام',
    'security.dashboard.activeMonitoring': 'المراقبة النشطة',
    'security.dashboard.encryptionLevel': 'مستوى التشفير',
    
    // Certifications in Arabic
    'security.certifications.title': 'شهادات ومعايير الأمان',
    
    // Security Response Team in Arabic
    'security.responseTeam.title': 'فريق الاستجابة الأمنية',
    'security.responseTeam.description': 'لديك مخاوف أمنية؟ فريق الأمان المتخصص لدينا متاح على مدار الساعة لمعالجة أي مشاكل.',
    'security.responseTeam.reportVulnerability': 'الإبلاغ عن ثغرة',

    'security.commitment.title': 'التزامنا بالأمان',
    'security.commitment.description1': 'في مخبة، الأمان هو أولويتنا القصوى. نستخدم طبقات حماية متعددة لضمان بقاء أصولك الرقمية آمنة مع الحفاظ على تجربة مستخدم سلسة.',
    'security.commitment.description2': 'فريق خبراء الأمان لدينا يراقب باستمرار التهديدات والثغرات المحتملة، وينفذ أحدث التدابير الأمنية للبقاء في المقدمة أمام المخاطر الناشئة في مجال العملات الرقمية.',

    // About Page
    'about.title': 'حول مخبة',
    'about.subtitle': 'أول محفظة عربية للعملات الرقمية مصممة لمنطقة الشرق الأوسط وشمال أفريقيا.',
    'about.ourStory': 'قصتنا',
    'about.storyParagraph1': 'تأسست مخبة عام 2023 برسالة واضحة: إنشاء محفظة عملات رقمية آمنة وسهلة الاستخدام تخدم الاحتياجات الفريدة للمستخدمين في الشرق الأوسط وشمال أفريقيا.',
    'about.storyParagraph2': 'لاحظنا فجوة في السوق حيث أن معظم محافظ العملات الرقمية مصممة للأسواق الغربية، مع عدم وجود دعم مناسب للغة العربية أو فهم للاحتياجات الثقافية والمالية لمنطقتنا.',
    'about.storyParagraph3': 'اليوم، نحن فخورون بكوننا أول محفظة عملات رقمية تضع المستخدمين العرب في المقدمة، مع دعم كامل للغة العربية وواجهة مصممة خصيصاً لثقافتنا.',
    'about.storyParagraph4': 'رؤيتنا هي أن نصبح البوابة الأساسية للعملات الرقمية في منطقة الشرق الأوسط وشمال أفريقيا، مما يمكن الملايين من الوصول إلى مستقبل التمويل اللامركزي.',
    'about.ourMission': 'مهمتنا',
    'about.missionDescription': 'تمكين المستخدمين العرب من المشاركة بثقة في اقتصاد العملات الرقمية من خلال توفير محفظة آمنة وسهلة الاستخدام مصممة خصيصاً لاحتياجاتهم.',
    'about.ourValues': 'قيمنا',
    'about.values.security.title': 'الأمان أولاً',
    'about.values.security.description': 'أصولك محمية بأحدث تقنيات التشفير والأمان.',
    'about.values.accessibility.title': 'سهولة الوصول',
    'about.values.accessibility.description': 'نجعل العملات الرقمية في متناول الجميع، بغض النظر عن مستوى الخبرة التقنية.',
    'about.values.culture.title': 'الوعي الثقافي',
    'about.values.culture.description': 'نصمم منتجاتنا مع فهم عميق للثقافة العربية والاحتياجات المحلية.',
    'about.values.innovation.title': 'الابتكار',
    'about.values.innovation.description': 'نسعى باستمرار لتحسين وابتكار حلول جديدة لمستخدمينا.',
    // Values cards for about page
    'about.values.security': 'الأمان',
    'about.values.empowerment': 'التمكين',
    'about.values.transparency': 'الشفافية',
    'about.values.innovation': 'الابتكار',
    'about.values.inclusivity': 'الشمولية',
    'about.values.community': 'المجتمع',
    'about.team.title': 'فريقنا',
    'about.team.description': 'تم بناء مخبة من قبل فريق متنوع من الخبراء الشغوفين بتكنولوجيا البلوك تشين والمتحمسين لخدمة المنطقة العربية.',
    'about.team.ahmed.name': 'أحمد محمد',
    'about.team.ahmed.role': 'المؤسس والرئيس التنفيذي',
    'about.team.ahmed.bio': 'خبير في البلوك تشين مع أكثر من 8 سنوات من الخبرة في التمويل الرقمي والعملات الرقمية.',
    'about.team.sarah.name': 'سارة أحمد',
    'about.team.sarah.role': 'رئيسة التكنولوجيا',
    'about.team.sarah.bio': 'مطورة برمجيات متخصصة في تطبيقات البلوك تشين والأنظمة اللامركزية.',
    'about.team.omar.name': 'عمر فاروق',
    'about.team.omar.role': 'رئيس الأمان',
    'about.team.omar.bio': 'خبير أمن سيبراني متخصص في التشفير وتصميم الأنظمة الآمنة.',
    'about.joinJourney': 'انضم إلى رحلتنا',
    'about.joinDescription': 'نحن في مهمة لجعل العملات الرقمية في متناول الجميع في منطقة الشرق الأوسط وشمال أفريقيا. سواء كنت مستخدماً أو شريكاً أو عضواً محتملاً في الفريق، نحب أن نتواصل معك.',
    'about.getInTouch': 'تواصل معنا',

    // Terms Page
    'terms.title': 'شروط الخدمة',
    'terms.subtitle': 'يرجى قراءة هذه الشروط بعناية قبل استخدام محفظة مخبة.',
    'terms.lastUpdated': 'آخر تحديث: 9 مايو 2024',
    'terms.acceptance.title': 'قبول الشروط',
    'terms.acceptance.content': 'من خلال الوصول إلى محفظة مخبة أو استخدامها، فإنك توافق على الالتزام بشروط الخدمة هذه. إذا كنت لا توافق على جميع الشروط والأحكام، يجب عدم استخدام خدماتنا.',
    'terms.services.title': 'وصف الخدمات',
    'terms.services.content': 'تقدم مخبة محفظة عملات رقمية غير احتجازية تسمح للمستخدمين بتخزين وإرسال واستقبال وإدارة الأصول الرقمية. ليس لدينا وصول إلى مفاتيحك الخاصة أو أموالك، ولا يمكننا استردادها في حالة فقدانها.',
    'terms.responsibilities.title': 'مسؤوليات المستخدم',
    'terms.responsibilities.content': 'أنت مسؤول عن الحفاظ على سرية عبارة الاسترداد والمفاتيح الخاصة. يجب عدم مشاركة هذه المعلومات مع أي شخص. أنت المسؤول الوحيد عن جميع الأنشطة التي تحدث تحت حسابك.',
    'terms.risks.title': 'المخاطر',
    'terms.risks.content': 'تنطوي استثمارات العملات الرقمية على مخاطر كبيرة. يمكن أن تتقلب الأسعار بشكل كبير، والأداء السابق لا يدل على النتائج المستقبلية. يجب أن تنظر بعناية في أهدافك الاستثمارية وقدرتك على تحمل المخاطر قبل استخدام خدماتنا.',
    'terms.prohibited.title': 'الأنشطة المحظورة',
    'terms.prohibited.content': 'توافق على عدم استخدام خدماتنا لأي أغراض غير قانونية، بما في ذلك على سبيل المثال لا الحصر غسيل الأموال أو تمويل الإرهاب أو الأنشطة الاحتيالية. نحتفظ بالحق في الإبلاغ عن الأنشطة المشبوهة للسلطات ذات الصلة.',
    'terms.intellectual.title': 'الملكية الفكرية',
    'terms.intellectual.content': 'جميع المحتويات والتصاميم والرسوم والتجميع والتحويل الرقمي وغيرها من الأمور المتعلقة بمحفظة مخبة محمية بحقوق الطبع والنشر والعلامات التجارية وقوانين الملكية الفكرية الأخرى.',
    'terms.liability.title': 'تحديد المسؤولية',
    'terms.liability.content': 'إلى أقصى حد يسمح به القانون، لن تكون مخبة وشركاتها التابعة مسؤولة عن أي أضرار غير مباشرة أو عرضية أو خاصة أو تبعية أو عقابية، بما في ذلك فقدان الأرباح أو البيانات أو السمعة التجارية.',
    'terms.modifications.title': 'تعديلات الشروط',
    'terms.modifications.content': 'نحتفظ بالحق في تعديل هذه الشروط في أي وقت. سنقدم إشعاراً بالتغييرات المهمة من خلال تحديث التاريخ في أعلى هذه الشروط والحفاظ على نسخة حالية على موقعنا الإلكتروني.',
    'terms.governing.title': 'القانون الحاكم',
    'terms.governing.content': 'تخضع هذه الشروط وتفسر وفقاً لقوانين [الاختصاص القضائي]، دون اعتبار لأحكام تضارب القوانين.',
    'terms.contact.title': 'معلومات الاتصال',
    'terms.contact.content': 'إذا كان لديك أي أسئلة حول هذه الشروط، يرجى الاتصال بنا على <EMAIL>.',
    'terms.agreement': 'باستخدام محفظة مخبة، فإنك تقر بأنك قرأت وفهمت ووافقت على الالتزام بشروط الخدمة هذه.',

    // Privacy Page
    'privacy.title': 'سياسة الخصوصية',
    'privacy.subtitle': 'تعرف على كيفية جمعنا واستخدامنا وحماية معلوماتك.',
    'privacy.lastUpdated': 'آخر تحديث: 9 مايو 2024',
    'privacy.overview.title': 'نظرة عامة',
    'privacy.overview.content': 'في مخبة، نحن ملتزمون بحماية خصوصيتك والشفافية حول كيفية تعاملنا مع بياناتك. توضح سياسة الخصوصية هذه المعلومات التي نجمعها وكيف نستخدمها والخيارات المتاحة لك.',
    'privacy.dataCollection.title': 'المعلومات التي نجمعها',
    'privacy.dataCollection.content': 'نجمع الحد الأدنى من المعلومات اللازمة لتقديم خدماتنا، بما في ذلك معلومات الجهاز وتحليلات الاستخدام وأي معلومات تقدمها طوعاً عند الاتصال بالدعم.',
    'privacy.nonCustodial.title': 'الطبيعة غير الاحتجازية',
    'privacy.nonCustodial.content': 'كمحفظة غير احتجازية، ليس لدينا وصول إلى مفاتيحك الخاصة أو عبارات البذور أو الأموال. تبقى معلوماتك الحساسة على جهازك في جميع الأوقات.',
    'privacy.dataUse.title': 'كيف نستخدم معلوماتك',
    'privacy.dataUse.content': 'نستخدم المعلومات المجمعة لتحسين خدماتنا وتقديم دعم العملاء وضمان الأمان والامتثال للالتزامات القانونية. نحن لا نبيع معلوماتك الشخصية لأطراف ثالثة أبداً.',
    'privacy.dataSharing.title': 'مشاركة المعلومات',
    'privacy.dataSharing.content': 'لا نشارك معلوماتك الشخصية مع أطراف ثالثة إلا عند الطلب بموجب القانون أو لحماية حقوقنا وسلامة مستخدمينا.',
    'privacy.security.title': 'أمن البيانات',
    'privacy.security.content': 'ننفذ التدابير التقنية والتنظيمية المناسبة لحماية معلوماتك الشخصية من الوصول غير المصرح به أو التعديل أو الكشف أو التدمير.',
    'privacy.retention.title': 'الاحتفاظ بالبيانات',
    'privacy.retention.content': 'نحتفظ بمعلوماتك فقط للمدة اللازمة لتقديم خدماتنا والامتثال للالتزامات القانونية. يمكنك طلب حذف بيانات حسابك في أي وقت.',
    'privacy.rights.title': 'حقوقك',
    'privacy.rights.content': 'لك الحق في الوصول إلى معلوماتك الشخصية أو تحديثها أو حذفها. يمكنك أيضاً إلغاء الاشتراك في ممارسات جمع البيانات المعينة من خلال إعدادات جهازك.',
    'privacy.cookies.title': 'ملفات تعريف الارتباط والتتبع',
    'privacy.cookies.content': 'نستخدم ملفات تعريف الارتباط والتقنيات المماثلة لتحسين تجربتك وتحليل أنماط الاستخدام. يمكنك التحكم في إعدادات ملفات تعريف الارتباط من خلال متصفحك.',
    'privacy.changes.title': 'التغييرات على هذه السياسة',
    'privacy.changes.content': 'قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنخطرك بأي تغييرات مهمة من خلال نشر السياسة الجديدة على موقعنا الإلكتروني وتحديث تاريخ السريان.',
    'privacy.contact.title': 'اتصل بنا',
    'privacy.contact.content': 'إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى الاتصال بفريق الخصوصية لدينا على <EMAIL>.',

    // Status Page
    'status.title': 'حالة النظام',
    'status.subtitle': 'تحقق من الحالة الحالية لخدمات مخبة واتصالات الشبكة.',
    'status.currentStatus': 'حالة النظام الحالية',
    'status.allOperational': 'جميع الأنظمة تعمل بشكل طبيعي',
    'status.services.api': 'خدمات API',
    'status.services.wallet': 'خدمات المحفظة',
    'status.services.support': 'دعم العملاء',
    'status.services.website': 'الموقع الإلكتروني',
    'status.networkConnections': 'اتصالات الشبكة',
    'status.networks.ethereum': 'شبكة إيثريوم',
    'status.networks.bsc': 'شبكة بينانس الذكية',
    'status.networks.polygon': 'شبكة بوليجون',
    'status.performance.responseTime': 'متوسط وقت الاستجابة',
    'status.performance.uptime': 'وقت التشغيل (30 يوماً)',
    'status.noIncidents': 'لم يتم الإبلاغ عن حوادث حديثة.',
    'status.operational': 'يعمل بشكل طبيعي',
    'status.degraded': 'أداء منخفض',
    'status.maintenance': 'تحت الصيانة',
    'status.outage': 'انقطاع الخدمة',
    'status.excellent': 'ممتاز',
    'status.good': 'جيد',
    'status.fair': 'مقبول',
    'status.poor': 'ضعيف',
    'status.subscribe': 'اشترك في التحديثات',
    'status.subscribeDescription': 'احصل على إشعارات حول تغييرات حالة النظام وجداول الصيانة.',
    'status.emailPlaceholder': 'البريد الإلكتروني',
    'status.walletPlaceholder': 'عنوان المحفظة (اختياري)',
    'status.subscribeButton': 'اشتراك',
    'status.subscribing': 'جارٍ الاشتراك...',
    'status.subscribeSuccess': 'تم الاشتراك بنجاح في تحديثات الحالة! ستتلقى إشعارات حول تغييرات حالة الخدمة.',
    'status.subscribeError': 'فشل في الاشتراك في تحديثات الحالة',
    'status.alreadySubscribed': 'أنت مشترك بالفعل في تحديثات الحالة!',
    'status.networkError': 'خطأ في الشبكة. يرجى المحاولة مرة أخرى.',

    // Blog Page
    'blog.title': 'مدونة',
    'blog.subtitle': 'أخبار العملات الرقمية والدروس والرؤى من فريق مخبة',
    'blog.readMore': 'اقرأ المزيد',
    'blog.read': 'اقرأ',
    'blog.newsletter.title': 'اشترك في نشرتنا الإخبارية',
    'blog.newsletter.description': 'احصل على آخر التحديثات والدروس والرؤى حول العملات الرقمية وتقنية البلوك تشين مباشرة في صندوق الوارد الخاص بك.',
    'blog.newsletter.placeholder': 'عنوان بريدك الإلكتروني',
    'blog.newsletter.subscribe': 'اشتراك',
    'blog.categories.all': 'الكل',
    'blog.categories.announcements': 'الإعلانات',
    'blog.categories.security': 'الأمان',
    'blog.categories.education': 'التعليم',
    'blog.categories.tutorials': 'الدروس',
    'blog.categories.marketInsights': 'رؤى السوق',
    'blog.posts.1.title': 'مقدمة مخبة: أول محفظة عربية للعملات الرقمية',
    'blog.posts.1.excerpt': 'تعرف على مهمتنا لإنشاء محفظة آمنة وسهلة الاستخدام لمنطقة الشرق الأوسط وشمال أفريقيا وما وراءها.',
    'blog.posts.1.author': 'سارة أحمد',
    'blog.posts.2.title': 'أفضل الممارسات الأمنية لمحافظ العملات الرقمية',
    'blog.posts.2.excerpt': 'نصائح أساسية للحفاظ على أصولك الرقمية آمنة من التهديدات والثغرات الشائعة.',
    'blog.posts.2.author': 'عمر فاروق',
    'blog.posts.3.title': 'فهم حلول الطبقة الثانية: Arbitrum و Optimism و Base',
    'blog.posts.3.excerpt': 'دليل شامل لحلول تحسين إيثريوم وكيف تحسن من سرعة المعاملات والتكلفة.',
    'blog.posts.3.author': 'محمد خالد',
    'blog.posts.4.title': 'صعود التمويل اللامركزي في منطقة الشرق الأوسط وشمال أفريقيا',
    'blog.posts.4.excerpt': 'استكشاف الاعتماد المتزايد للتمويل اللامركزي عبر الشرق الأوسط وشمال أفريقيا.',
    'blog.posts.4.author': 'ليلى حسن',
    'blog.posts.5.title': 'كيفية ربط الأصول بين البلوك تشين المختلفة',
    'blog.posts.5.excerpt': 'دليل خطوة بخطوة لنقل أصولك الرقمية عبر الشبكات المختلفة باستخدام مخبة.',
    'blog.posts.5.author': 'محمد خالد',
    'blog.posts.6.title': 'الميزات القادمة: ما هو التالي لمخبة',
    'blog.posts.6.excerpt': 'نظرة خاطفة على الميزات والتحسينات الجديدة والمثيرة القادمة إلى مخبة في الربع الثاني من 2024.',
    'blog.posts.6.author': 'سارة أحمد',

    // Docs Page
    'docs.title': 'الوثائق',
    'docs.subtitle': 'أدلة وموارد شاملة لمساعدتك في الاستفادة القصوى من محفظة مخبة.',
    'docs.gettingStarted': 'البدء',
    'docs.quickStart': 'دليل البدء السريع',
    'docs.installation': 'التثبيت',
    'docs.firstWallet': 'إنشاء محفظتك الأولى',
    'docs.backup': 'النسخ الاحتياطي والاسترداد',
    'docs.walletManagement': 'إدارة المحفظة',
    'docs.importWallet': 'استيراد محفظة موجودة',
    'docs.multipleWallets': 'إدارة محافظ متعددة',
    'docs.walletSecurity': 'أمن المحفظة',
    'docs.transactions': 'المعاملات',
    'docs.sendReceive': 'الإرسال والاستقبال',
    'docs.swapTokens': 'تبديل الرموز',
    'docs.transactionHistory': 'تاريخ المعاملات',
    'docs.networkFees': 'فهم رسوم الشبكة',
    'docs.security': 'الأمان',
    'docs.bestPractices': 'أفضل الممارسات الأمنية',
    'docs.recoveryPhrase': 'أمان عبارة الاسترداد',
    'docs.twoFactor': 'المصادقة ثنائية العوامل',
    'docs.phishingProtection': 'الحماية من التصيد الاحتيالي',
    'docs.advanced': 'الميزات المتقدمة',
    'docs.customNetworks': 'الشبكات المخصصة',
    'docs.dappBrowser': 'متصفح التطبيقات اللامركزية',
    'docs.hardwareWallets': 'تكامل المحافظ الصلبة',
    'docs.troubleshooting': 'استكشاف الأخطاء وإصلاحها',
    'docs.commonIssues': 'المشاكل الشائعة',
    'docs.syncProblems': 'مشاكل المزامنة',
    'docs.transactionFailed': 'المعاملات الفاشلة',
    'docs.contactSupport': 'اتصل بالدعم',
    'docs.apiReference': 'مرجع API',
    'docs.authentication': 'المصادقة',
    'docs.endpoints': 'نقاط النهاية للـ API',
    'docs.rateLimit': 'تحديد المعدل',
    'docs.sdks': 'مكتبات التطوير',

    // Buy/Sell Common
    'trade.selectWallet': 'اختر المحفظة',
    'trade.connectWallet': 'ربط المحفظة',
    'trade.recommendedQuote': 'السعر الموصى به',
    'trade.seeMoreQuotes': 'عرض المزيد من الأسعار',
    'trade.youllReceiveApproximately': 'ستحصل على ما يقارب',
    'trade.mostReliable': 'الأكثر موثوقية',
    'trade.debitOrCredit': 'بطاقة ائتمان أو خصم',
    'trade.bankTransfer': 'حوالة بنكية',
    'trade.saudiRiyal': 'الريال السعودي',
    'trade.ethereum': 'إيثريوم',
    'trade.ethereumMainnet': 'شبكة إيثريوم الرئيسية',

    // Feature Request Page
    'featureRequest.title': 'اقترح ميزة جديدة',
    'featureRequest.subtitle': 'شارك أفكارك واقتراحاتك لتحسين مخبة أو تعاون معنا.',
    'featureRequest.thankYou': 'شكراً لك!',
    'featureRequest.submitted': 'تم تسليم طلب الميزة بنجاح. سنراجع اقتراحك ونتواصل معك قريباً.',
    'featureRequest.submitTitle': 'أرسل طلب ميزة جديدة',
    'featureRequest.name': 'الاسم',
    'featureRequest.namePlaceholder': 'اسمك الكامل',
    'featureRequest.email': 'البريد الإلكتروني',
    'featureRequest.emailPlaceholder': '<EMAIL>',
    'featureRequest.phone': 'رقم الهاتف',
    'featureRequest.phonePlaceholder': '+966 50 123 4567',
    'featureRequest.category': 'نوع الطلب',
    'featureRequest.categoryPlaceholder': 'اختر نوع الطلب',
    'featureRequest.featureTitle': 'عنوان الميزة',
    'featureRequest.titlePlaceholder': 'عنوان مختصر للميزة المقترحة',
    'featureRequest.description': 'الوصف',
    'featureRequest.descriptionPlaceholder': 'اشرح الميزة المقترحة بالتفصيل...',
    'featureRequest.walletAddress': 'عنوان المحفظة',
    'featureRequest.walletAddressPlaceholder': '0x... (اختياري)',
    'featureRequest.submitting': 'جارٍ الإرسال...',
    'featureRequest.submit': 'إرسال الطلب',
    'featureRequest.popular': 'الطلبات الشائعة',
    'featureRequest.categories.feature': 'ميزة جديدة',
    'featureRequest.categories.collaboration': 'تعاون',
    'featureRequest.categories.feedback': 'ملاحظات',
    'featureRequest.categories.bug': 'إبلاغ عن خطأ',
    'featureRequest.categories.investment': 'استثمار',
    'featureRequest.categories.other': 'أخرى',
    'featureRequest.process.title': 'كيف نتعامل مع طلبات الميزات',
    'featureRequest.process.step1': 'التسليم',
    'featureRequest.process.step1Description': 'أرسل طلب الميزة الخاص بك مع جميع التفاصيل اللازمة.',
    'featureRequest.process.step2': 'المراجعة',
    'featureRequest.process.step2Description': 'فريقنا يراجع طلبك ويقيم جدواه وأولويته.',
    'featureRequest.process.step3': 'التطوير',
    'featureRequest.process.step3Description': 'إذا تمت الموافقة، نبدأ في تطوير الميزة وفقاً لخطة المشروع.',
    'featureRequest.process.step4': 'الإطلاق',
    'featureRequest.process.step4Description': 'نطلق الميزة الجديدة ونعلمك بتوفرها.',

    // Card Page
    'card.title': 'بطاقة مخبة الائتمانية',
    'card.subtitle': 'حلول دفع شاملة مع تقنية العملات الرقمية المتطورة',
    'card.description': 'احصل على بطاقة مخبة الائتمانية الثورية التي تدمج العملات الرقمية مع المدفوعات التقليدية بسلاسة تامة',
    'card.contactless': 'دفع لاتلامسي',
    'card.contactlessDesc': 'اضغط للدفع',
    'card.bankLevel': 'مستوى مصرفي',
    'card.secure': 'أمان مصرفي',
    'card.instant': 'تحويل فوري',
    'card.instantDesc': 'تحويلات',
    'card.global': 'قبول عالمي',
    'card.globalDesc': 'قبول',
    'card.peopleWaiting': '1,200+ شخص في الانتظار',

    // Card Form
    'card.form.title': 'انضم لقائمة الانتظار',
    'card.form.submit': 'انضم لقائمة الانتظار',
    'card.form.submitting': 'جارٍ الإرسال...',
    'card.form.success': 'تم إضافتك بنجاح لقائمة الانتظار! سنتواصل معك قريباً.',
    'card.form.error': 'حدث خطأ. يرجى المحاولة مرة أخرى.',
    'card.form.alreadyJoined': 'أنت مضاف بالفعل لقائمة الانتظار!',
    'card.form.fullName': 'الاسم الكامل',
    'card.form.fullNamePlaceholder': 'أدخل اسمك الكامل',
    'card.form.email': 'البريد الإلكتروني',
    'card.form.emailPlaceholder': 'أدخل بريدك الإلكتروني',
    'card.form.phone': 'رقم الهاتف',
    'card.form.phonePlaceholder': 'أدخل رقم هاتفك',
    'card.form.country': 'البلد',
    'card.form.countryPlaceholder': 'أدخل بلدك',
    'card.form.interest': 'الاهتمام الرئيسي',
    'card.form.cardType': 'نوع البطاقة المفضل',
    'card.form.walletAddress': 'عنوان المحفظة',
    'card.form.walletAddressPlaceholder': 'عنوان محفظتك (اختياري)',

    // Card Form Options
    'card.form.onlinePurchases': 'المشتريات عبر الإنترنت',
    'card.form.travelSpending': 'إنفاق السفر',
    'card.form.fiatWithdrawal': 'سحب العملات التقليدية',
    'card.form.cryptoCashback': 'استرداد نقدي بالعملات الرقمية',
    'card.form.defiAccess': 'الوصول للتمويل اللامركزي',
    'card.form.other': 'أخرى',
    'card.form.virtual': 'بطاقة افتراضية',
    'card.form.physical': 'بطاقة فعلية',
    'card.form.both': 'كلاهما',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
  initialLanguage?: Language;
}

export const LanguageProvider = ({ children, initialLanguage = 'en' }: LanguageProviderProps) => {
  const [language, setLanguage] = useState<Language>(initialLanguage);
  const isRTL = language === 'ar';

  // Function to translate text
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;

    // Add or remove RTL-specific classes
    if (isRTL) {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [language, isRTL]);

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isRTL, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
